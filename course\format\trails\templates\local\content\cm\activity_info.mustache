{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/content/cm/activity_info

    Container to display activity information on the course page such as:
      - Activity completion requirements (automatic completion)
      - Manual completion button

    Example context (json):
    {
        "activityname": "Course announcements",
        "hascompletion": true,
        "uservisible": true,
        "isautomatic": true,
        "showmanualcompletion": true,
        "completiondetails": [
             {
                "statuscomplete": 1,
                "description": "Viewed"
            },
            {
                "statusincomplete": 1,
                "description": "Receive a grade"
            }
        ]
    }
}}
<div data-region="activity-information" data-activityname="{{activityname}}" class="activity-information">
    {{#hascompletion}}
        {{#uservisible}}
            <div data-region="completion-info">
                {{#isautomatic}}
                    <div class="automatic-completion-conditions" data-region ="completionrequirements" role="list" aria-label="{{#str}}completionrequirements, core_course, {{activityname}}{{/str}}">
                        {{#completiondetails}}
                            {{$ core_course/completion_automatic }}
                                {{> core_course/completion_automatic }}
                            {{/ core_course/completion_automatic }}
                        {{/completiondetails}}
                    </div>
                {{/isautomatic}}
                {{^isautomatic}}
                    {{#showmanualcompletion}}
                        {{$ core_course/completion_manual }}
                            {{> core_course/completion_manual }}
                        {{/ core_course/completion_manual }}
                    {{/showmanualcompletion}}
                {{/isautomatic}}
            </div>
        {{/uservisible}}
    {{/hascompletion}}
</div>
