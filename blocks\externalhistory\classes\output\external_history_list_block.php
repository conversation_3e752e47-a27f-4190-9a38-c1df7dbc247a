<?php

namespace block_externalhistory\output;

use renderable;
use templatable;
use renderer_base;
use moodle_url;
use block_externalhistory\models\external_history;

class external_history_list_block extends external_history_list {

    protected int $userid;
    /** @var external_history[] */
    protected array $histories;

    public function __construct(int $userid, int $limit = 0) {
        $this->userid = $userid;
        $this->histories = external_history::list_user_histories($userid, $limit);
    }

    /**
     * Export data for the block’s Mustache template.
     *
     * @param renderer_base $output
     * @return array
     */
    public function export_for_template(renderer_base $output): array {
        // Base URLs.
        $headerlink = (new moodle_url('/blocks/externalhistory/index.php'))->out();
        $linkedit   = (new moodle_url('/blocks/externalhistory/edit.php', ['id' => 0]))->out();

        // Map each history record to its card data.
        $cards = array_map(
            fn(external_history $h) => $h->export_for_template($output),
            $this->histories
        );

        return [
            'headerlink' => $headerlink,
            'linkedit'   => $linkedit,
            'cards'      => $cards,
        ];
    }
}
