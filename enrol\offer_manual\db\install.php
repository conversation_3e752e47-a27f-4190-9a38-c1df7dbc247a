<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Install script for Turma manual
 *
 * Documentation: {@link https://moodledev.io/docs/guides/upgrade}
 *
 * @package    enrol_offer_manual
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once($CFG->libdir . '/enrollib.php');


/**
 * Executed on installation of Turma manual
 *
 * @return bool
 */
function xmldb_enrol_offer_manual_install()
{
    $pluginname = 'offer_manual';

    $enabled = \core\plugininfo\enrol::enable_plugin($pluginname, 1);

    if ($enabled) {
        echo "Plugin de inscrição '{$pluginname}' habilitado com sucesso!";
    } else {
        echo "Falha ao habilitar o plugin de inscrição '{$pluginname}'.";
    }

    return $enabled;
}
