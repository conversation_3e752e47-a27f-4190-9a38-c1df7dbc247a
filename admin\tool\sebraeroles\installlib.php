<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

use \tool_lfxp\helpers\user\role;


/**
 * TODO describe file installlib
 *
 * @package    tool_sebraeroles
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Check if the 'admin_sebrae' role exists and create it if not.
 *
 * @return void
 */
function create_admin_sebrae_role()
{
    global $CFG, $DB;

    $shortname = 'admin_sebrae';

    if ($DB->record_exists('role', ['shortname' => $shortname])) {
        mtrace("Role '{$shortname}' already exists. Skipping creation.");
        return;
    }

    $xml_path = $CFG->dirroot . "/admin/tool/sebraeroles/roles/admin_sebrae.xml";

    $roleid = role::create_role_from_xml($xml_path);

    if ($roleid) {
        mtrace("Role '{$shortname}' created successfully with id " . $roleid);
    } else {
        mtrace("Failed to create role '{$shortname}'.");
    }
}