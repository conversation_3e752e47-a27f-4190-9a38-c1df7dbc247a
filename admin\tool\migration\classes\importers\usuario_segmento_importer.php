<?php namespace tool_migration\importers;

use tool_migration\models\usuario_segmento;

require_once($CFG->dirroot . '/user/profile/lib.php');
require_once($CFG->dirroot . '/user/lib.php');

class usuario_segmento_importer extends abstract_importer {

    const AUDIENCE_MEMBER_TABLE = 'local_audience_members';

    public function import_pending(int $limit = 0) : void {
        foreach (usuario_segmento::get_pending_imports($limit) as $usuario_segmento) {
            try {
                $this->upsert_audience_member($usuario_segmento);
            } catch (\Throwable $th) {
                $codaluno = $usuario_segmento->get('source_codaluno');
                $codsegmento = $usuario_segmento->get('source_codsegmento');
                $this->output("Erro ao importar usuario \"$codaluno\" para o segmento \"$codsegmento\": " .$th->getMessage(), 1);
            }
        }
    }

    public function update_pending(int $limit = 0) : void {
        foreach (usuario_segmento::get_pending_updates($limit) as $usuario_segmento) {
            try {
                $this->upsert_audience_member($usuario_segmento);
            } catch (\Throwable $th) {
                $codaluno = $usuario_segmento->get('source_codaluno');
                $codsegmento = $usuario_segmento->get('source_codsegmento');
                $this->output("Erro ao atualizar usuario \"$codaluno\" no segmento \"$codsegmento\": " .$th->getMessage(), 1);
            }
        }
    }

    protected function upsert_audience_member(usuario_segmento $usuario_segmento){
        global $DB;
        $audience_member = $usuario_segmento->to_audience_member();

        if(empty($audience->id)){
            $audience_member->id = $DB->insert_record(self::AUDIENCE_MEMBER_TABLE, $audience_member);
            $usuario_segmento->mark_as_imported($audience_member->id);
            $this->output("Usuário $audience_member->userid adicionado no publico-alvo $audience_member->audienceid adicionado", 1);
            return;
        }

        $DB->update_record(self::AUDIENCE_MEMBER_TABLE, $audience_member);
        $usuario_segmento->mark_as_updated();
        $this->output("Usuário $audience_member->userid atualizado no publico-alvo $audience_member->audienceid adicionado", 1);
    }
}
