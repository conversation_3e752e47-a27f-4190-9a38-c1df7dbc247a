.filters-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    margin-bottom: 1rem;
}
 
.filters-container .filter-group {
    flex: 0 1 auto;
}
 
.filters-container .input-group {
    width: 250px;
}
 
/* Limita o título do card em 2 linhas com reticências */
.activityname {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2em;
    max-height: 2.4em;
    margin-bottom: 0;
    padding-right: 24px; /* Espaço para o ícone do lápis */
    position: relative; /* Para posicionamento do ícone */
}

/* Ajusta o ícone de edição */
.activityname .quickeditlink {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Garante que o container do título mantenha a altura correta */
.activity-instance {
    min-height: 3em;
    width: 100%; /* Garante que ocupe toda a largura disponível */
}

/* Ajusta o alinhamento e garante que o texto não quebre o layout */
.activitytitle {
    display: flex;
    align-items: flex-start;
    width: 100%; /* Garante que ocupe toda a largura disponível */
}

/* Garante que o texto dentro do título não quebre o layout */
.activitytitle .media-body {
    min-width: 0;
    flex: 1;
    width: 100%; /* Garante que ocupe toda a largura disponível */
}

/* Garante que o link dentro do título respeite o truncamento */
.activityname a,
.activityname span,
.activityname div {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%; /* Garante que ocupe toda a largura disponível */
}

#page-course-view-gallery .course-tabs #courseTabs .nav-item a{
    color: #fff !important;
}

.editing .content.course-content-item-content.collapse.show {
    margin-top: 0px !important;
}

/* Ajuste para telas menores */
@media (max-width: 768px) {
    .filters-container {
        flex-direction: column;
        align-items: stretch;
    }
 
    .filters-container .input-group {
        width: 100%;
    }
}
 
/* Estilos para as abas */
.course-tabs .nav-tabs {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 1.5rem;
}
 
.course-tabs .nav-item {
    margin-bottom: -1px;
}
 
.course-tabs .nav-tabs li:first-child .nav-link {
    font-weight: normal;
}
 
.course-tabs .nav-link {
    color: #495057;
    border: none !important;
    border-bottom: 3px solid transparent !important;
    padding: 0.75rem 1.25rem;
    font-weight: normal;
    transition: all 0.2s ease-in-out;
    background: transparent !important;
}
 
.course-tabs .nav-link:hover {
    color: #1177d1;
    border-bottom: 3px solid rgba(17, 119, 209, 0.5) !important;
}
 
.course-tabs .nav-link.active {
    color: #1177d1;
    background-color: transparent;
    border-bottom: 3px solid #1177d1 !important;
}
 
/* Remover estilos padrão do Bootstrap */
.nav-tabs .nav-link {
    border: none !important;
    background: transparent !important;
}
 
.nav-tabs .nav-link.active {
    border: none !important;
    border-bottom: 3px solid #1177d1 !important;
    background: transparent !important;
}
 
/* Responsividade */
@media (max-width: 768px) {
    .course-tabs .nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
   
    .course-tabs .nav-link {
        white-space: nowrap;
        padding: 0.5rem 1rem;
    }
}
 
.section-pagination {
    margin: 2rem 0;
    display: none; /* Inicialmente oculto, só mostra quando necessário */
}

.section-pagination:not(.d-none) {
    margin-top: 3rem !important;
}

.section-pagination .pagination {
    margin-bottom: 0;
}
 
.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
    justify-content: center;
    flex-wrap: wrap;
}
 
.page-item {
    margin: 0;
}
 
.page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    line-height: 1.25;
    color: #1177d1;
    background-color: #fff;
    border: 1px solid #dee2e6;
    text-decoration: none;
    min-width: 2.5rem;
    text-align: center;
}
 
.page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: #1177d1;
    border-color: #1177d1;
}
 
.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: not-allowed;
    background-color: #fff;
    border-color: #dee2e6;
}
 
.page-link:hover {
    z-index: 2;
    color: #0056b3;
    text-decoration: none;
    background-color: #e9ecef;
    border-color: #dee2e6;
}
 
.page-numbers {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}
 
/* Ajustes para mobile */
@media (max-width: 576px) {
 
    .page-link {
        padding: 0.4rem 0.6rem;
        min-width: 2.2rem;
    }
}
 
/* Estilos para a aba todos */
#todos .section-content {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}
 
#todos .activity {
    margin-bottom: 1rem;
}
 
/* Remove bordas e espaçamentos desnecessários na aba todos */
#todos .section {
    border: none;
    margin: 0;
    padding: 0;
}
 
#todos .course-section-header {
    display: none;
}
 