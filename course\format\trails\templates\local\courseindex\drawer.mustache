{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/courseindex/drawer

    This template renders the course index drawer with the placeholder.

    The code from this file is just an stub as the final code will come from
    the new layouts for Moodle 4.0.

    Example context (json):
    {}
}}
<nav id="courseindex" class="courseindex">
    <div id="courseindex-content">
        {{> format_trails/local/courseindex/placeholders }}
    </div>
</nav>
{{#js}}
require(['core_courseformat/local/courseindex/drawer'], function(component) {
    component.init('courseindex');
});
{{/js}}
