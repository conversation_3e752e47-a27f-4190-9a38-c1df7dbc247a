<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Trails course format. Display the whole course as "trails" made of modules.
 *
 * @package format_trails
 * @copyright 2006 The Open University
 * <AUTHOR> and others.
 * @license http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir.'/filelib.php');
require_once($CFG->libdir.'/completionlib.php');

// Horrible backwards compatible parameter aliasing.
if ($season = optional_param('season', 0, PARAM_INT)) {
    $url = $PAGE->url;
    $url->param('section', $season);
    debugging('Outdated season param passed to course/view.php', DEBUG_DEVELOPER);
    redirect($url);
}
// End backwards-compatible aliasing.

// Retrieve course format option fields and add them to the $course object.
$format = course_get_format($course);
$course = $format->get_course();
$context = context_course::instance($course->id);

if (($marker >= 0) && has_capability('moodle/course:setcurrentsection', $context) && confirm_sesskey()) {
    $course->marker = $marker;
    course_set_marker($course->id, $marker);
}

// Make sure section 0 is created.
course_create_sections_if_missing($course, 0);

$renderer = $PAGE->get_renderer('format_trails');

if (!empty($displaysection)) {
    $format->set_section_number($displaysection);
}
$outputclass = $format->get_output_classname('content');
$widget = new $outputclass($format);

echo $renderer->render($widget);

// Include course format js module.
$PAGE->requires->js('/course/format/trails/format.js');

// Acessibility(NVDA) to course cards

echo '
    <script defer>
        setTimeout(function () {
            // Seleciona todos os elementos ".instancename" dentro de tags "a" e suas tags "a" parentes
            const links = document.querySelectorAll("a .instancename");
            
            links.forEach(function (span) {
                // Acessa o elemento "a" pai
                const link = span.closest("a");
                
                if (link) { 
                    // Cria uma nova "div" com as mesmas classes do "a" original
                    const newDiv = document.createElement("div");
                    newDiv.className = link.className;
                    
                    // Move o conteúdo para a nova "div"
                    while (link.firstChild) {
                        newDiv.appendChild(link.firstChild);
                    }
                    
                    // Substitui o "a" pela "div" no DOM
                    link.parentNode.replaceChild(newDiv, link);
                } else {
                    console.warn("Elemento <a> pai não encontrado para .instancename.");
                }
            });

            // Agora, para adicionar o "aria-label" e o estilo z-index nos elementos "a"
            const playButtons = document.querySelectorAll(".activity-item > a");

            playButtons.forEach(function (button) {
                const activityItem = button.closest(".activity-item");
                
                if (activityItem) {
                    const instancename = activityItem.querySelector(".instancename");
                    
                    if (instancename) { // Verifica se o instancename existe
                        const instancenameText = instancename.textContent.trim();
                        
                        // Adiciona o "aria-label" ao botão de play
                        button.setAttribute("aria-label", instancenameText);
                        
                        // Adiciona o estilo z-index: 10
                        button.style.zIndex = "10";
                    } else {
                        console.warn(".instancename não encontrado dentro de .activity-item.");
                    }
                } else {
                    console.warn(".activity-item pai não encontrado para o botão de play.");
                }
            });

            // Código adicional para manipular o tabindex e a navegação por TAB
            (function () {
                // Seleciona o drawer pelo ID
                const drawer = document.getElementById("theme_smart-drawers-courseindex");

                if (drawer) {
                    // Função para verificar se um elemento <a> é clicável
                    function isClickableLink(element) {
                        return element.tagName.toLowerCase() === "a" && element.hasAttribute("href") && element.getAttribute("href") !== "#";
                    }

                    // Função para atualizar o tabindex com base no estado do drawer
                    function updateTabIndex() {
                        const isOpen = drawer.classList.contains("show");

                        // Seleciona os elementos que devem ser tabuláveis
                        const courseItems = drawer.querySelectorAll(".courseindex-item.courseindex-item-cm");
                        const chevronItems = drawer.querySelectorAll(".courseindex-chevron.icons-collapse-expand");

                        if (isOpen) {
                            // Para cada item do curso
                            courseItems.forEach(function(item) {
                                // Define tabindex="0" para o item pai
                                item.setAttribute("tabindex", "0");

                                // Remove tabindex dos filhos que são interativos, exceto links clicáveis
                                const focusableChildren = item.querySelectorAll("a, button, [tabindex]");
                                focusableChildren.forEach(function(child) {
                                    if (child !== item) {
                                        if (isClickableLink(child)) {
                                            // Se for um link clicável, mantém o tabindex
                                            child.setAttribute("tabindex", "0");
                                        } else {
                                            // Remove o tabindex de elementos não clicáveis
                                            child.removeAttribute("tabindex");
                                        }
                                    }
                                });
                            });

                            // Para cada item de chevron
                            chevronItems.forEach(function(item) {
                                // Define tabindex="0" para o elemento chevron
                                item.setAttribute("tabindex", "0");

                                // Remove tabindex dos filhos que são interativos, exceto links clicáveis
                                const focusableChildren = item.querySelectorAll("a, button, [tabindex]");
                                focusableChildren.forEach(function(child) {
                                    if (child !== item) {
                                        if (isClickableLink(child)) {
                                            // Se for um link clicável, mantém o tabindex
                                            child.setAttribute("tabindex", "0");
                                        } else {
                                            // Remove o tabindex de elementos não clicáveis
                                            child.removeAttribute("tabindex");
                                        }
                                    }
                                });
                            });
                        } else {
                            // Se o drawer estiver fechado, define tabindex="-1" para todos os elementos interativos dentro dele
                            const allFocusableElements = drawer.querySelectorAll("a, button, [tabindex]");
                            allFocusableElements.forEach(function(element) {
                                element.setAttribute("tabindex", "-1");
                            });
                        }
                    }

                    // Adicionar contador e limite de tentativas
                    let mutationAttempts = 0;
                    const maxAttempts = 10;

                    // Observa mudanças na classe do drawer para detectar abertura/fechamento
                    const observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.attributeName === "class") {
                                updateTabIndex();
                                mutationAttempts++;
                                if (mutationAttempts >= maxAttempts) {
                                    observer.disconnect();
                                    console.warn("Limite de tentativas da MutationObserver alcançado e desconectado.");
                                }
                            }
                        });
                    });

                    // Inicia a observação do elemento drawer
                    observer.observe(drawer, { attributes: true });

                    // Chamada inicial para configurar o tabindex corretamente
                    updateTabIndex();
                } else {
                    console.warn("Elemento com ID \'theme_smart-drawers-courseindex\' não encontrado.");
                }
            })();

            // Código adicional para o menu lateral com validações
            (function () {
                // Seleciona o botão que abre o menu
                const openButton = document.querySelector("#topofscroll .btn[data-toggler=\'drawers\']");

                // Verifica se o botão existe antes de prosseguir
                if (!openButton) {
                    console.warn("Botão de abertura do menu não encontrado.");
                    return;
                }

                // Cria dinamicamente a região de alerta para o NVDA
                const liveRegion = document.createElement("div");
                liveRegion.setAttribute("aria-live", "polite");
                liveRegion.classList.add("sr-only");
                document.body.appendChild(liveRegion);

                // Adiciona os atributos necessários ao botão
                openButton.setAttribute("aria-expanded", "false");

                // Função para adicionar e remover texto da live region
                function updateLiveRegionMessage(message) {
                    if (liveRegion) { // Verifica se liveRegion foi criado
                        // Adiciona o texto à live region
                        liveRegion.textContent = message;

                        // Remove o texto após um curto intervalo
                        setTimeout(() => {
                            liveRegion.textContent = "";
                        }, 1000); // 1 segundo
                    } else {
                        console.warn("liveRegion não está disponível.");
                    }
                }

                // Adiciona o evento de clique ao botão de abertura
                openButton.addEventListener("click", () => {
                    // Atualiza o atributo `aria-expanded` para `true`
                    openButton.setAttribute("aria-expanded", "true");

                    // Atualiza a live region com a mensagem de abertura
                    updateLiveRegionMessage("Menu lateral aberto");
                });
            })();
        }, 5000);
    </script>
';
