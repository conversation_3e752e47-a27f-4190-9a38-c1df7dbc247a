<?php

require_once(__DIR__ . '/../../../config.php');
require_once($CFG->libdir . '/adminlib.php');
require_once($CFG->dirroot . '/' . $CFG->admin . '/tool/companymanagement/locallib.php');

use tool_companymanagement\repositories\company_repository;
use tool_companymanagement\services\company_service;

require_login();

$sitecontext = \context_system::instance();
$url = "/admin/tool/companymanagement/index.php";
$title = get_string('pluginname', 'tool_companymanagement');

require_capability('tool/companymanagement:view', $sitecontext);

if ($delete = optional_param("delete", null, PARAM_INT)) {
	$id = $delete;

	$repository = new  company_repository;
	$company = $repository->find($id);

	$service = new company_service($repository);
	$service->delete($id);

	$params = [
		'page' => optional_param('page', 0, PARAM_INT),
		'perpage' => optional_param('perpage', 30, PARAM_INT),
		'sort' => optional_param('sort', 'firstname', PARAM_RAW),
		'dir' => optional_param('dir', 'ASC', PARAM_ALPHA),
		'search' => optional_param('search', '', PARAM_TEXT),
	];

	$url = new moodle_url($url, $params);

	redirect($url, get_string('message:company_deleted', 'tool_companymanagement', $company->get('name')));
	exit;
}

$PAGE->set_context($sitecontext);
$PAGE->set_url($url);
$PAGE->set_pagelayout('standard');
$PAGE->set_title($title);
$PAGE->set_heading($title);

$PAGE->navbar->add($title, $url);

$renderer = $PAGE->get_renderer('tool_companymanagement');

echo $OUTPUT->header();
echo $OUTPUT->heading($title);
echo $renderer->output();
echo $OUTPUT->footer();
