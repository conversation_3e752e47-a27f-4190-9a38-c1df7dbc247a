USE [dbUcSebraeFull];
GO

SELECT
    A.Id                         AS source_id,
    A.Name                       AS source_name,
    A.Description                AS source_description,
    A.ImageUrl                   AS source_imageurl,
    A.Active                     AS source_active,
    A.ActivityGroupId            AS source_activitygroupid,
    A.Keywords                   AS source_keywords,
    A.UpdateDate                 AS source_updatedate,
    A.RegistrationDate           AS source_registrationdate,

    (SELECT AF.Description FROM [dbUcSebraeFull].[dbo.LMS].[TRN_ActivityFeatures] AF WHERE AF.ActivityId = A.Id AND AF.FeatureId = 7)  AS source_apresentacao,
    (SELECT AF.Description FROM [dbUcSebraeFull].[dbo.LMS].[TRN_ActivityFeatures] AF WHERE AF.ActivityId = A.Id AND AF.FeatureId = 8)  AS source_objetivos,
    (SELECT AF.Description FROM [dbUcSebraeFull].[dbo.LMS].[TRN_ActivityFeatures] AF WHERE AF.ActivityId = A.Id AND AF.FeatureId = 9)  AS source_conteudo_programatico,
    (SELECT AF.Description FROM [dbUcSebraeFull].[dbo.LMS].[TRN_ActivityFeatures] AF WHERE AF.ActivityId = A.Id AND AF.FeatureId = 10) AS source_nivel_complexidade,
    (SELECT AF.Description FROM [dbUcSebraeFull].[dbo.LMS].[TRN_ActivityFeatures] AF WHERE AF.ActivityId = A.Id AND AF.FeatureId = 11) AS source_termo_aceite,
    (SELECT AF.Description FROM [dbUcSebraeFull].[dbo.LMS].[TRN_ActivityFeatures] AF WHERE AF.ActivityId = A.Id AND AF.FeatureId = 12) AS source_ficha_tecnica,
    (SELECT AF.Description FROM [dbUcSebraeFull].[dbo.LMS].[TRN_ActivityFeatures] AF WHERE AF.ActivityId = A.Id AND AF.FeatureId = 13) AS source_requisitos,
    (SELECT AF.Description FROM [dbUcSebraeFull].[dbo.LMS].[TRN_ActivityFeatures] AF WHERE AF.ActivityId = A.Id AND AF.FeatureId = 14) AS source_criterios_avaliacao,
    (SELECT AF.Description FROM [dbUcSebraeFull].[dbo.LMS].[TRN_ActivityFeatures] AF WHERE AF.ActivityId = A.Id AND AF.FeatureId = 15) AS source_publico,
    (SELECT AF.Description FROM [dbUcSebraeFull].[dbo.LMS].[TRN_ActivityFeatures] AF WHERE AF.ActivityId = A.Id AND AF.FeatureId = 16) AS source_carga_horaria,
    (SELECT AF.Description FROM [dbUcSebraeFull].[dbo.LMS].[TRN_ActivityFeatures] AF WHERE AF.ActivityId = A.Id AND AF.FeatureId = 17) AS source_area_subarea

FROM [dbUcSebraeFull].[dbo.LMS].[TRN_Activities] A
ORDER BY
    A.Name;
GO
