{"version": 3, "file": "section.min.js", "sources": ["../src/section.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Format trails section extra logic component.\n *\n * @module     format_trails/section\n * @copyright  2022 Ferran <PERSON>cio <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {BaseComponent} from 'core/reactive';\nimport {getCurrentCourseEditor} from 'core_courseformat/courseeditor';\nimport Templates from 'core/templates';\n\nclass HighlightSection extends BaseComponent {\n\n    /**\n     * Constructor hook.\n     */\n    create() {\n        // Optional component name for debugging.\n        this.name = 'format_trails_section';\n        // Default query selectors.\n        this.selectors = {\n            SECTION: `[data-for='section']`,\n            SETMARKER: `[data-action=\"sectionHighlight\"]`,\n            REMOVEMARKER: `[data-action=\"sectionUnhighlight\"]`,\n            ACTIONTEXT: `.menu-action-text`,\n            ICON: `.icon`,\n        };\n        // Default classes to toggle on refresh.\n        this.classes = {\n            HIDE: 'd-none',\n        };\n        // The trails format section specific actions.\n        this.formatActions = {\n            HIGHLIGHT: 'sectionHighlight',\n            UNHIGHLIGHT: 'sectionUnhighlight',\n        };\n    }\n\n    /**\n     * Component watchers.\n     *\n     * @returns {Array} of watchers\n     */\n    getWatchers() {\n        return [\n            {watch: `section.current:updated`, handler: this._refreshHighlight},\n        ];\n    }\n\n    /**\n     * Update a content section using the state information.\n     *\n     * @param {object} param\n     * @param {Object} param.element details the update details.\n     */\n    async _refreshHighlight({element}) {\n        let selector;\n        let newAction;\n        if (element.current) {\n            selector = this.selectors.SETMARKER;\n            newAction = this.formatActions.UNHIGHLIGHT;\n        } else {\n            selector = this.selectors.REMOVEMARKER;\n            newAction = this.formatActions.HIGHLIGHT;\n        }\n        // Find the affected action.\n        const affectedAction = this.getElement(`${this.selectors.SECTION} ${selector}`, element.id);\n        if (!affectedAction) {\n            return;\n        }\n        // Change action, text and icon.\n        affectedAction.dataset.action = newAction;\n        const actionText = affectedAction.querySelector(this.selectors.ACTIONTEXT);\n        if (affectedAction.dataset?.swapname && actionText) {\n            const oldText = actionText?.innerText;\n            actionText.innerText = affectedAction.dataset.swapname;\n            affectedAction.dataset.swapname = oldText;\n        }\n        const icon = affectedAction.querySelector(this.selectors.ICON);\n        if (affectedAction.dataset?.swapicon && icon) {\n            const newIcon = affectedAction.dataset.swapicon;\n            if (newIcon) {\n                const pixHtml = await Templates.renderPix(newIcon, 'core');\n                Templates.replaceNode(icon, pixHtml, '');\n            }\n        }\n    }\n}\n\nexport const init = () => {\n    // Add component to the section.\n    const courseEditor = getCurrentCourseEditor();\n    if (courseEditor.supportComponents && courseEditor.isEditing) {\n        new HighlightSection({\n            element: document.getElementById('region-main'),\n            reactive: courseEditor,\n        });\n    }\n};\n"], "names": ["HighlightSection", "BaseComponent", "create", "name", "selectors", "SECTION", "SETMARKER", "REMOVEMARKER", "ACTIONTEXT", "ICON", "classes", "HIDE", "formatActions", "HIGHLIGHT", "UNHIGHLIGHT", "getWatchers", "watch", "handler", "this", "_refreshHighlight", "selector", "newAction", "element", "current", "affectedAction", "getElement", "id", "dataset", "action", "actionText", "querySelector", "swapname", "oldText", "innerText", "icon", "swapicon", "newIcon", "pixHtml", "Templates", "renderPix", "replaceNode", "courseEditor", "supportComponents", "isEditing", "document", "getElementById", "reactive"], "mappings": ";;;;;;;sJA2BMA,yBAAyBC,wBAK3BC,cAESC,KAAO,8BAEPC,UAAY,CACbC,+BACAC,6CACAC,kDACAC,+BACAC,mBAGCC,QAAU,CACXC,KAAM,eAGLC,cAAgB,CACjBC,UAAW,mBACXC,YAAa,sBASrBC,oBACW,CACH,CAACC,gCAAkCC,QAASC,KAAKC,uGAWjDC,SACAC,WAFgBC,QAACA,cAGjBA,QAAQC,SACRH,SAAWF,KAAKd,UAAUE,UAC1Be,UAAYH,KAAKN,cAAcE,cAE/BM,SAAWF,KAAKd,UAAUG,aAC1Bc,UAAYH,KAAKN,cAAcC,iBAG7BW,eAAiBN,KAAKO,qBAAcP,KAAKd,UAAUC,oBAAWe,UAAYE,QAAQI,QACnFF,sBAILA,eAAeG,QAAQC,OAASP,gBAC1BQ,WAAaL,eAAeM,cAAcZ,KAAKd,UAAUI,6CAC3DgB,eAAeG,gEAASI,UAAYF,WAAY,OAC1CG,QAAUH,wBAAAA,WAAYI,UAC5BJ,WAAWI,UAAYT,eAAeG,QAAQI,SAC9CP,eAAeG,QAAQI,SAAWC,cAEhCE,KAAOV,eAAeM,cAAcZ,KAAKd,UAAUK,wCACrDe,eAAeG,kEAASQ,UAAYD,KAAM,OACpCE,QAAUZ,eAAeG,QAAQQ,YACnCC,QAAS,OACHC,cAAgBC,mBAAUC,UAAUH,QAAS,2BACzCI,YAAYN,KAAMG,QAAS,qBAMjC,WAEVI,cAAe,0CACjBA,aAAaC,mBAAqBD,aAAaE,eAC3C3C,iBAAiB,CACjBsB,QAASsB,SAASC,eAAe,eACjCC,SAAUL"}