<template>
  <div v-if="totalPages > 1" class="d-flex justify-content-center">
    <nav class="mt-5 pagination" aria-label="Page navigation">
      <ul class="pagination justify-content-center">
        <!-- Bo<PERSON><PERSON> Anterior -->
        <li @click="previousPage" class="page-item page-item-previous" :class="{
          'disabled': !canCallPreviousPage
        }" title="Página anterior" data-page-number="">
          <button class="page-link prev-page" :disabled="!canCallPreviousPage">
            <span aria-hidden="true"><i
                class="icon fa fa-angle-left m-0 d-flex justify-content-center align-items-center"></i></span>
          </button>
        </li>

        <!-- Primeira Página -->
        <li class="page-item" :title="'Página 1'" @click="changePage(1)">
          <button :class="{
            'page-link': true,
            'page-link': currentPage !== 1,
            'btn-primary active page-link': currentPage === 1
          }">
            1
          </button>
        </li>

        <!-- Elipsis inicial (se necessário) -->
        <li v-if="showStartEllipsis" class="page-item disabled">
          <button class="page-link disabled">...</button>
        </li>

        <!-- Páginas do meio -->
        <li v-for="pageNumber in middlePagination" class="page-item" :key="pageNumber" :title="'Página ' + pageNumber"
          @click="changePage(pageNumber)">
          <button :class="{
            'page-link': true,
            'page-link': pageNumber !== currentPage,
            'btn-primary active page-link': pageNumber === currentPage
          }">
            {{ pageNumber }}
          </button>
        </li>

        <!-- Elipsis final (se necessário) -->
        <li v-if="showEndEllipsis" class="page-item disabled">
          <button class="page-link disabled">...</button>
        </li>

        <!-- Última Página (se diferente da primeira) -->
        <li v-if="totalPages > 1" class="page-item" :title="'Página ' + totalPages" @click="changePage(totalPages)">
          <button :class="{
            'page-link': true,
            'page-link': currentPage !== totalPages,
            'btn-primary active page-link': currentPage === totalPages
          }">
            {{ totalPages }}
          </button>
        </li>

        <!-- Botão Próximo -->
        <li @click="nextPage" class="page-item page-item-next" :class="{
          'disabled': !canCallNextPage
        }" data-page-number="" title="Próxima página">
          <button class="page-link next-page" :disabled="!canCallNextPage">
            <span aria-hidden="true"><i
                class="icon fa fa-angle-right m-0 d-flex justify-content-center align-items-center"></i></span>
          </button>
        </li>
      </ul>
    </nav>
  </div>
</template>

<script>
export default {
  name: 'SimplePagination',

  emits: ['page-changed'],

  props: {
    currentPage: {
      type: Number,
      required: true
    },
    totalPages: {
      type: Number,
      required: true
    },
    maxVisiblePages: {
      type: Number,
      default: 3 // Número de páginas visíveis ao redor de cada lado da página atual
    }
  },

  computed: {
    canCallPreviousPage() {
      return this.currentPage > 1
    },

    canCallNextPage() {
      return this.currentPage < this.totalPages
    },

    showStartEllipsis() {
      if (this.totalPages <= 5) return false;
      return (!this.isMobile() && this.currentPage > 5)
        || (this.isMobile() && this.currentPage > 3);
    },

    showEndEllipsis() {
      if (this.totalPages <= 5) return false;
      return (!this.isMobile() && this.currentPage <= this.totalPages - 5)
        || (this.isMobile() && this.currentPage <= this.totalPages - 3);
    },

    middlePagination() {
      if (this.totalPages == 0) return [];
      let treshold = 1;
      const pages = [];
      if (this.totalPages <= 5) {
        for (let i = 2; i <= this.totalPages - 1; i++) {
          pages.push(i);
        }
      } else {

        if ((this.currentPage <= 5 && !this.isMobile()) || (this.currentPage <= 3 && this.isMobile())) {
          //primeiras páginas
          if (this.currentPage <= this.totalPages - 5) {
            treshold = 2;
          }

          if (!this.isMobile()) {
            for (let i = 2; i <= this.currentPage + 3 && i <= this.totalPages - treshold; i++) {
              pages.push(i);
            }
          }
          else {
            for (let i = 2; i <= 3; i++) {
              pages.push(i);
            }
          }
        }
        else if ((this.currentPage > this.totalPages - 5 && !this.isMobile()) || (this.currentPage > this.totalPages - 3 && this.isMobile())) {
          // últimas páginas
          treshold = 2;
          if (this.isMobile()) {
            if (this.currentPage > 3) {
              treshold = this.totalPages - 2;
            }
          }
          else if (this.currentPage >= 6) {
            treshold = this.currentPage - 3;
          }
          for (let i = treshold; i <= this.totalPages - 1; i++) {
            pages.push(i);
          }
        } else {
          // meio
          if (this.isMobile()) {
            pages.push(this.currentPage);
          }
          else {
            for (let i = this.currentPage - 3; i <= this.currentPage + 3; i++) {
              pages.push(i);
            }
          }

        }
      }
      return pages;
    }
  },

  methods: {
    changePage(pageNumber) {
      if (this.currentPage !== pageNumber) {
        this.$emit('page-changed', pageNumber)
      }
    },

    previousPage() {
      if (this.canCallPreviousPage) {
        this.changePage(this.currentPage - 1)
      }
    },

    nextPage() {
      if (this.canCallNextPage) {
        this.changePage(this.currentPage + 1)
      }
    },

    isMobile() {
      return window.innerWidth < 601
    }
  }
}
</script>

<style lang="scss" scoped>
.page-item:not(.disabled):not(.active) {
  cursor: pointer;

  button {
    [data-theme="dark"] {
      background-color: #41474F !important;
    }

    [data-theme="light"] {
      background-color: #D3D9DF !important;
    }
  }
}

.page-item-previous {
  margin-right: 12px;
}

.page-item-next {
  margin-left: 12px;
}

.pagination {
  height: 38px;
}

.page-item>button {
  width: 38px;
  height: 38px;
  flex-grow: 0;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
}

.btn-primary.active {
  color: white !important;
}

.page-item:first-child .page-link,
.page-item:last-child .page-link {
  border-radius: 6px;
}

.page-item:nth-child(2) .page-link {
  margin-left: 0;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.page-item:nth-last-child(2) .page-link {
  margin-left: 0;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.page-item.disabled .page-link {
  background-color: #343a40 !important;
  color: #7b7f83 !important;
}
</style>