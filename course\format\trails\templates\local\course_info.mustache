{{!
	{
	  "id": "",
	  "fullnamedisplay": "",
	  "viewurl": "",
	  "image": "",
	  "summary": "",
	  "startdate": "",
	  "progress": "",
	  "hasprogress": "",
	  "isfavourite": "",
	  "hidden": "",
	  "showshortname": "",
	  "coursecategory": "",
	  "totalsections": "",
	  "hasteacher": "",
	  "hasmanyteachers": "",
	  "teachers": [
		{
			"id":0,
			"name": "string"
	  ],
	  "customfields": {
			"modality": "EAD",
			"level": 3,
			"workload": "80h"
		}
	}
}}

<div class="course-info mb-5">
	<div class="courseimage overflow-hidden">
		<img src="{{image}}" {{#coverposition}}style="object-position:{{.}}"{{/coverposition}}>
	</div>
	
	<div class="courseinfo-box">
		<div class="courselogo">
			{{#haslogo}}
				<img src="{{logo}}" alt="{{fullnamedisplay}}" />
			{{/haslogo}}
			
			{{^haslogo}}
				<p class="display-3 font-weight-bold text-uppercase mb-0">{{fullnamedisplay}}</p>
			{{/haslogo}}
		</div>
		
		<h4 class="h4 mr-3 mt-3 text-uppercase d-flex align-items-center">
			<i class="fa fa-tags mr-2"></i> 
			<span class="mr-3">{{coursecategory}}</span>

			{{#customfields.modality}}
				<span class="badge badge-primary rounded-sm pt-2 mr-3">{{.}}</span>
			{{/customfields.modality}}

			{{#gamification}}
				<div class="badge badge-warning rounded-sm mr-3">
					<i class="fa-solid fa-trophy"></i> {{points}}
				</div>
			{{/gamification}}
		</h4>
		
		<div class="d-flex align-items-center justify-content-center justify-content-md-start h5 mt-3 flex-wrap">
			{{#hasteacher}}
			<div class="mr-3">
				{{#hasmanyteachers}}
					<div class="dropdown">
						<a href="#" class="dropdown-toggle teacher-link" data-toggle="dropdown"><i class="fa fa-user pr-1"></i> Professores</a>
						
						<div class="dropdown-menu">
							{{#teachers}}
								<a class="dropdown-item" href="{{config.wwwroot}}/user/profile.php?id={{id}}">{{name}}</a>
							{{/teachers}}
						</div>
					</div>
				{{/hasmanyteachers}}
				
				{{^hasmanyteachers}}
					<a class="teacher-link" href="{{config.wwwroot}}/user/profile.php?id={{teachers.0.id}}"><i class="fa fa-user pr-1"></i> {{teachers.0.name}}</a>
				{{/hasmanyteachers}}
			</div>
			{{/hasteacher}}
			
			{{#customfields.workload}}
			<div class="mr-3">
				<i class="fa-regular fa-clock pr-1"></i> {{.}}
			</div>
			{{/customfields.workload}}
			
			{{#totalsections}}
			<div class="mr-3">
				<i class="fa-regular fa-folder-open pr-1 mt-2 mt-md-0"></i> {{.}} {{sectionname}}
			</div>
			{{/totalsections}}
			
			{{#startdate}}
			<div class="mr-3">
				<i class="fa-regular fa-calendar pr-1"></i> {{.}}
			</div>
			{{/startdate}}

			
		</div>
		
		<div class="actions d-flex align-items-center mt-3 font-weight-bold">
			<!--<div class="courseinfo-ranking text-warning mr-3">
				<i class="fa fa-star"></i>
				<i class="fa fa-star"></i>
				<i class="fa fa-star"></i>
				<i class="fa fa-star"></i>
				<i class="fa fa-star-half"></i>
				<span class="text-white">4.5</span>
			</div>
			
			<div class="custom-link mr-3">
				<a href="#"><i class="fa fa-thumbs-up pr-1"></i> Avaliar</a>
			</div>
			-->
			<div class="custom-link">
				<a href="#" class="toggle-favourite" data-status="{{isfavourite}}">
					<i class="fa{{^isfavourite}}-regular{{/isfavourite}} fa-heart pr-1 theicon"></i> 
					<span class="favtext {{#isfavourite}}d-none{{/isfavourite}}">Favoritar</span>
					<span class="unfavtext {{^isfavourite}}d-none{{/isfavourite}}">Desfavoritar</span>
					<span class="sr-only" aria-live="polite"></span>
				</a>
			</div>
		</div>
		
		{{#showsummary}}
		<div class="lead mb-4">{{{summary}}}</div>
		{{/showsummary}}
		
		<div class="d-flex align-items-center mt-3 mt-md-4">
			{{! // to be implemented
			<div class="custom-link mr-5">
				<a class="d-flex align-items-center h5 mb-0" href="#">
					<i class="fa fa-circle-play fa-2x pr-3"></i> Continuar
				</a>
			</div>
			}}	

			{{#showcourseprogress}}
			<div class="d-flex align-items-center col-md-6 p-0">
				<div class="progress bg-glassy w-100 mr-3" style="height: 8px;">
					<div class="progress-bar bg-danger" role="progressbar" style="width:{{progress}}%" aria-valuenow="{{progress}}" aria-valuemin="0" aria-valuemax="100"></div>
				</div>
				<span class="text-white">{{progress}}%</span>
			</div>
			{{/showcourseprogress}}
		</div>
	</div>
</div>

{{#js}}
	require(['jquery'], function($){
		$("#page-enrol-index .box.generalbox")
			.wrapAll("<div class='enrolmethods-wrapper'></div>")
	})
{{/js}}