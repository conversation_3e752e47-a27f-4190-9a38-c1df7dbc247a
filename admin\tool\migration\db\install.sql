CREATE TABLE mdl_eadtech_estudantes (
    id BIGINT(10) NOT NULL auto_increment,
    source_codaluno BIGINT(10) NOT NULL,
    source_nome VARCHAR(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
    source_email VARCHAR(120) COLLATE utf8mb4_general_ci,
    source_estado VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_sexo VARCHAR(20) COLLATE utf8mb4_general_ci,
    source_nascimento VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_escolaridade VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_estadocivil VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_renda VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_datacadastro VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_cnpj_lista TEXT COLLATE utf8mb4_general_ci,
    source_cargo VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_nivelocupacional VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_cpf VARCHAR(15) COLLATE utf8mb4_general_ci,
    source_emailsecundario VARCHAR(120) COLLATE utf8mb4_general_ci,
    source_celular VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_campolivre3 VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_perfil VARCHAR(60) COLLATE utf8mb4_general_ci,
    source_status VARCHAR(50) COLLATE utf8mb4_general_ci,
    source_filial VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_dataadmissao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_dataexpiracao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_dataalteracao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_nomesocial VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_pais VARCHAR(50) COLLATE utf8mb4_general_ci,
    source_funcao VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_degreed_usuarioativo VARCHAR(3) COLLATE utf8mb4_general_ci,
    source_ufsebrae VARCHAR(60) COLLATE utf8mb4_general_ci,
    source_datamodificacao VARCHAR(30) COLLATE utf8mb4_general_ci,
    custom_data TEXT COLLATE utf8mb4_general_ci,
    instanceid BIGINT(10),
    legacyid BIGINT(10),
    needs_update TINYINT(1) NOT NULL DEFAULT 0,
    hash TEXT COLLATE utf8mb4_general_ci NOT NULL,
    locked_by VARCHAR(60) COLLATE utf8mb4_general_ci,
    lock_expires_at BIGINT(10),
CONSTRAINT  PRIMARY KEY (id)
, KEY mdl_eadtestu_sou2_ix (source_codaluno)
)
 ENGINE = InnoDB
 DEFAULT COLLATE = utf8mb4_general_ci ROW_FORMAT=DYNAMIC
 COMMENT='Dados de migração da EADTECH';

CREATE TABLE mdl_eadtech_cursos (
    id BIGINT(10) NOT NULL auto_increment,
    source_codcurso BIGINT(10) NOT NULL,
    source_disponivel VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_idcurso VARCHAR(50) COLLATE utf8mb4_general_ci,
    source_nome VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_nomecursomenu VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_status VARCHAR(50) COLLATE utf8mb4_general_ci,
    source_prazo VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_datacriacao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_datamoficacao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_cursopresencial VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_frequencia VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_media VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_rematricula VARCHAR(50) COLLATE utf8mb4_general_ci,
    source_keywords VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_cargahoraria VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_descricao MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_tipo_solucao MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_apresentacao MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_objetivos MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_conteudo_programatico MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_nivel_de_complexidade MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_termo_de_aceite MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_ficha_tecnica MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_requisitos MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_criterios_de_avaliacao MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_publico MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_area_subarea MEDIUMTEXT COLLATE utf8mb4_general_ci,
    instanceid BIGINT(10),
    legacyid BIGINT(10),
    needs_update TINYINT(1) NOT NULL DEFAULT 0,
    hash MEDIUMTEXT COLLATE utf8mb4_general_ci NOT NULL,
    locked_by VARCHAR(60) COLLATE utf8mb4_general_ci,
    lock_expires_at BIGINT(10),
CONSTRAINT  PRIMARY KEY (id)
, KEY mdl_eadtcurs_sou2_ix (source_codcurso)
)
 ENGINE = InnoDB
 DEFAULT COLLATE = utf8mb4_general_ci ROW_FORMAT=DYNAMIC
 COMMENT='Dados de migração da EADTECH - Cursos';

CREATE TABLE mdl_eadtech_segmentos (
    id BIGINT(10) NOT NULL auto_increment,
    source_codsegmento BIGINT(10) NOT NULL,
    source_nomesegmento VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_criado VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_modificado VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_codusuariogestor BIGINT(10),
    instanceid BIGINT(10),
    needs_update TINYINT(1) NOT NULL DEFAULT 0,
    hash TEXT COLLATE utf8mb4_general_ci NOT NULL,
    locked_by VARCHAR(60) COLLATE utf8mb4_general_ci,
    lock_expires_at BIGINT(10),
CONSTRAINT  PRIMARY KEY (id)
, UNIQUE KEY mdl_eadtsegm_sou2_uix (source_codsegmento)
)
 ENGINE = InnoDB
 DEFAULT COLLATE = utf8mb4_general_ci ROW_FORMAT=DYNAMIC
 COMMENT='Dados de migração da EADTECH - Segmentos';

CREATE TABLE mdl_eadtech_usuarios_segmento (
    id BIGINT(10) NOT NULL auto_increment,
    source_codaluno BIGINT(10) NOT NULL,
    source_codsegmento BIGINT(10) NOT NULL,
    source_tipo VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_principal VARCHAR(10) COLLATE utf8mb4_general_ci,
    instanceid BIGINT(10),
    needs_update TINYINT(1) NOT NULL DEFAULT 0,
    hash MEDIUMTEXT COLLATE utf8mb4_general_ci NOT NULL,
    locked_by VARCHAR(60) COLLATE utf8mb4_general_ci,
    lock_expires_at BIGINT(10),
CONSTRAINT  PRIMARY KEY (id)
, UNIQUE KEY mdl_eadtusuasegm_sousou2_uix (source_codsegmento, source_codaluno)
)
 ENGINE = InnoDB
 DEFAULT COLLATE = utf8mb4_general_ci ROW_FORMAT=DYNAMIC
 COMMENT='Dados de migração da EADTECH - Relacionamento entre usuarios';

 CREATE TABLE mdl_eadtech_trilhas (
    id BIGINT(10) NOT NULL auto_increment,
    source_id BIGINT(10) NOT NULL,
    source_disponivel VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_nome VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_status VARCHAR(50) COLLATE utf8mb4_general_ci,
    source_prazo VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_data_de_criacao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_data_de_modificacao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_frequencia VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_media VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_permite_rematricula VARCHAR(50) COLLATE utf8mb4_general_ci,
    source_keywords VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_carga_horaria_minima VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_descricao MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_tipo_solucao MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_apresentacao MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_objetivos MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_conteudo_programatico MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_nivel_de_complexidade MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_termo_de_aceite MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_ficha_tecnica MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_requisitos MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_criterios_de_avaliacao MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_publico MEDIUMTEXT COLLATE utf8mb4_general_ci,
    source_area_subarea MEDIUMTEXT COLLATE utf8mb4_general_ci,
    instanceid BIGINT(10),
    legacyid BIGINT(10),
    needs_update TINYINT(1) NOT NULL DEFAULT 0,
    hash MEDIUMTEXT COLLATE utf8mb4_general_ci NOT NULL,
    locked_by VARCHAR(60) COLLATE utf8mb4_general_ci,
    lock_expires_at BIGINT(10),
CONSTRAINT  PRIMARY KEY (id)
, KEY mdl_eadttril_sou_ix (source_id)
)
 ENGINE = InnoDB
 DEFAULT COLLATE = utf8mb4_general_ci ROW_FORMAT=DYNAMIC
 COMMENT='Dados de migração da EADTECH - Trilhas';

 CREATE TABLE mdl_eadtech_gestores (
    id BIGINT(10) NOT NULL auto_increment,
    source_codusuariogestor BIGINT(10) NOT NULL,
    source_nomecompleto VARCHAR(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
    source_login VARCHAR(120) NOT NULL COLLATE utf8mb4_general_ci,
    source_email VARCHAR(120) COLLATE utf8mb4_general_ci,
    source_datadecriacao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_datamodificacao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_status VARCHAR(50) COLLATE utf8mb4_general_ci,
    source_trocarsenha VARCHAR(3) COLLATE utf8mb4_general_ci,
    source_acessarmensagens VARCHAR(3) COLLATE utf8mb4_general_ci,
    source_webaula VARCHAR(3) COLLATE utf8mb4_general_ci,
    source_ehelpdesk VARCHAR(3) COLLATE utf8mb4_general_ci,
    source_acessobiometrico VARCHAR(3) COLLATE utf8mb4_general_ci,
    source_tran_userid VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_corpodocente VARCHAR(255) COLLATE utf8mb4_general_ci,
    instanceid BIGINT(10),
    needs_update TINYINT(1) NOT NULL DEFAULT 0,
    hash MEDIUMTEXT COLLATE utf8mb4_general_ci NOT NULL,
    locked_by VARCHAR(60) COLLATE utf8mb4_general_ci,
    lock_expires_at BIGINT(10),
CONSTRAINT  PRIMARY KEY (id)
, KEY mdl_eadtgest_sou_ix (source_codusuariogestor)
)
 ENGINE = InnoDB
 DEFAULT COLLATE = utf8mb4_general_ci ROW_FORMAT=DYNAMIC
 COMMENT='Dados de migração dos gestores EADTECH';

CREATE TABLE mdl_eadtech_progresso_cursos (
    id BIGINT(10) NOT NULL auto_increment,
    source_codsituacaoalunocurso BIGINT(10) NOT NULL,
    source_codcurso BIGINT(10) NOT NULL,
    source_codturma BIGINT(10) NOT NULL,
    source_codaluno BIGINT(10) NOT NULL,
    source_datamatricula VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_percconclusao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_percaproveitamento VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_prazodeacesso VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_origemmatricula VARCHAR(60) COLLATE utf8mb4_general_ci,
    source_datainicio VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_datafim VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_statusmatricula VARCHAR(60) COLLATE utf8mb4_general_ci,
    source_progressstatus VARCHAR(60) COLLATE utf8mb4_general_ci,
    source_data_de_cancelamento VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_motivo_do_cancelamento VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_nota_do_usuario VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_data_do_primeiro_acesso VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_data_do_ultimo_acesso VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_data_de_conclusao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_data_de_modificacao VARCHAR(30) COLLATE utf8mb4_general_ci,
    instanceid BIGINT(10) NOT NULL DEFAULT 0,
    legacyid BIGINT(10),
    course_completion_id BIGINT(10) NOT NULL DEFAULT 0,
    user_enrolment_id BIGINT(10) NOT NULL DEFAULT 0,
    needs_update TINYINT(1) NOT NULL DEFAULT 0,
    hash LONGTEXT COLLATE utf8mb4_general_ci NOT NULL,
    locked_by VARCHAR(60) COLLATE utf8mb4_general_ci,
    lock_expires_at BIGINT(10),
CONSTRAINT  PRIMARY KEY (id)
, KEY mdl_eadtprogcurs_sou_ix (source_codsituacaoalunocurso),
KEY idx_eadtech_prog_prev_comp (
        source_codcurso,
        source_codaluno,
        course_completion_id,
        source_codsituacaoalunocurso
    )
)
 ENGINE = InnoDB
 DEFAULT COLLATE = utf8mb4_general_ci ROW_FORMAT=DYNAMIC
 COMMENT='Dados de progresso em cursos (situações de matricula)';

CREATE TABLE mdl_eadtech_progresso_trilhas (
    id BIGINT(10) NOT NULL auto_increment,
    source_id BIGINT(10) NOT NULL,
    source_codigo_da_trilha BIGINT(10) NOT NULL,
    source_codigo_da_turma BIGINT(10) NOT NULL,
    source_codaluno BIGINT(10) NOT NULL,
    source_data_de_matricula VARCHAR(30) COLLATE utf8mb4_general_ci,
    porcent_conclusao VARCHAR(30) COLLATE utf8mb4_general_ci,
    porcent_aproveitamento VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_prazo_de_acesso VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_origem_da_matricula VARCHAR(60) COLLATE utf8mb4_general_ci,
    source_data_de_inicio VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_data_de_fim VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_status_da_matricula VARCHAR(60) COLLATE utf8mb4_general_ci,
    source_status_de_progresso VARCHAR(60) COLLATE utf8mb4_general_ci,
    source_data_de_cancelamento VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_motivo_do_cancelamento VARCHAR(255) COLLATE utf8mb4_general_ci,
    source_nota_do_usuario VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_data_do_primeiro_acesso VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_data_do_ultimo_acesso VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_data_de_conclusao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_data_de_modificacao VARCHAR(30) COLLATE utf8mb4_general_ci,
    instanceid BIGINT(10) NOT NULL DEFAULT 0,
    legacyid BIGINT(10),
    course_completion_id BIGINT(10) NOT NULL DEFAULT 0,
    user_enrolment_id BIGINT(10) NOT NULL DEFAULT 0,
    needs_update TINYINT(1) NOT NULL DEFAULT 0,
    hash LONGTEXT COLLATE utf8mb4_general_ci NOT NULL,
    locked_by VARCHAR(60) COLLATE utf8mb4_general_ci,
    lock_expires_at BIGINT(10),
CONSTRAINT  PRIMARY KEY (id)
, KEY mdl_eadtprogtril_sou_ix (source_id)
, KEY mdl_eadtprogtril_sousoucous_ix (source_codigo_da_trilha, source_codaluno, course_completion_id, source_id)
)
 ENGINE = InnoDB
 DEFAULT COLLATE = utf8mb4_general_ci ROW_FORMAT=DYNAMIC
 COMMENT='Dados de progresso em trilhas (situações de matricula) EAD';

CREATE TABLE mdl_eadtech_empresas (
    id BIGINT(10) NOT NULL auto_increment,
    source_codempresa BIGINT(10) NOT NULL,
    source_razao_social VARCHAR(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
    source_cnpj VARCHAR(20) COLLATE utf8mb4_general_ci,
    source_datacadastro VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_estado VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_dataabertura VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_excluido VARCHAR(2) COLLATE utf8mb4_general_ci,
    source_dataalteracao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_fornecedorsolucoes VARCHAR(2) COLLATE utf8mb4_general_ci,
    source_nome_fantasia VARCHAR(255) COLLATE utf8mb4_general_ci,
    instanceid BIGINT(10),
    needs_update TINYINT(1) NOT NULL DEFAULT 0,
    hash LONGTEXT COLLATE utf8mb4_general_ci NOT NULL,
    locked_by VARCHAR(60) COLLATE utf8mb4_general_ci,
    lock_expires_at BIGINT(10),
CONSTRAINT  PRIMARY KEY (id)
, UNIQUE KEY mdl_eadtempr_sou_uix (source_codempresa)
)
 ENGINE = InnoDB
 DEFAULT COLLATE = utf8mb4_general_ci ROW_FORMAT=DYNAMIC
 COMMENT='Dados de migração da EADTECH - Empresas';


CREATE TABLE mdl_eadtech_empresas_alunos (
    id BIGINT(10) NOT NULL auto_increment,
    source_codempresa_aluno BIGINT(10) NOT NULL,
    source_codaluno BIGINT(10) NOT NULL,
    source_codempresa BIGINT(10),
    source_datacadastro VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_excluido VARCHAR(1) COLLATE utf8mb4_general_ci,
    source_empresaprincipal VARCHAR(1) COLLATE utf8mb4_general_ci,
    source_datainclusao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_datamodificacao VARCHAR(30) COLLATE utf8mb4_general_ci,
    instanceid BIGINT(10),
    needs_update TINYINT(1) NOT NULL DEFAULT 0,
    hash LONGTEXT COLLATE utf8mb4_general_ci NOT NULL,
    locked_by VARCHAR(60) COLLATE utf8mb4_general_ci,
    lock_expires_at BIGINT(10),
CONSTRAINT  PRIMARY KEY (id)
, UNIQUE KEY mdl_eadtempralun_sou_uix (source_codempresa_aluno)
)
 ENGINE = InnoDB
 DEFAULT COLLATE = utf8mb4_general_ci ROW_FORMAT=DYNAMIC
 COMMENT='EADTECH - Empresas x Alunos';

CREATE TABLE mdl_eadtech_seg_turmas_curso (
    id BIGINT(10) NOT NULL auto_increment,
    source_codturma BIGINT(10) NOT NULL,
    source_codsegmento BIGINT(10) NOT NULL,
    source_codprogramaturma BIGINT(10),
    instanceid BIGINT(10),
    needs_update TINYINT(1) NOT NULL DEFAULT 0,
    hash LONGTEXT COLLATE utf8mb4_general_ci NOT NULL,
    locked_by VARCHAR(60) COLLATE utf8mb4_general_ci,
    lock_expires_at BIGINT(10),
CONSTRAINT  PRIMARY KEY (id)
, UNIQUE KEY mdl_eadtsegturmcurs_sousou_uix (source_codturma, source_codsegmento)
)
 ENGINE = InnoDB
 DEFAULT COLLATE = utf8mb4_general_ci ROW_FORMAT=Compressed
 COMMENT='EADTECH - Segmentos x Turmas de cursos';

CREATE TABLE mdl_eadtech_seg_turmas_trilha (
    id BIGINT(10) NOT NULL auto_increment,
    source_codturma BIGINT(10) NOT NULL,
    source_codsegmento BIGINT(10) NOT NULL,
    source_codtrilha BIGINT(10),
    instanceid BIGINT(10),
    needs_update TINYINT(1) NOT NULL DEFAULT 0,
    hash LONGTEXT COLLATE utf8mb4_general_ci NOT NULL,
    locked_by VARCHAR(60) COLLATE utf8mb4_general_ci,
    lock_expires_at BIGINT(10),
CONSTRAINT  PRIMARY KEY (id)
, UNIQUE KEY mdl_eadtsegturmtril_sousou_uix (source_codturma, source_codsegmento)
)
 ENGINE = InnoDB
 DEFAULT COLLATE = utf8mb4_general_ci ROW_FORMAT=Compressed
 COMMENT='EADTECH - Segmentos x Turmas de trilhas';


 
CREATE TABLE mdl_eadtech_cursos_externos (
    id BIGINT(10) NOT NULL auto_increment,
    source_id BIGINT(10) NOT NULL,
    source_codaluno BIGINT(10) NOT NULL,
    source_nomedaacao VARCHAR(120) COLLATE utf8mb4_general_ci,
    source_descricao TEXT COLLATE utf8mb4_general_ci NOT NULL,
    source_instituicao VARCHAR(90) COLLATE utf8mb4_general_ci,
    source_datarealizacao VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_cargahoraria VARCHAR(10) COLLATE utf8mb4_general_ci,
    source_certificado VARCHAR(120) COLLATE utf8mb4_general_ci,
    source_criado VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_editado VARCHAR(30) COLLATE utf8mb4_general_ci,
    source_excluido VARCHAR(1) COLLATE utf8mb4_general_ci,
    source_certificadoguid VARCHAR(120) COLLATE utf8mb4_general_ci,
    source_datarealizacaofim VARCHAR(1) COLLATE utf8mb4_general_ci,
    instanceid BIGINT(10),
    needs_update TINYINT(1) NOT NULL DEFAULT 0,
    hash TEXT COLLATE utf8mb4_general_ci NOT NULL,
    locked_by VARCHAR(60) COLLATE utf8mb4_general_ci,
    lock_expires_at BIGINT(10),
CONSTRAINT  PRIMARY KEY (id)
, UNIQUE KEY mdl_eadtcursexte_sou_uix (source_id)
)
 ENGINE = InnoDB
 DEFAULT COLLATE = utf8mb4_general_ci ROW_FORMAT=Compressed
 COMMENT='EADTECH - Cursos externos';