<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace enrol_offer_self;

use local_offermanager\persistent\offer_class_model;
use moodleform;

/**
 * Class self_enrol_form
 *
 * @package    enrol_offer_self
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class self_enrol_form extends moodleform
{
    protected $instance;

    /**
     * Overriding this function to get unique form id for multiple self enrolments.
     *
     * @return string form identifier
     */
    protected function get_form_identifier()
    {
        $formid = $this->_customdata->id . '_' . get_class($this);
        return $formid;
    }

    public function definition()
    {
        $mform = $this->_form;
        $instance = $this->_customdata;

        $offerclass = offer_class_model::get_by_enrolid($instance->id);

        $description = $offerclass->get_description_html();
        $enrolperiod_str = $offerclass->get_formated_enrol_period();

        $plugin = $offerclass->get_plugin();

        $offercourse = $offerclass->get_offer_course();

        $mform->addElement('hidden', 'id');
        $mform->setType('id', PARAM_INT);
        $mform->setDefault('id', $offercourse->get('courseid'));

        $mform->addElement('hidden', 'instance');
        $mform->setType('instance', PARAM_INT);
        $mform->setDefault('instance', $instance->id);

        $heading = $plugin->get_instance_name($instance);
        $mform->addElement('header', 'selfheader', $heading);

        if ($description) {
            $mform->addElement(
                'static',
                'description',
                get_string('description'),
                $description
            );
        }

        $mform->addElement(
            'static',
            'enrolperiod',
            get_string('enrolperiod', 'local_offermanager'),
            $enrolperiod_str
        );

        $this->add_action_buttons(false, get_string('enrolme', 'enrol_self'));
    }

    public function validation($data, $files)
    {
        $errors = parent::validation($data, $files);

        return $errors;
    }
}
