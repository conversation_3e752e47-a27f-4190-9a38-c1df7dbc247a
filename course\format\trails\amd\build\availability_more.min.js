define("format_trails/availability_more",["exports"],(function(_exports){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0;const Selectors_availabilityinfo="#page-course-view-trails #region-main .notifications .alert.alert-danger .availabilityinfo-error ul",Selectors_availabilityinfoshowmore="#region-main .notifications .alert.alert-danger .availabilityinfo-error ul li.showmore",Selectors_dataregion="availability-multiple",Selectors_dataaction="showmore";_exports.init=()=>{window.onload=function(){document.querySelectorAll(Selectors_availabilityinfo).forEach((e=>e.setAttribute("data-region",Selectors_dataregion))),document.querySelectorAll(Selectors_availabilityinfoshowmore).forEach((e=>e.setAttribute("data-action",Selectors_dataaction)))}}}));

//# sourceMappingURL=availability_more.min.js.map