<?php


defined('MOODLE_INTERNAL') || die;

if ($ADMIN->fulltree) {

    $settings->add(
        new admin_setting_configtext(
            "block_externalhistory/title",
            get_string('configtitle', 'local_courseblockapi'),
            '',
            get_string('pluginname', 'block_externalhistory'),
            PARAM_TEXT
        )
    );

    // Display Course Categories on the recently accessed courses block items.
    $settings->add(
        new admin_setting_configtext(
            "block_externalhistory/per_page",
            get_string('per_page', 'local_courseblockapi'),
            get_string('per_page_help', 'local_courseblockapi'),
            8,
            PARAM_INT
        )
    );
}
