<?php

/**
 * @copyright 2025 Revvo
 * @license http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 * @package tool_companymanagement
 */

namespace tool_companymanagement\form;

use tool_companymanagement\repositories\company_repository;
use tool_companymanagement\services\company_service;
use local_ssystem\util\data_formatter_trait;
use stdClass;
use tool_companymanagement\util\cnpj_helper;
use context;

defined('MOODLE_INTERNAL') || die;

require_once($CFG->dirroot . '/admin/tool/companymanagement/lib.php');

class company_form extends \core_form\dynamic_form
{
    use data_formatter_trait;

    protected company_service $service;

    /**
     * Define the form.
     */
    public function definition()
    {
        $mform = $this->_form;

        $company = new stdClass;

        // Shared fields.
        tool_companymanagement_companyedit_shared_definition($mform, $company);
    }

    protected function get_company_service() : company_service {
        if(!isset($this->service)){
            $this->service = new company_service(new company_repository);
        }
        return $this->service;
    }

    /**
     * Validate the form data.
     * @param array $companynew
     * @param array $files
     * @return array|bool
     */
    public function validation($companynew, $files)
    {
        $err = parent::validation($companynew, $files) ?: [];
        
        $id   = isset($companynew['id']) ? (int)$companynew['id'] : 0;
        $cnpj = $this->unformat_CNPJ($companynew['cnpj'] ?? '');

        // var_dump([
        //     'id' => $id,
        //     'cnpj' => $cnpj,
        //     'companynew' => $companynew,
        // ]);exit();

        if (empty($cnpj) || !cnpj_helper::validate($cnpj)) {
            $err['cnpj'] = get_string('cnpjinvalid', 'tool_companymanagement');
            return $err;
        }

        $repository = $this->get_company_service()->get_repository();

        $exists = $repository->find_by_CNPJ($cnpj);
        if ($exists && ($id === 0 || $exists->get('id') !== $id)) {
            $err['cnpj'] = get_string('cnpjexists', 'tool_companymanagement');
        }

        return $err;
    }

    /**
     * Returns context where this form is used
     *
     * @return context
     */
    protected function get_context_for_dynamic_submission(): context
    {
        return \context_system::instance();
    }

    /**
     * Checks if current user has access to this form, otherwise throws exception
     */
    protected function check_access_for_dynamic_submission(): void
    {
        if (isset($this->_ajaxformdata) && $this->_ajaxformdata['action'] === "update") {
            require_capability('tool/companymanagement:update', $this->get_context_for_dynamic_submission());
        } else {
            require_capability('tool/companymanagement:create', $this->get_context_for_dynamic_submission());
        }
    }

    /**
     * Process the form submission, used if form was submitted via AJAX
     */
    public function process_dynamic_submission()
    {
        $service = $this->get_company_service();

        $companynew = $this->get_data();

        if (isset($companynew->id) && $companynew->id > 0) {
            $service->update($companynew);
        } else {
            $service->create($companynew);
        }

        return null;
    }

    /**
     * Load in existing data as form defaults
     */
    public function set_data_for_dynamic_submission(): void
    {
        if ($id = $this->optional_param('id', 0, PARAM_INT)) {
            $repository = $this->get_company_service()->get_repository();
            $company = $repository->find($id);

            $this->set_data($company->to_record());
        }
    }

    /**
     * Page url
     * @return \moodle_url
     */
    protected function get_page_url_for_dynamic_submission(): \moodle_url
    {
        return new \moodle_url("/admin/tool/companymanagement/index.php");
    }
}
