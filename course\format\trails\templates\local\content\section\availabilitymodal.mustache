{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/content/section/availabilitymodal

    Displays the section availability modal form.

    Example context (json):
    {
    }

}}
<div class="d-flex flex-column p-3">
    <form>
        <div class="d-flex flex-row align-items-start py-3 border-bottom">
            <div class="icon-box mx-2">
                    {{#pix}} t/hide, core {{/pix}}
                </div>
            <input
                class="mt-2 mx-2"
                type="radio"
                id="showSectionRadio"
                name="option"
                value="sectionShow"
                aria-describedby="showRadio_help"
            >
            <div class="w-100">
                <label class="mb-1" for="showSectionRadio">
                    {{#str}} availability_show, core_courseformat {{/str}}
                </label>
                <div id="showRadio_help" class="small text-muted">
                    {{#str}} availability_show_help, core_courseformat {{/str}}
                </div>
            </div>
        </div>
        <div class="d-flex flex-row align-items-start py-3">
            <div class="icon-box mx-2">
                    {{#pix}} t/show, core {{/pix}}
                </div>
            <input
                class="mt-2 mx-2"
                type="radio"
                id="hideSectionRadio"
                name="option"
                value="sectionHide"
                aria-describedby="hideRadio_help"
            >
            <div class="w-100">
                <label class="mb-1" for="hideSectionRadio">
                    {{#str}} availability_hide, core_courseformat {{/str}}
                </label>
                <div id="hideRadio_help" class="small text-muted">
                    {{#str}} availability_hide_help, core_courseformat {{/str}}
                </div>
            </div>
        </div>
    </form>
</div>
