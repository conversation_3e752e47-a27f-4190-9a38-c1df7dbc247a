SELECT DISTINCT
    SAC.CODSITUAC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    SAC.CODCURSO               AS CODCURSO,
    SAC.CODTURMA               AS CODTURMA,
    SAC.CODALUNO               AS CODALUNO,
    SAC.DATAMATRICULA          AS DATAMATRICULA,
    SAC.PercConclusao          AS PercConclusao,
    SAC.PercAproveitamento     AS PercAproveitamento,
    SAC.PRAZODEACESSO          AS PrazoDeAcesso,
    SAC.ORIGEMMATRICULA        AS OrigemMatricula,
    SAC.DATAINICIO             AS DataInicio,
    SAC.DATAFIM                AS DataFim,
    TA.STATUSMATRICULA         AS StatusMatricula,
    CASE
        WHEN TA.STATUSMATRICULA = 'M' THEN dbo.GetStudentProgressStatus(
            ISNULL(totalPontuaveis, 0),
            IIF(SAC.PRAZODEACESSO >= GETDATE(), 'S', 'N'),
            CAST(ISNULL(SAC.PercConclusao,       0.00) AS DECIMAL),
            CAST(ISNULL(C.FREQUENCIA,            0.00) AS DECIMAL),
            CAST(ISNULL(SAC.PercAproveitamento,  0.00) AS DECIMAL),
            CAST(ISNULL(C.MEDIA,                 0.00) AS DECIMAL),
            SAC.FINALIZADO
        )
        WHEN TA.STATUSMATRICULA = 'C' THEN 'Cancelado'
        WHEN TA.STATUSMATRICULA = 'D' THEN 'Desistente'
        WHEN TA.STATUSMATRICULA = 'T' THEN 'Trancado'
    END                         AS ProgressStatus,
    MC.DTCANCELAMENTO           AS Data_de_cancelamento,
    CASE MC.CODMOTIVO
        WHEN 0 THEN 'Outros'
        WHEN 1 THEN 'Nao gostei do conteudo do curso'
        WHEN 2 THEN 'Nao tenho tempo para concluir'
        WHEN 3 THEN 'Quero me matricular em outro curso'
        WHEN 4 THEN 'Nao sou empresario'
        WHEN 5 THEN 'Nao tem um motivo'
    END                         AS Motivo_do_cancelamento,
    ''                          AS Nota_do_usuario,
    SAC.DATAINICIO              AS Data_do_primeiro_acesso,
    SAC.DATAFIM                 AS Data_do_ultimo_acesso,
    CASE 
        WHEN SAC.FINALIZADO = 'S' THEN SAC.DATAFIM 
        ELSE NULL 
    END                         AS Data_de_conclusao,
    ''                          AS Data_de_modificacao
FROM SITUACAOALUNOCURSO SAC WITH (NOLOCK)
INNER JOIN TURMAS_ALUNOS TA WITH (NOLOCK) ON (TA.CODALUNO = SAC.CODALUNO AND TA.CODTURMA = SAC.CODTURMA)
INNER JOIN TURMA T WITH (NOLOCK) ON (T.CODTURMA = SAC.CODTURMA)
INNER JOIN CURSOS C WITH (NOLOCK) ON (C.CODCURSO = SAC.CODCURSO)
INNER JOIN USUARIOS U WITH (NOLOCK) ON (U.CODALUNO = SAC.CODALUNO)
LEFT JOIN (
    SELECT 
        CODTURMA, 
        ISNULL(SUM(TOTALPONTUAVEIS1 + TOTALPONTUAVEIS2), 0) AS TOTALPONTUAVEIS
    FROM (
        SELECT 
            T.CODTURMA,
            (
                SELECT COUNT(*) 
                FROM TOPICOS TP WITH (NOLOCK)
                WHERE (
                    (TP.CompoeAproveitamento = 'S' AND TP.PONTUAVEL = 'S')
                    OR TP.TIPOPONTUAVEL IN ('Q', 'U')
                )
                AND TP.CODTURMA = T.CODTURMA
            ) AS TOTALPONTUAVEIS1,
            (
                SELECT COUNT(*) 
                FROM PONTUAVEISEXTRAS PES WITH (NOLOCK)
                WHERE PES.CODTURMA = T.CODTURMA
            ) AS TOTALPONTUAVEIS2
        FROM TURMA T WITH (NOLOCK)
    ) TOTAIS
    GROUP BY CODTURMA
) PONTUAVEIS ON (PONTUAVEIS.CODTURMA = T.CODTURMA)
LEFT JOIN MATRICULASCANCELADAS MC WITH (NOLOCK) ON (
    SAC.CODALUNO = MC.CODALUNO AND 
    SAC.CODTURMA = MC.CODTURMA AND 
    MC.TIPOCANCELAMENTO = 'C'
)
WHERE 
    U.WebAula = 0
    AND T.ActivityCollectionClassId IS NULL
    AND T.activityCollectionClassIdConfigClass IS NULL
    AND CompoeTrilhaDeAprendizagem = 0

UNION ALL

SELECT DISTINCT
    SAC.CODSITUACAOALUNOCURSO AS ID,
    SAC.CODCURSO,
    SAC.CODTURMA,
    SAC.CODALUNO,
    SAC.DATAINICIO AS DATAMATRICULA,
    SAC.PercConclusao,
    SAC.PercAproveitamento,
    SAC.PRAZODEACESSO,
    SAC.ORIGEMMATRICULA,
    SAC.DATAINICIO,
    SAC.DATAFIM,
    TA.STATUSMATRICULA,
    CASE 
        WHEN TA.STATUSMATRICULA = 'M' THEN [dbo].[GetStudentProgressStatus](
            ISNULL(totalPontuaveis, 0),
            IIF(SAC.PRAZODEACESSO >= GETDATE(), 'S', 'N'),
            CAST(ISNULL(SAC.PercConclusao, 0.00) AS DECIMAL),
            CAST(ISNULL(C.FREQUENCIA, 0.00) AS DECIMAL),
            CAST(ISNULL(SAC.PercAproveitamento, 0.00) AS DECIMAL),
            CAST(ISNULL(C.MEDIA, 0.00) AS DECIMAL),
            SAC.FINALIZADO
        )
        WHEN TA.STATUSMATRICULA = 'C' THEN 'Cancelado'
        WHEN TA.STATUSMATRICULA = 'D' THEN 'Desistente'
        WHEN TA.STATUSMATRICULA = 'T' THEN 'Trancado'
    END AS PROGRESSSTATUS,
    MC.DTCANCELAMENTO AS [Data de cancelamento],
    CASE MC.CODMOTIVO
        WHEN 0 THEN 'Outros'
        WHEN 1 THEN 'Não gostei do conteúdo do curso'
        WHEN 2 THEN 'Não tenho tempo para concluir'
        WHEN 3 THEN 'Quero me matricular em outro curso'
        WHEN 4 THEN 'Não sou empresário'
        WHEN 5 THEN 'Não tem um motivo'
    END AS [Motivo do cancelamento],
    '' AS [Nota do usuário],
    SAC.DATAINICIO AS [Data do primeiro acesso],
    SAC.DATAFIM AS [Data do último acesso],
    CASE 
        WHEN SAC.FINALIZADO = 'S' THEN SAC.DATAFIM 
        ELSE NULL 
    END AS [Data de conclusão],
    '' AS [Data de modificação]
FROM HISTORICOSITUACAOALUNOCURSO SAC WITH (NOLOCK)
INNER JOIN TURMAS_ALUNOS TA WITH (NOLOCK) ON (TA.CODALUNO = SAC.CODALUNO AND TA.CODTURMA = SAC.CODTURMA)
INNER JOIN TURMA T WITH (NOLOCK) ON (T.CODTURMA = SAC.CODTURMA)
INNER JOIN CURSOS C WITH (NOLOCK) ON (C.CODCURSO = SAC.CODCURSO)
INNER JOIN USUARIOS U WITH (NOLOCK) ON (U.CODALUNO = SAC.CODALUNO)
LEFT JOIN (
    SELECT 
        CODTURMA, 
        ISNULL(SUM(TOTALPONTUAVEIS1 + TOTALPONTUAVEIS2), 0) AS TOTALPONTUAVEIS
    FROM (
        SELECT 
            T.CODTURMA,
            (
                SELECT COUNT(*) 
                FROM TOPICOS TP WITH (NOLOCK)
                WHERE (
                    (TP.CompoeAproveitamento = 'S' AND TP.PONTUAVEL = 'S')
                    OR TP.TIPOPONTUAVEL IN ('Q', 'U')
                )
                AND TP.CODTURMA = T.CODTURMA
            ) AS TOTALPONTUAVEIS1,
            (
                SELECT COUNT(*) 
                FROM PONTUAVEISEXTRAS PES WITH (NOLOCK)
                WHERE PES.CODTURMA = T.CODTURMA
            ) AS TOTALPONTUAVEIS2
        FROM TURMA T WITH (NOLOCK)
    ) TOTAIS
    GROUP BY CODTURMA
) PONTUAVEIS ON (PONTUAVEIS.CODTURMA = T.CODTURMA)
LEFT JOIN MATRICULASCANCELADAS MC WITH (NOLOCK) ON (
    SAC.CODALUNO = MC.CODALUNO AND 
    SAC.CODTURMA = MC.CODTURMA AND 
    MC.TIPOCANCELAMENTO = 'C'
)
WHERE 
    SAC.MotivoHistoricoRematricula = 1
    AND U.WebAula = 0
    AND T.ActivityCollectionClassId IS NULL
    AND T.activityCollectionClassIdConfigClass IS NULL
    AND CompoeTrilhaDeAprendizagem = 0;



-- Não tem nota?