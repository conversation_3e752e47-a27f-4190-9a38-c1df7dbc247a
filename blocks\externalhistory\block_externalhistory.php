<?php

/**
 * Block definition class for the block_pluginname plugin.
 *
 * @package   block_externalhistory
 * @copyright 2025, REVVO <www.somosrevvo.com.br>
 */
defined('MOODLE_INTERNAL') || die();

use block_externalhistory\output\external_history_list_block;

class block_externalhistory extends block_base {

    /**
     * Initialize block title.
     */
    public function init(): void {
        // $this->title = get_string('pluginname', 'block_externalhistory');
        $this->title = ''; // Avoids double title.
    }

    /**
     * Include custom CSS when block is displayed.
     */
    public function specialization(): void {
        global $PAGE;
        $PAGE->requires->css('/blocks/externalhistory/styles.css');
    }

    /**
     * Generate block content using renderable and renderer.
     *
     * @return stdClass|null
     */
    public function get_content() {
        global $USER, $PAGE;

        if ($this->content !== null) {
            return $this->content;
        }

        $renderable = new external_history_list_block($USER->id, 3);

        $renderer = $PAGE->get_renderer('block_externalhistory');
        $html = $renderer->render_external_history_list_block($renderable);

        $this->content = new stdClass();
        $this->content->text   = $html;
        $this->content->footer = '';

        return $this->content;
    }
}