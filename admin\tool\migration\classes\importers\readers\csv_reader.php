<?php namespace tool_migration\importers\readers;

use Generator;
use RuntimeException;

class csv_reader {
    private string $filepath;

    public function __construct(string $filepath)
    {
        $this->filepath = $filepath;
    }

    /**
     * @return Generator|array[]
     */
    public function read($prefix = "source_"): Generator
    {
        $handle = fopen($this->filepath, 'r');
        if (!$handle) {
            throw new RuntimeException("Não foi possível abrir o arquivo CSV.");
        }

        // Lê o cabeçalho
        $rawHeader = fgetcsv($handle, 0, ';');
        if (!$rawHeader) {
            throw new RuntimeException("O cabeçalho do CSV está vazio ou inválido.");
        }

        $rawHeader[0] = preg_replace('/^\xEF\xBB\xBF/', '', $rawHeader[0]);

        $header = array_map(fn($col) => $prefix . mb_strtolower(trim($col)), $rawHeader);

        while (($row = fgetcsv($handle, 0, ';')) !== false) {
            if (count($row) !== count($header)) {
                continue;
            }

            yield array_combine($header, $row);
        }

        fclose($handle);
    }
}