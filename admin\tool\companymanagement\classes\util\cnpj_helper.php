<?php

namespace tool_companymanagement\util;

class cnpj_helper {

    /**
     * Validates a CNPJ using its check digits.
     *
     * @param string $cnpj The CNPJ to validate.
     * @return bool True if the CNPJ is valid, false otherwise.
     */
    public static function validate(string $cnpj): bool
    {
        $cnpj = preg_replace('/\D/', '', $cnpj);

        if (strlen($cnpj) !== 14) {
            return false;
        }

        if (preg_match('/^(\d)\1{13}$/', $cnpj)) {
            return false;
        }

        $multipliers1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
        $multipliers2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];

        $sum1 = 0;
        for ($i = 0; $i < 12; $i++) {
            $sum1 += $cnpj[$i] * $multipliers1[$i];
        }
        $remainder1 = $sum1 % 11;
        $checkDigit1 = $remainder1 < 2 ? 0 : 11 - $remainder1;

        $sum2 = 0;
        for ($i = 0; $i < 13; $i++) {
            $sum2 += $cnpj[$i] * $multipliers2[$i];
        }
        $remainder2 = $sum2 % 11;
        $checkDigit2 = $remainder2 < 2 ? 0 : 11 - $remainder2;

        return $cnpj[12] == $checkDigit1 && $cnpj[13] == $checkDigit2;
    }
}
