<?php namespace tool_migration\models;

use core\persistent;
use Generator;
use coding_exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Abstract class adding record-level locking to migratable entities.
 */
abstract class abstract_lockable_migratable_entity extends abstract_migratable_entity {

    protected static string $lock_uuid;

    /**
     * Returns a static UUID for locking and identification.
     *
     * @return string
     */
    protected static function get_lock_uuid(): string{
        if(empty(self::$lock_uuid)){
            self::$lock_uuid = \core\uuid::generate();
        }
        return self::$lock_uuid;
    }

    /**
     * Returns the lock duration
     *
     * @return int
     */
    protected static function get_lock_expiration_time(): int {
        return time() + HOURSECS;
    }

    /**
     * @throws coding_exception If a property key from define_model_properties does not start with 'source_'.
     * @return array
     */
    protected static function define_properties(): array {
        $definitions = parent::define_properties();

        return array_merge($definitions, [
            'locked_by' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
            ],
            'lock_expires_at' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
            ],
        ]);
    }

    /**
     * Reserve a batch of new imports by marking lock columns, then fetch via recordset.
     *
     * @param int $reserve_count Number of records to lock.
     * @return Generator<static>
     */
    public static function get_pending_imports(int $reserve_count = 100): Generator {
        global $DB;

        $table = static::TABLE;
        $params = [
            'lock_uuid' => static::get_lock_uuid(),
            'expires_at' => static::get_lock_expiration_time(),
            'now' => time(),
            'limit' => $reserve_count,
        ];

        $sql = "UPDATE {{$table}}
            SET locked_by = :lock_uuid, lock_expires_at = :expires_at
            WHERE instanceid = 0
                AND (lock_expires_at IS NULL OR lock_expires_at < :now)
            ORDER BY id
            LIMIT :limit";
        $DB->execute($sql, $params);

        $recordset = $DB->get_recordset_select(
            static::TABLE,
            'locked_by = :lock_uuid AND lock_expires_at >= :now AND instanceid IS NULL',
            $params,
            'id'
        );
        foreach ($recordset as $record) {
            yield new static(0, $record);
        }
        $recordset->close();
    }

    /**
     * Reserve a batch of updates by marking lock columns, then fetch via recordset.
     *
     * @param int $reserve_count Number of records to lock.
     * @return Generator<static>
     */
    public static function get_pending_updates(int $reserve_count = 100): Generator{
        global $DB;

        $table = static::TABLE;
        $params = [
            'lock_uuid' => static::get_lock_uuid(),
            'expires_at' => static::get_lock_expiration_time(),
            'now' => time(),
            'limit' => $reserve_count,
        ];

        $sql = "UPDATE {{$table}}
            SET locked_by = :lock_uuid, lock_expires_at = :expires_at
            WHERE instanceid > 0
                AND needs_update = 1
                AND (lock_expires_at IS NULL OR lock_expires_at < :now)
            ORDER BY id
            LIMIT :limit";
        $DB->execute($sql, $params);

        $recordset = $DB->get_recordset_select(
            static::TABLE,
            'locked_by = :lock_uuid AND lock_expires_at >= :now AND instanceid > 0 AND needs_update == 1',
            $params,
            'id'
        );
        foreach ($recordset as $record) {
            yield new static(0, $record);
        }
        $recordset->close();
    }

    /**
     * Clear lock metadata after processing.
     *
     * @return void
     */
    public function release_lock(bool $save = true){
        $this->raw_set('locked_by', null);
        $this->raw_set('lock_expires_at', null);
        
        if($save){
            $this->save();
        }
    }

    public function mark_as_updated() : static {
        $this->release_lock(false);
        return parent::mark_as_updated();
    }
}
