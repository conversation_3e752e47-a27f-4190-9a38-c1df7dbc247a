{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/content/section/cmlist

    Displays the course module list inside a course section.

    Example context (json):
    {
        "cms": [
            {
                "cmitem": {
                    "cmformat": {
                        "cmname": "<a class=\"aalink\" href=\"#\"><span class=\"instancename\">Forum example</span></a>",
                        "hasname": "true"
                    },
                    "id": 3,
                    "module": "forum",
                    "extraclasses": "newmessages"
                }
            },
            {
                "cmitem": {
                    "cmformat": {
                        "cmname": "<a class=\"aalink\" href=\"#\"><span class=\"instancename\">Assign example</span></a>",
                        "hasname": "true"
                    },
                    "id": 4,
                    "module": "assign",
                    "extraclasses": ""
                }
            }
        ],
        "hascms": true,
        "showmovehere": true,
        "movingstr": "Moving this activity: folder example",
        "cancelcopyurl": "#",
        "movetosectionurl": "#",
        "strmovefull": "Move 'folder example' to this location"
    }
}}
{{#showmovehere}}
<p>{{movingstr}} (<a href="{{{cancelcopyurl}}}">{{#str}} cancel {{/str}}</a>)</p>
{{/showmovehere}}
<ul class="section p-0 img-text {{#hascms}} d-block {{/hascms}} d-flex align-items-center flex-wrap" data-for="cmlist">
{{#cms}}
    {{#showmovehere}}
    <li class="movehere" style="height:100px">
        <a href="{{{moveurl}}}" title="{{strmovefull}}" class="movehere"></a>
    </li>
    {{/showmovehere}}
    {{#cmitem}}
        {{$ core_courseformat/local/content/section/cmitem}}
            {{> format_trails/local/content/section/cmitem}}
        {{/ core_courseformat/local/content/section/cmitem}}
    {{/cmitem}}
{{/cms}}

{{^cms}}
    <h5 class="ml-3" style="opacity:0.5">
		<i class="fa fa-warning pr-1"></i> {{#str}}noactivities, format_trails{{/str}}
	</h5>
{{/cms}}

{{#showmovehere}}
    <li class="movehere" style="height:100px">
        <a href="{{{movetosectionurl}}}" title="{{strmovefull}}" class="movehere"></a>
    </li>
{{/showmovehere}}
    </ul>
