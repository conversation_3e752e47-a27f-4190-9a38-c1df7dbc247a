<?php namespace tool_migration\models;

defined('MOODLE_INTERNAL') || die();

use tool_migration\util\ingestion_helper;
use Exception;
use Generator;

class progresso_curso extends abstract_migratable_entity {
    const TABLE = 'eadtech_progresso_cursos';

    protected ?int $userid;
    protected static array $course_instances = [];
    protected static array $enrol_instances = [];


    /**  
     * | Código          | Descrição        |
     * |-----------------|------------------|
     * |   EA            | Em Andamento     |
     * |   NI            | Não Iniciado     |
     * |   T             | Concluído        |
     * |   R             | Reprovado        |
     * |   D             | Abandono         |
     * |   A             | Aprovado         |
     * |   NC            | Não Concluído    |
     * |   completed     | Aprovado         |
     * |   concluded     | Concluído        |
     * |   dropout       | Abandono         |
     * |   inProgress    | Em Andamento     |
     * |   notConcluded  | Não Concluído    |
     * |   notStarted    | Não Iniciado     |
     * |   reproved      | Aprovado         |
     */
    const STATUS_COMPLETED = 'T';
    const STATUS_APPROVED = 'A';
    const STATUS_COMPLETED2 = 'concluded';
    const STATUS_APPROVED2 = 'completed';

    protected function get_enrol_instance_name() : string {
        return "LEGADO";
    }

    public function get_userid() : int {
        if(!isset($this->userid)){
            if ($id = estudante::get_instanceid_from_identifier($this->get('source_codaluno'))) {
                $this->userid = $id;
            }else{
                throw new Exception("Usuário " . $this->get('source_codaluno') . " não encontrado");
            }
        }

        return $this->userid;
    }

    protected function get_source_codsituacaoalunocurso() : int {
        return (int) $this->raw_get('source_codsituacaoalunocurso');
    }

    public function get_courseid() : int {
        $key = $this->raw_get('source_codcurso');

        if(!isset(static::$course_instances[$key])){
            static::$course_instances[$key] = $this->fetch_courseid();
        }

        return static::$course_instances[$key];
    }

    protected function fetch_courseid() : int {
        $courseid = curso::get_instanceid_from_identifier($this->get('source_codcurso'));

        if($courseid === null){
            throw new Exception("Curso " . $this->get('source_codcurso') . " não encontrado");
        }

        return $courseid;
    }

    public function get_enrolid() : int {
        $key = $this->get('source_codcurso');

        if(!isset(static::$enrol_instances[$key])){
            static::$enrol_instances[$key] = $this->fetch_enrolid();
        }

        return static::$enrol_instances[$key];
    }

    protected function fetch_enrolid() : int {
        global $DB;

        $enrol_conditions = [
            'enrol' => 'manual',
            'courseid' => $this->get_courseid(),
            'name' => $this->get_enrol_instance_name(),
        ];

        if($enrolid = $DB->get_field('enrol', 'id', $enrol_conditions)){
            return $enrolid;
        }

        $enrol_conditions['status'] = 0;
        $enrol_conditions['timecreated'] = time();
        $enrol_conditions['timemodified'] = time();

        return $DB->insert_record('enrol', $enrol_conditions);
    }


    public static function define_identifier(): string {
        return 'source_codsituacaoalunocurso';
    }

    public function is_completed() : bool {
        $progressstatus = trim($this->get('source_progressstatus') ?? '');
        return in_array($progressstatus, [
            static::STATUS_COMPLETED,
            static::STATUS_APPROVED,
            static::STATUS_COMPLETED2,
            static::STATUS_APPROVED2,
        ]);
    }

    protected static function define_model_properties(): array {
        return [
            'source_codsituacaoalunocurso' => [
                'type' => PARAM_INT,
            ],
            'source_codcurso' => [
                'type' => PARAM_TEXT,
            ],
            'source_codturma' => [
                'type' => PARAM_TEXT,
            ],
            'source_codaluno' => [
                'type' => PARAM_TEXT,
            ],
            'source_datamatricula' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_percconclusao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_percaproveitamento' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_prazodeacesso' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_origemmatricula' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_datainicio' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_datafim' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_statusmatricula' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_progressstatus' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_data_de_cancelamento' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_motivo_do_cancelamento' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_nota_do_usuario' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_data_do_primeiro_acesso' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_data_do_ultimo_acesso' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_data_de_conclusao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_data_de_modificacao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
        ];
    }

    protected static function define_properties(): array {
        $definition = parent::define_properties();

        $definition['course_completion_id'] = [
            'type' => PARAM_INT,
            'default' => 0,
        ];

        $definition['user_enrolment_id'] = [
            'type' => PARAM_INT,
            'default' => 0,
        ];

        return $definition;
    }


    public function get_idnumber(): string {
        return 'LEGADO-' . $this->get('source_codsituacaoalunocurso');
    }

    public function to_legacy_record() : object {
        global $CFG;

        $record = (object)[];
               
        return $record;
    }

    public function to_user_enrolment() : object {
        $record = (object)[
            'status' => 0,
            'userid' => $this->get_userid(),
            'enrolid' => $this->get_enrolid(),
            'timestart' => ingestion_helper::str_to_timestamp($this->get('source_datainicio')),
            'timeend' => ingestion_helper::str_to_timestamp($this->get('source_datafim')),
            'timecreated' => ingestion_helper::str_to_timestamp($this->get('source_datamatricula')),
            'timemodified' => time(),
        ];

        if($id = $this->get('user_enrolment_id')){
            $record->id = $id;
        }
               
        return $record;
    }

    public function to_course_completion() : object {
        $record = (object)[
            'userid' => $this->get_userid(),
            'course' => $this->get_courseid(),
            'timeenrolled' => ingestion_helper::str_to_timestamp($this->get('source_datamatricula')),
            'timestarted' => ingestion_helper::str_to_timestamp($this->get('source_data_do_primeiro_acesso')),
            'timecompleted' => ingestion_helper::str_to_timestamp($this->get('source_data_de_conclusao')),
        ];

        if($id = $this->get('course_completion_id')){
            $record->id = $id;
        }
               
        return $record;
    }

    /**
     * Returns a generator yielding entities wich completion have not yet been imported.
     * 
     * @param int $limit Optional limit on the number of records to retrieve.
     * @return Generator
     */
    public static function get_pending_completion_imports(int $limit = 0): Generator {
        global $DB;

        $recordset = $DB->get_recordset_select(
            static::TABLE,
            'course_completion_id = 0',
            null,
            'id',
            '*',
            0,
            $limit
        );

        foreach ($recordset as $record) {
            yield new static(0, $record);
        }
        $recordset->close();
    }

    /**
     * Returns a generator yielding entities wich completion have already been imported but require updates.
     *
     * @param int $limit Optional limit on the number of records to retrieve.
     * @return Generator
     */
    public static function get_pending_completion_updates(int $limit = 0): Generator {
        global $DB;

        $recordset = $DB->get_recordset_select(
            static::TABLE,
            'course_completion_id > 0 AND needs_update = 1',
            null,
            'id',
            '*',
            0,
            $limit
        );
        foreach ($recordset as $record) {
            yield new static(0, $record);
        }
        $recordset->close();
    }

    public function mark_enrolment_as_imported(int $instanceid, bool $save = true) : static {
        $this->set('user_enrolment_id', $instanceid);

        if($save){
            $this->save();
        }

        return $this;
    }

    public function mark_completion_as_imported(int $instanceid) : static {
        $this->set('course_completion_id', $instanceid);
        $this->save();
        return $this;
    }
}
