<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace enrol_offer_manual;

use advanced_testcase;
use enrol_offer_manual_plugin;
use local_offermanager\persistent\offer_model;
use local_offermanager\persistent\offer_course_model;
use local_offermanager\persistent\offer_class_model;
use moodle_exception;
use context_course;
use stdClass;

/**
 * Tests for Turma manual
 *
 * @package    enrol_offer_manual
 * @category   test
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
final class enrol_offer_manual_plugin_test extends \advanced_testcase
{
    /**
     * Testa a criação bem-sucedida de uma instância de inscrição.
     */
    public function test_add_instance_success()
    {
        $this->resetAfterTest();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offercourse = $offer->add_course($course->id);

        $plugin = new enrol_offer_manual_plugin();
        $instanceid = $plugin->add_instance($course, [
            'offercourseid' => $offercourse->get('id'),
            'classname' => 'Turma A',
            'startdate' => time(),
        ]);

        $this->assertGreaterThan(0, $instanceid);

        $classes = $offercourse->get_classes();
        $this->assertCount(1, $classes);

        $class = $classes[0];
        $this->assertEquals($instanceid, $class->get('enrolid'));
    }

    /**
     * Testa a criação de uma instância de inscrição com campo offercourseid ausente.
     */
    public function test_add_instance_missing_offercourseid()
    {
        $this->resetAfterTest();

        $course = $this->getDataGenerator()->create_course();

        $plugin = new enrol_offer_manual_plugin();

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('offercourseid_required', 'enrol_offer_manual'));
        $plugin->add_instance($course, []);
    }

    /**
     * Testa a criação de uma instância de inscrição com campo classname ausente.
     */
    public function test_add_instance_missing_classname()
    {
        $this->resetAfterTest();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offercourse = $offer->add_course($course->id);

        $plugin = new enrol_offer_manual_plugin();

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:classname_required', 'enrol_offer_manual'));
        $plugin->add_instance($course, ['offercourseid' => $offercourse->get('id')]);
    }

    /**
     * Testa a criação de uma instância de inscrição com campo startdate ausente.
     */
    public function test_add_instance_missing_startdate()
    {
        $this->resetAfterTest();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offercourse = $offer->add_course($course->id);

        $plugin = new enrol_offer_manual_plugin();

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:startdate_required', 'enrol_offer_manual'));
        $plugin->add_instance($course, ['offercourseid' => $offercourse->get('id'), 'classname' => 'Turma A']);
    }

    /**
     * Testa a atualização bem-sucedida de uma instância de inscrição.
     */
    public function test_update_instance_success()
    {
        $this->resetAfterTest();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offercourse = $offer->add_course($course->id);

        $plugin = new enrol_offer_manual_plugin();

        $plugin->add_instance($course, [
            'offercourseid' => $offercourse->get('id'),
            'classname' => 'Turma A',
            'startdate' => time(),
        ]);

        $classes = $offercourse->get_classes();

        $instance = $classes[0]->get_enrol_instance();

        $result = $plugin->update_instance($instance, (object) [
            'offercourseid' => $offercourse->get('id'),
            'classname' => 'Turma B',
            'startdate' => time(),
        ]);

        $this->assertTrue($result);

        $instance = $classes[0]->get_enrol_instance();

        $this->assertEquals('Turma B', $instance->name);
    }

    // /**
    //  * Testa a atualização de uma instância de inscrição com campo offercourseid ausente.
    //  */
    // public function test_update_instance_missing_offercourseid()
    // {
    //     $this->resetAfterTest();

    //     $offer = new offer_model(0, (object) [
    //         'name' => 'Oferta teste'
    //     ]);

    //     $offer->save();

    //     $course = $this->getDataGenerator()->create_course();

    //     $offercourse = $offer->add_course($course->id);

    //     $plugin = new enrol_offer_manual_plugin();
    //     $instanceid = $plugin->add_instance($course, [
    //         'offercourseid' => $offercourse->get('id'),
    //         'classname' => 'Turma A',
    //         'startdate' => time(),
    //     ]);

    //     $classes = $offercourse->get_classes();

    //     $instance = $classes[0]->get_enrol_instance();

    //     $this->expectException(moodle_exception::class);
    //     $this->expectExceptionMessage(get_string('offercourseid_required', 'enrol_offer_manual'));
    //     $plugin->update_instance($instance, (object) []);
    // }

    /**
     * Testa a atualização de uma instância de inscrição com campo classname ausente.
     */
    // public function test_update_instance_missing_classname()
    // {
    //     $this->resetAfterTest();

    //     $offer = new offer_model(0, (object) [
    //         'name' => 'Oferta teste'
    //     ]);

    //     $offer->save();

    //     $course = $this->getDataGenerator()->create_course();

    //     $offercourse = $offer->add_course($course->id);

    //     $plugin = new enrol_offer_manual_plugin();
    //     $instanceid = $plugin->add_instance($course, [
    //         'offercourseid' => $offercourse->get('id'),
    //         'classname' => 'Turma A',
    //         'startdate' => time(),
    //     ]);

    //     $classes = $offercourse->get_classes();

    //     $instance = $classes[0]->get_enrol_instance();


    //     $this->expectException(moodle_exception::class);
    //     $this->expectExceptionMessage(get_string('error:classname_required', 'enrol_offer_manual'));
    //     $plugin->update_instance($instance, (object) ['offercourseid' => $offercourse->get('id')]);
    // }

    // /**
    //  * Testa a atualização de uma instância de inscrição com campo startdate ausente.
    //  */
    // public function test_update_instance_missing_startdate()
    // {
    //     $this->resetAfterTest();

    //     $offer = new offer_model(0, (object) [
    //         'name' => 'Oferta teste'
    //     ]);

    //     $offer->save();

    //     $course = $this->getDataGenerator()->create_course();

    //     $offercourse = $offer->add_course($course->id);

    //     $plugin = new enrol_offer_manual_plugin();
    //     $instanceid = $plugin->add_instance($course, [
    //         'offercourseid' => $offercourse->get('id'),
    //         'classname' => 'Turma A',
    //         'startdate' => time(),
    //     ]);

    //     $classes = $offercourse->get_classes();

    //     $instance = $classes[0]->get_enrol_instance();

    //     $this->expectException(moodle_exception::class);
    //     $this->expectExceptionMessage(get_string('error:startdate_required', 'enrol_offer_manual'));
    //     $plugin->update_instance($instance, (object) ['offercourseid' => $offercourse->get('id'), 'classname' => 'Turma B']);
    // }

    /**
     * Testa a exclusão bem-sucedida de uma instância de inscrição.
     */
    public function test_delete_instance_success()
    {
        $this->resetAfterTest();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offercourse = $offer->add_course($course->id);

        $plugin = new enrol_offer_manual_plugin();
        $instanceid = $plugin->add_instance($course, [
            'offercourseid' => $offercourse->get('id'),
            'classname' => 'Turma A',
            'startdate' => time(),
        ]);

        $classes = $offercourse->get_classes();

        $instance = $classes[0]->get_enrol_instance();

        $plugin->delete_instance($instance);

        $this->assertEmpty($offercourse->get_classes());
    }

    /**
     * Testa a exclusão de uma instância de inscrição inexistente.
     */
    public function test_delete_instance_not_found()
    {
        $this->resetAfterTest();

        $plugin = new enrol_offer_manual_plugin();
        $instance = (object) ['id' => 999];

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:class_not_found', 'local_offermanager'));
        $plugin->delete_instance($instance);
    }

    /**
     * Testa a inscrição de um usuário com sucesso.
     */
    public function test_enrol_user_success()
    {
        $this->resetAfterTest();

        $course = $this->getDataGenerator()->create_course();
        $user = $this->getDataGenerator()->create_user();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();
        $user = $this->getDataGenerator()->create_user();

        $offercourse = $offer->add_course($course->id);

        $plugin = new enrol_offer_manual_plugin();

        $plugin->add_instance($course, [
            'offercourseid' => $offercourse->get('id'),
            'classname' => 'Turma A',
            'startdate' => time() + 3600,
        ]);

        $classes = $offercourse->get_classes();

        $offer_class = $classes[0];
        $instance = $offer_class->get_enrol_instance();

        $plugin = new enrol_offer_manual_plugin();

        $plugin->enrol_user($instance, $user->id);

        $this->assertTrue(is_enrolled(context_course::instance($course->id), $user->id));

        $user_enrolments = $offer_class->get_user_enrolments();

        $this->assertCount(1, $user_enrolments);
        
        $user_enrolment = reset($user_enrolments);

        $this->assertEquals($instance->enrolstartdate, $user_enrolment->timestart);
        $this->assertEquals(0, $user_enrolment->timeend);
    }

    /**
     * Testa a inscrição de um usuário com timestart e timeend personalizados.
     */
    public function test_enrol_user_custom_timestart_timeend()
    {
        $this->resetAfterTest();

        $course = $this->getDataGenerator()->create_course();
        $user = $this->getDataGenerator()->create_user();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();
        $user = $this->getDataGenerator()->create_user();

        $offercourse = $offer->add_course($course->id);

        $plugin = new enrol_offer_manual_plugin();

        $plugin->add_instance($course, [
            'offercourseid' => $offercourse->get('id'),
            'classname' => 'Turma A',
            'startdate' => time(),
        ]);

        $classes = $offercourse->get_classes();

        $offer_class = $classes[0];
        $instance = $offer_class->get_enrol_instance();

        $timestart = time() + 3600;
        $timeend = $timestart + 86400; 

        $plugin->enrol_user($instance, $user->id, null, $timestart, $timeend);

        $user_enrolments = $offer_class->get_user_enrolments();

        $user_enrolment = reset($user_enrolments);

        $this->assertEquals($timestart, $user_enrolment->timestart);
        $this->assertEquals($timeend, $user_enrolment->timeend);
    }

    /**
     * Testa a inscrição de um usuário com timestart e timeend padrão.
     */
    public function test_enrol_user_default_timestart_timeend()
    {
        $this->resetAfterTest();

        $course = $this->getDataGenerator()->create_course();
        $user = $this->getDataGenerator()->create_user();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();
        $user = $this->getDataGenerator()->create_user();

        $offercourse = $offer->add_course($course->id);

        $plugin = new enrol_offer_manual_plugin();

        $now = time();
        $plugin->add_instance($course, [
            'offercourseid' => $offercourse->get('id'),
            'classname' => 'Turma A',
            'startdate' => $now,
            'enddate' => $now + 172800,
            'enrolperiod' => 86400,
        ]);

        $classes = $offercourse->get_classes();

        $offer_class = $classes[0];
        $instance = $offer_class->get_enrol_instance();

        $plugin->enrol_user($instance, $user->id);
        $user_enrolments = $offer_class->get_user_enrolments();

        $user_enrolment = reset($user_enrolments);
        $this->assertEquals($instance->enrolstartdate, $user_enrolment->timestart);
        $this->assertEquals($now + $instance->enrolperiod, $user_enrolment->timeend);
    }

    /**
     * Testa a inscrição de um usuário com timestart e timeend padrão.
     */
    public function test_enrol_user_default_timestart_timeend_and_short_period()
    {
        $this->resetAfterTest();

        $course = $this->getDataGenerator()->create_course();
        $user = $this->getDataGenerator()->create_user();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();
        $user = $this->getDataGenerator()->create_user();

        $offercourse = $offer->add_course($course->id);

        $plugin = new enrol_offer_manual_plugin();

        $now = time();
        $plugin->add_instance($course, [
            'offercourseid' => $offercourse->get('id'),
            'classname' => 'Turma A',
            'startdate' => $now + 43200,
            'enddate' => $now + 86400,
            'enrolperiod' => 86400,
        ]);

        $classes = $offercourse->get_classes();

        $offer_class = $classes[0];
        $instance = $offer_class->get_enrol_instance();

        $plugin->enrol_user($instance, $user->id);
        $user_enrolments = $offer_class->get_user_enrolments();

        $user_enrolment = reset($user_enrolments);
        $this->assertEquals($instance->enrolstartdate, $user_enrolment->timestart);
        $this->assertEquals($instance->enrolenddate, $user_enrolment->timeend);
    }

    /**
     * Testa a inscrição de um usuário com roleid padrão.
     */
    public function test_enrol_user_default_roleid()
    {
        $this->resetAfterTest();

        $course = $this->getDataGenerator()->create_course();
        $user = $this->getDataGenerator()->create_user();

        $course = $this->getDataGenerator()->create_course();
        $user = $this->getDataGenerator()->create_user();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offercourse = $offer->add_course($course->id);

        $plugin = new enrol_offer_manual_plugin();

        $plugin->add_instance($course, [
            'offercourseid' => $offercourse->get('id'),
            'classname' => 'Turma A',
            'startdate' => time(),
            'roleid'  => 3
        ]);

        $classes = $offercourse->get_classes();

        $offer_class = $classes[0];
        $instance = $offer_class->get_enrol_instance();

        $plugin->enrol_user($instance, $user->id);

        $roles = get_user_roles(context_course::instance($course->id), $user->id);
        $role = reset($roles);
        $this->assertEquals(3, $role->roleid);
    }

    /**
     * Testa a inscrição de um usuário com roleid personalizado.
     */
    public function test_enrol_user_custom_roleid()
    {
        $this->resetAfterTest();

        // Cria um curso e um usuário.
        $course = $this->getDataGenerator()->create_course();
        $user = $this->getDataGenerator()->create_user();

        $course = $this->getDataGenerator()->create_course();
        $user = $this->getDataGenerator()->create_user();

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta teste'
        ]);

        $offer->save();

        $course = $this->getDataGenerator()->create_course();

        $offercourse = $offer->add_course($course->id);

        $plugin = new enrol_offer_manual_plugin();

        $plugin->add_instance($course, [
            'offercourseid' => $offercourse->get('id'),
            'classname' => 'Turma A',
            'startdate' => time(),
            'roleid'  => 3
        ]);

        $classes = $offercourse->get_classes();

        $offer_class = $classes[0];
        $instance = $offer_class->get_enrol_instance();

        $plugin->enrol_user($instance, $user->id, 5);

        $roles = get_user_roles(context_course::instance($course->id), $user->id);
        $role = reset($roles);
        $this->assertEquals(5, $role->roleid);
    }

    /**
     * Testa o método validate_required_fields da trait.
     */
    public function test_validate_required_fields()
    {
        $this->resetAfterTest();

        $plugin = new enrol_offer_manual_plugin();
        $errors = [];
        $data = [];

        $reflection = new \ReflectionClass(get_class($plugin));
        $method = $reflection->getMethod('validate_required_fields');
        $method->setAccessible(true);
        $method->invokeArgs($plugin, [&$data, &$errors]);

        $this->assertArrayHasKey('offercourseid', $errors);
        $this->assertArrayHasKey('classname', $errors);
        $this->assertArrayHasKey('startdate', $errors);
    }

    /**
     * Testa o método validate_date_fields da trait.
     */
    public function test_validate_date_fields()
    {
        $this->resetAfterTest();

        $plugin = new enrol_offer_manual_plugin();
        $errors = [];
        $data = [];

        $reflection = new \ReflectionClass(get_class($plugin));
        $method = $reflection->getMethod('validate_date_fields');
        $method->setAccessible(true);
        $method->invokeArgs($plugin, [&$data, &$errors]);

        $this->assertEmpty($errors);

        $data['enableenddate'] = true;
        $errors = [];
        $method->invokeArgs($plugin, [&$data, &$errors]);
        $this->assertArrayHasKey('enddate', $errors);

        $data['enddate'] = time() - 1000;
        $data['startdate'] = time();
        $errors = [];
        $method->invokeArgs($plugin, [&$data, &$errors]);
        $this->assertArrayHasKey('enddate', $errors);
    }

    /**
     * Testa o método validate_user_limits da trait.
     */
    public function test_validate_user_limits()
    {
        $this->resetAfterTest();

        $plugin = new enrol_offer_manual_plugin();
        $errors = [];
        $data = [];

        $reflection = new \ReflectionClass(get_class($plugin));
        $method = $reflection->getMethod('validate_user_limits');
        $method->setAccessible(true);
        $method->invokeArgs($plugin, [&$data, &$errors]);


        $this->assertEmpty($errors);

        $data['minusers'] = 'abc';
        $errors = [];
        $method->invokeArgs($plugin, [&$data, &$errors]);
        $this->assertArrayHasKey('minusers', $errors);

        $data['minusers'] = 10;
        $data['maxusers'] = 5;
        $errors = [];
        $method->invokeArgs($plugin, [&$data, &$errors]);
        $this->assertArrayHasKey('maxusers', $errors);
    }

    /**
     * Testa o método validate_numeric_fields da trait.
     */
    public function test_validate_numeric_fields()
    {
        $this->resetAfterTest();

        $plugin = new enrol_offer_manual_plugin();
        $errors = [];
        $data = [];

        $reflection = new \ReflectionClass(get_class($plugin));
        $method = $reflection->getMethod('validate_numeric_fields');
        $method->setAccessible(true);
        $method->invokeArgs($plugin, [&$data, &$errors]);

        $this->assertEmpty($errors);

        $data['maxusers'] = 'abc';
        $errors = [];
        $method->invokeArgs($plugin, [&$data, &$errors]);
        $this->assertArrayHasKey('maxusers', $errors);

        $data['enrolperiod'] = 'abc';
        $errors = [];
        $method->invokeArgs($plugin, [&$data, &$errors]);
        $this->assertArrayHasKey('enrolperiod', $errors);
    }

    /**
     * Testa o método validate_extension_fields da trait.
     */
    public function test_validate_extension_fields()
    {
        $this->resetAfterTest();

        $plugin = new enrol_offer_manual_plugin();
        $errors = [];
        $data = [];

        $reflection = new \ReflectionClass(get_class($plugin));
        $method = $reflection->getMethod('validate_extension_fields');
        $method->setAccessible(true);
        $method->invokeArgs($plugin, [&$data, &$errors]);

        $this->assertEmpty($errors);

        $data['enableextension'] = true;
        $errors = [];
        $method->invokeArgs($plugin, [&$data, &$errors]);
        $this->assertArrayHasKey('extensiondaysbefore', $errors);

        $data['enableextension'] = true;
        $data['extensiondaysbefore'] = 10;
        $errors = [];
        $method->invokeArgs($plugin, [&$data, &$errors]);
        $this->assertArrayHasKey('extensionmaxrequests', $errors);

        $data['enableextension'] = false;
        $data['extensionmaxrequests'] = 'abc';
        $errors = [];
        $method->invokeArgs($plugin, [&$data, &$errors]);
        $this->assertArrayHasKey('extensionmaxrequests', $errors);
    }

    /**
     * Testa o método edit_instance_validation da trait.
     */
    public function test_edit_instance_validation()
    {
        $this->resetAfterTest();

        $plugin = new enrol_offer_manual_plugin();
        $course = $this->getDataGenerator()->create_course();
        $context = context_course::instance($course->id);
        $instance = new stdClass();
        $instance->id = 1;

        // Teste com dados válidos
        $data = [
            'offercourseid' => 1,
            'classname' => 'Turma A',
            'startdate' => time(),
        ];
        $files = [];
        $errors = $plugin->edit_instance_validation($data, $files, $instance, $context);
        $this->assertEmpty($errors);

        $data = [
            'classname' => 'Turma A',
            'startdate' => time(),
        ];
        $files = [];
        $errors = $plugin->edit_instance_validation($data, $files, $instance, $context);
        $this->assertArrayHasKey('offercourseid', $errors);

        $data = [
            'offercourseid' => 1,
            'classname' => 'Turma A',
            'startdate' => time(),
            'enableenddate' => 1,
            'enddate' => time() - 1000,
        ];
        $files = [];
        $errors = $plugin->edit_instance_validation($data, $files, $instance, $context);
        $this->assertArrayHasKey('enddate', $errors);
    }
}
