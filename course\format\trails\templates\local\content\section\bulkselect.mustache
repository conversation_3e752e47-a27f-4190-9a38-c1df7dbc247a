{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/content/section/bulkselect

    Displays an section bulk selector.

    Example context (json):
    {
        "id": 35,
        "name": "Section title"
    }
}}
<div class="bulkselect align-self-center d-none" data-for="sectionBulkSelect">
    <input
        id="sectionCheckbox{{id}}"
        type="checkbox"
        data-id="{{id}}"
        data-action="toggleSelectionSection"
        data-bulkcheckbox="1"
    />
    <label class="sr-only" for="sectionCheckbox{{id}}">
        {{#selecttext}} {{selecttext}} {{/selecttext}}
        {{^selecttext}}
            {{#str}} selectsection, core_courseformat, {{name}}{{/str}}
        {{/selecttext}}
    </label>
</div>
