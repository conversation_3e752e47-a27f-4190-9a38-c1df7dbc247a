<?php namespace tool_migration\importers;

use tool_migration\models\progresso_trilha;
use Exception;

require_once($CFG->dirroot . '/user/profile/lib.php');
require_once($CFG->dirroot . '/user/lib.php');

class progress_trilha_importer extends abstract_importer {

    public function import_pending(int $limit = 0) : void {
        foreach (progresso_trilha::get_pending_imports($limit) as $progresso_trilha) {
            try {               
                $this->upsert_legacy_record($progresso_trilha);
            } catch (\Throwable $th) {
                $identifier = $progresso_trilha->get($progresso_trilha::define_identifier());
                $this->output("Erro ao importar situação de matrícula \"$identifier\": " .$th->getMessage(), 1);
            }
        }
    }

    public function update_pending(int $limit = 0) : void {
        foreach (progresso_trilha::get_pending_updates($limit) as $progresso_trilha) {
            try {
                $this->upsert_legacy_record($progresso_trilha);
            } catch (\Throwable $th) {
                $identifier = $progresso_trilha->get($progresso_trilha::define_identifier());
                $this->output("Erro ao atualizar situação de matrícula \"$identifier\": " .$th->getMessage(), 1);
            }
        }
    }


    /**
     * Marks as obsolete (course_completion_id = -1, user_enrolment_id = -1)
     * all old registers that would be overwritten by newer versions.
     */
    public function prune_obsolete_completions(): void {
        global $DB;
        $this->output("Marcando registros obsoletos...", 0);

        $sql = "
            UPDATE {eadtech_progresso_trilhas} pc
            JOIN (
                SELECT
                    source_codigo_da_trilha,
                    source_codaluno,
                    MAX(source_id) AS max_situacao
                FROM {eadtech_progresso_trilhas}
                GROUP BY source_codigo_da_trilha, source_codaluno
            ) AS latest
            ON latest.source_codigo_da_trilha = pc.source_codigo_da_trilha
            AND latest.source_codaluno = pc.source_codaluno
            SET
                pc.course_completion_id = -1,
                pc.user_enrolment_id    = -1
            WHERE
                pc.source_id < latest.max_situacao
        ";

        $DB->execute($sql);
    }

    public function import_pending_completion(int $limit = 0) : void {
        $this->output("Importando matrículas e conclusões...", 0);

        foreach (progresso_trilha::get_pending_completion_imports($limit) as $progresso_trilha) {
            try {               
                $this->upsert_completion($progresso_trilha);
            } catch (\Throwable $th) {
                $identifier = $progresso_trilha->get($progresso_trilha::define_identifier());
                $this->output("Erro ao importar conclusão de curso \"$identifier\": " .$th->getMessage(), 1);
            }
        }
    }

    public function update_pending_completion(int $limit = 0) : void {
        $this->output("Importando matrículas e conclusões...", 0);
        foreach (progresso_trilha::get_pending_completion_updates($limit) as $progresso_trilha) {
            try {
                $this->upsert_completion($progresso_trilha);
            } catch (\Throwable $th) {
                $identifier = $progresso_trilha->get($progresso_trilha::define_identifier());
                $this->output("Erro ao atualizar conclusão de curso \"$identifier\": " .$th->getMessage(), 1);
            }
        }
    }
    
    protected function upsert_legacy_record(progresso_trilha $progresso_trilha){
        return; // TODO
    }


    
    protected function upsert_completion(progresso_trilha $progresso_trilha){
        $previous = $this->get_previously_imported_completion($progresso_trilha);

        if($previous){
            return $this->replace_completion($progresso_trilha, $previous);
        }        

        if($progresso_trilha->get('course_completion_id')){
            return $this->update_completion($progresso_trilha);
        }

        return $this->insert_completion($progresso_trilha);
    }


    protected function get_previously_imported_completion(progresso_trilha $progresso_trilha): ?progresso_trilha {
        global $DB;

        $select = "source_codigo_da_trilha = :codigo_da_trilha
                AND source_codaluno = :codaluno
                AND course_completion_id > 0
                AND source_id != :source_id";

        $params = [
            'codigo_da_trilha' => $progresso_trilha->get('source_codigo_da_trilha'),
            'codaluno' => $progresso_trilha->get('source_codaluno'),
            'source_id' => $progresso_trilha->get('source_id'),
        ];

        $records = $DB->get_records_select(
            progresso_trilha::TABLE,
            $select,
            $params,
            'source_id DESC',
            '*',
            0,
            1
        );

        if ($records) {
            return new progresso_trilha(0, reset($records));
        }

        return null;
    }


    protected function replace_completion(progresso_trilha $progresso_trilha, progresso_trilha $previous){
        global $DB;

        $identifier = $progresso_trilha->get($progresso_trilha::define_identifier());

        if($previous->get('source_id') > $progresso_trilha->get('source_id')){
            $progresso_trilha->set('user_enrolment_id', -1);
            $progresso_trilha->set('course_completion_id', -1);
            $progresso_trilha->save();

            $this->output("A matricula $identifier é obsoleta e foi ignorada", 1);
            return;
        }

        try {
            $transaction = $DB->start_delegated_transaction();

            $progresso_trilha->set('user_enrolment_id', $previous->get('user_enrolment_id'));
            $progresso_trilha->set('course_completion_id', $previous->get('course_completion_id'));
            $progresso_trilha->save();

            $previous->set('user_enrolment_id', -1);
            $previous->set('course_completion_id', -1);
            $previous->save();

            // Enrol
            $user_enrolment = $progresso_trilha->to_user_enrolment();
            $DB->update_record('user_enrolments', $user_enrolment);
            $this->output("Matrícula em curso $identifier atualizada", 1);
            
            // Completion
            $completion = $progresso_trilha->to_course_completion();
            $DB->update_record('course_completions', $completion);
            $progresso_trilha->mark_as_updated();            
            $this->output("Conclusão de curso $identifier atualizada", 1);

            $transaction->allow_commit();
        } catch (Exception $e) {
            $transaction->rollback($e);
        }
    }

    protected function insert_completion(progresso_trilha $progresso_trilha){
        global $DB;

        $identifier = $progresso_trilha->get($progresso_trilha::define_identifier());

        if(!$progresso_trilha->is_completed()){
            // Don't bother with nom-completed records
            $progresso_trilha->set('user_enrolment_id', -1);
            $progresso_trilha->set('course_completion_id', -1);
            $progresso_trilha->save();

            $this->output("A matricula $identifier não está completa e foi ignorada", 1);
            return;
        }


        try {
            $transaction = $DB->start_delegated_transaction();
            
            // Enrol
            $user_enrolment = $progresso_trilha->to_user_enrolment();
            // Checking if a previous enrolment exists:
            $user_enrolment->id = $DB->get_field('user_enrolments', 'id', ['enrolid' => $user_enrolment->enrolid, 'userid' => $user_enrolment->userid]);
            if(empty($user_enrolment->id)){
                $user_enrolment->id = $DB->insert_record('user_enrolments', $user_enrolment);
            }else{
                $this->output("Reutilizando user_enrolments.id anterior", 2);
            }
            $progresso_trilha->mark_enrolment_as_imported($user_enrolment->id);
            $this->output("Matrícula em curso $identifier adicionada", 1);
            
            // Completion
            $completion = $progresso_trilha->to_course_completion();
            // Checking if a previous completion exists:
            $completion->id = $DB->get_field('course_completions', 'id', ['course' => $completion->course, 'userid' => $completion->userid]);
            if(empty($completion->id)){
                $completion->id = $DB->insert_record('course_completions', $completion);
            }else{
                $this->output("Reutilizando course_completions.id anterior", 2);
            }        
            $progresso_trilha->mark_completion_as_imported($completion->id);            
            $this->output("Conclusão de curso $identifier adicionada", 1);

            $transaction->allow_commit();
        } catch (Exception $e) {
            $transaction->rollback($e);
        }
    }


    protected function update_completion(progresso_trilha $progresso_trilha){
        global $DB;

        try {
            $transaction = $DB->start_delegated_transaction();
            $identifier = $progresso_trilha->get($progresso_trilha::define_identifier());
            
            // Enrol
            $user_enrolment = $progresso_trilha->to_user_enrolment();
            $DB->update_record('user_enrolments', $user_enrolment);
            $this->output("Matrícula em curso $identifier atualizada", 1);
            
            // Completion
            $completion = $progresso_trilha->to_course_completion();
            $DB->update_record('course_completions', $completion);
            $progresso_trilha->mark_as_updated();            
            $this->output("Conclusão de curso $identifier atualizada", 1);

            $transaction->allow_commit();
        } catch (Exception $e) {
            $transaction->rollback($e);
        }
    }
}