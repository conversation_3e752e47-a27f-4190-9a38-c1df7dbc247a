{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/courseindex/section

    Displays a course index section entry.

    Example context (json):
    {
        "title": "General",
        "id": 23,
        "uniqid": "0",
        "number": 1,
        "sectionurl": "#",
        "indexcollapsed": 0,
        "current": 1,
        "visible": 1,
        "hasrestrictions": 0,
        "cms": [
            {
                "id": 10,
                "name": "Glossary of characters",
                "url": "#",
                "visible": 1,
                "isactive": 0
            },
            {
                "id": 11,
                "name": "World Cinema forum",
                "url": "#",
                "visible": 1,
                "isactive": 0
            },
            {
                "id": 12,
                "name": "Announcements",
                "url": "#",
                "visible": 0,
                "isactive": 1
            }
        ]
    }
}}
<div
    class="courseindex-section {{#current}}current{{/current}}"
    id="{{uniqid}}-course-index-section-{{id}}"
    data-for="section"
    data-id="{{id}}"
    data-number="{{number}}"
    role="treeitem"
    aria-owns="courseindexcollapse{{number}}"
>
    <div class="courseindex-item d-flex
            {{^visible}}dimmed{{/visible}}
            {{#hasrestrictions}}restrictions{{/hasrestrictions}}
            courseindex-section-title"
        id="courseindexsection{{number}}"
        data-for="section_item"
    >
        <a href="{{{sectionurl}}}"
            class="courseindex-link text-truncate"
            data-action="togglecourseindexsection"
            data-for="section_title"
            tabindex="-1"
        >
            {{{title}}}
        </a>
        <span class="current-badge badge badge-primary ml-2 px-2 rounded-pill">
            {{highlighted}}
        </span>
        <span class="courseindex-locked ml-1" data-for="cm_name">
            {{#pix}} t/locked, core {{/pix}}
        </span>
        <span class="dragicon ml-auto">{{#pix}}i/dragdrop{{/pix}}</span>
        <a data-toggle="collapse"
            href="#courseindexcollapse{{number}}"
            class="courseindex-chevron icons-collapse-expand {{#indexcollapsed}}collapsed{{/indexcollapsed}}"
            aria-expanded="{{^indexcollapsed}}true{{/indexcollapsed}}{{#indexcollapsed}}false{{/indexcollapsed}}"
            aria-controls="courseindexcollapse{{number}}"
            tabindex="-1"
        >
            <span class="collapsed-icon icon-no-margin mr-1"
                title="{{#str}} expand, core {{/str}}">
                <span class="dir-rtl-hide">{{#pix}} t/collapsedchevron, core {{/pix}}</span>
                <span class="dir-ltr-hide">{{#pix}} t/collapsedchevron_rtl, core {{/pix}}</span>
                <span class="sr-only">{{#str}} expand, core {{/str}}</span>
            </span>
            <span class="expanded-icon icon-no-margin mr-1"
                title="{{#str}} collapse, core {{/str}}">
                {{#pix}} t/expandedchevron, core {{/pix}}
                <span class="sr-only">{{#str}} collapse, core {{/str}}</span>
            </span>
        </a>
    </div>
    <div id="courseindexcollapse{{number}}"
        class="courseindex-item-content collapse {{^indexcollapsed}}show{{/indexcollapsed}}"
        aria-labelledby="courseindexsection{{number}}" role="group"
    >
        <ul class="courseindex-sectioncontent unlist" data-for="cmlist" data-id="{{id}}" role="group">
            {{#cms}}
            {{> format_trails/local/courseindex/cm }}
            {{/cms}}
        </ul>
    </div>
</div>
{{#js}}
require(['core_courseformat/local/courseindex/section'], function(component) {
    component.init('{{uniqid}}-course-index-section-{{id}}');
});
{{/js}}
