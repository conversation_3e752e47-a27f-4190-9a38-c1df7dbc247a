{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/courseindex/placeholders

    This template renders the loading placeholders for the course index.

    Example context (json):
    {}
}}
<div data-region="loading-placeholder-content" aria-hidden="true" id="{{uniqid}}-course-index-placeholder">
    <ul class="media-list">
        <li class="media">
            <div class="media-body col-md-6 p-0 d-flex align-items-center">
                <div class="bg-pulse-grey rounded-circle mr-2"></div>
                <div class="bg-pulse-grey w-100"></div>
            </div>
        </li>
        <li class="media">
            <div class="media-body col-md-6 p-0 d-flex align-items-center">
                <div class="bg-pulse-grey rounded-circle mr-2"></div>
                <div class="bg-pulse-grey w-100"></div>
            </div>
        </li>
        <li class="media">
            <div class="media-body col-md-6 p-0 d-flex align-items-center">
                <div class="bg-pulse-grey rounded-circle mr-2"></div>
                <div class="bg-pulse-grey w-100"></div>
            </div>
        </li>
        <li class="media">
            <div class="media-body col-md-6 p-0 d-flex align-items-center">
                <div class="bg-pulse-grey rounded-circle mr-2"></div>
                <div class="bg-pulse-grey w-100"></div>
            </div>
        </li>
    </ul>
</div>
{{#js}}
require(['format_trails/local/courseindex/placeholder'], function(component) {
    component.init('{{uniqid}}-course-index-placeholder');
});
{{/js}}
