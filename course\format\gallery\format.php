<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * gallery course format. Display the whole course as "gallery" made of modules.
 *
 * @package format_gallery
 * @copyright 2006 The Open University
 * <AUTHOR> and others.
 * @license http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir.'/filelib.php');
require_once($CFG->libdir.'/completionlib.php');

// Horrible backwards compatible parameter aliasing.
if ($season = optional_param('season', 0, PARAM_INT)) {
    $url = $PAGE->url;
    $url->param('section', $season);
    debugging('Outdated season param passed to course/view.php', DEBUG_DEVELOPER);
    redirect($url);
}
// End backwards-compatible aliasing.

// Retrieve course format option fields and add them to the $course object.
$format = course_get_format($course);
$course = $format->get_course();
$context = context_course::instance($course->id);

if (($marker >= 0) && has_capability('moodle/course:setcurrentsection', $context) && confirm_sesskey()) {
    $course->marker = $marker;
    course_set_marker($course->id, $marker);
}

// Make sure section 0 is created.
course_create_sections_if_missing($course, 0);

$renderer = $PAGE->get_renderer('format_gallery');

if (!empty($displaysection)) {
    $format->set_section_number($displaysection);
}
$outputclass = $format->get_output_classname('content');
$widget = new $outputclass($format);

// Get search term
$searchterm = format_gallery_get_search_term();

// Apply search filter using JavaScript if search term is provided
if (!empty($searchterm)) {
    $PAGE->requires->js_amd_inline("
        require(['jquery'], function($) {
            $(document).ready(function() {
            $('#page-course-view-gallery #input-search').val('".$searchterm."')
           }); 
        });
    ");
}

echo $renderer->render($widget);

// Include course format js module.
$PAGE->requires->js('/course/format/gallery/format.js');

// Acessibility(NVDA) to course cards
echo '
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Seleciona todos os elementos ".instancename" dentro de tags "a" e suas tags "a" parentes
            const links = document.querySelectorAll("a .instancename");
            
            links.forEach(function (span) {
                // Acessa o elemento "a" pai
                const link = span.closest("a");
                
                // Cria uma nova "div" com as mesmas classes do "a" original
                const newDiv = document.createElement("div");
                newDiv.className = link.className;
                
                // Move o conteúdo para a nova "div"
                while (link.firstChild) {
                    newDiv.appendChild(link.firstChild);
                }
                
                // Substitui o "a" pela "div" no DOM
                link.parentNode.replaceChild(newDiv, link);
            });

            // Agora, para adicionar o "aria-label" e o estilo z-index nos elementos "a"
            const playButtons = document.querySelectorAll(".activity-item > a");

            playButtons.forEach(function (button) {
                // Encontra o texto ".instancename"
                const instancenameText = button.closest(".activity-item").querySelector(".instancename").textContent.trim();
                
                // Adiciona o "aria-label" ao botão de play
                button.setAttribute("aria-label", instancenameText);
                
                // Adiciona o estilo z-index: 10
                button.style.zIndex = "10";
            });
        });

    </script>
';