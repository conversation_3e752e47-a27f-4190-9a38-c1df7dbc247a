<?php namespace tool_migration\importers;

use tool_migration\models\empresa;

require_once($CFG->dirroot . '/user/profile/lib.php');
require_once($CFG->dirroot . '/user/lib.php');

class empresa_importer extends abstract_importer {

    const COMPANY_TABLE = 'tool_companymanagement';

    public function import_pending(int $limit = 0) : void {
        foreach (empresa::get_pending_imports($limit) as $empresa) {
            try {
                $this->upsert_company($empresa);
            } catch (\Throwable $th) {
                $code = $empresa->get('source_codempresa');
                $this->output("Erro ao importar empresa \"$code\": " .$th->getMessage(), 1);
            }
        }
    }

    public function update_pending(int $limit = 0) : void {
        foreach (empresa::get_pending_updates($limit) as $empresa) {
            try {
                $this->upsert_company($empresa);
            } catch (\Throwable $th) {
                $code = $empresa->get('source_codempresa');
                $this->output("Erro ao atualizar empresa \"$code\": " .$th->getMessage(), 1);
            }
        }
    }

    protected function upsert_company(empresa $empresa){
        global $DB;
        $company = $empresa->to_company();

        if(empty($company->id)){
            $company->id = $DB->insert_record(self::COMPANY_TABLE, $company);
            $empresa->mark_as_imported($company->id);
            $this->output("Empresa $company->name adicionada", 1);
            return;
        }

        $DB->update_record(self::COMPANY_TABLE, $company);
        $empresa->mark_as_updated();
        $this->output("Empresa $company->name atualizada", 1);
    }
}
