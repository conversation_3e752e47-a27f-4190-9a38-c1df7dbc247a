<?php namespace tool_migration\helpers;

use core_user;

class session_helper {

    protected static $original_user = null;

    protected static function get_original_user() : object {
        global $USER;

        if(self::$original_user === null){
            self::$original_user = clone $USER;
            unset(self::$original_user->description);
            unset(self::$original_user->access);
            unset(self::$original_user->preference);
        }

        return self::$original_user;
    }

    public static function set_user(object|int $user){
        self::get_original_user();

        if(is_int($user)){
            $user = core_user::get_user($user);
        }
        
        $user = clone($user);
        unset($user->description);
        unset($user->access);
        unset($user->preference);

        \core\session\manager::init_empty_session();
        \core\session\manager::set_user($user);
    }

    public static function set_original_user(){
        $user = self::get_original_user();

        \core\session\manager::init_empty_session();
        \core\session\manager::set_user($user);
    }
}
