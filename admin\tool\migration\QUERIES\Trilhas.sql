SELECT
    act.id                                   AS id,
    act.active                               AS disponivel,
    act.name                                 AS nome,
    act.active                               AS status,
    NULL                                     AS prazo,
    act.RegistrationDate                     AS data_de_criacao,
    UpdateDate                           AS data_de_modificacao,
    PercentFrequecyForCertification      AS frequencia,
    PercentGradeForCertification         AS media,
    AllowReEnrollment                    AS permite_rematricula,
    act.keywords                             AS keywords,
    trail.Workload                           AS carga_horaria_minima,
    act.Description                          AS descricao,
    /* tipo_solucao via subquery */
    (
        SELECT TOP 1 pcpt.NOME
        FROM dbUcSebraeFull.dbo.PDICOMPETENCIA_TRILHA ppc
        INNER JOIN dbUcSebraeFull.dbo.PDICOMPETENCIA pcpt
            ON pcpt.CODPDICOMPETENCIA = ppc.CODPDICOMPETENCIA
        WHERE ppc.CODTRILHA = trail.id
    )                                        AS tipo_solucao,
    /* features como colunas via subqueries */
    (
        SELECT TOP 1 af.Description
        FROM [dbo.LMS].TRN_ActivityFeatures af WITH (NOLOCK)
        WHERE af.ActivityId = trail.id
          AND af.featureId  =  7
    )                                        AS Apresentacao,
    (
        SELECT TOP 1 af.Description
        FROM [dbo.LMS].TRN_ActivityFeatures af WITH (NOLOCK)
        WHERE af.ActivityId = trail.id
          AND af.featureId  =  8
    )                                        AS Objetivos,
    (
        SELECT TOP 1 af.Description
        FROM [dbo.LMS].TRN_ActivityFeatures af WITH (NOLOCK)
        WHERE af.ActivityId = trail.id
          AND af.featureId  =  9
    )                                        AS Conteudo_programatico,
    (
        SELECT TOP 1 af.Description
        FROM [dbo.LMS].TRN_ActivityFeatures af WITH (NOLOCK)
        WHERE af.ActivityId = trail.id
          AND af.featureId  = 10
    )                                        AS Nivel_de_complexidade,
    (
        SELECT TOP 1 af.Description
        FROM [dbo.LMS].TRN_ActivityFeatures af WITH (NOLOCK)
        WHERE af.ActivityId = trail.id
          AND af.featureId  = 11
    )                                        AS Termo_de_Aceite,
    (
        SELECT TOP 1 af.Description
        FROM [dbo.LMS].TRN_ActivityFeatures af WITH (NOLOCK)
        WHERE af.ActivityId = trail.id
          AND af.featureId  = 12
    )                                        AS Ficha_tecnica,
    (
        SELECT TOP 1 af.Description
        FROM [dbo.LMS].TRN_ActivityFeatures af WITH (NOLOCK)
        WHERE af.ActivityId = trail.id
          AND af.featureId  = 13
    )                                        AS Requisitos,
    (
        SELECT TOP 1 af.Description
        FROM [dbo.LMS].TRN_ActivityFeatures af WITH (NOLOCK)
        WHERE af.ActivityId = trail.id
          AND af.featureId  = 14
    )                                        AS Criterios_de_avaliacao,
    (
        SELECT TOP 1 af.Description
        FROM [dbo.LMS].TRN_ActivityFeatures af WITH (NOLOCK)
        WHERE af.ActivityId = trail.id
          AND af.featureId  = 15
    )                                        AS Publico,
    (
        SELECT TOP 1 af.Description
        FROM [dbo.LMS].TRN_ActivityFeatures af WITH (NOLOCK)
        WHERE af.ActivityId = trail.id
          AND af.featureId  = 16
    )                                        AS Carga_Horaria,
    (
        SELECT TOP 1 af.Description
        FROM [dbo.LMS].TRN_ActivityFeatures af WITH (NOLOCK)
        WHERE af.ActivityId = trail.id
          AND af.featureId  = 17
    )                                        AS Area_subarea
FROM
    [dbo.LMS].TRN_Activities_Trail AS trail WITH (NOLOCK)
INNER JOIN
    [dbo.LMS].TRN_Activities       AS act   WITH (NOLOCK)
    ON act.id = trail.id;
