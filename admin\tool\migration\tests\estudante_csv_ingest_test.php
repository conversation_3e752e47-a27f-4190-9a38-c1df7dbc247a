<?php

namespace tool_migration;

use tool_migration\importers\readers\csv_reader;
use tool_migration\models\estudante;

final class estudante_csv_ingest_test extends \advanced_testcase {
    
    /**
     * @group !current
     *
     * @return void
     */
    public function test_estudante_upsert_from_csv(): void {
        global $DB;

        $this->resetAfterTest(true);

        $filepath = __DIR__ . '/fixtures/data/test-usuarios.csv';

        $reader = new csv_reader($filepath);

        foreach ($reader->read() as $row) {
            estudante::upsert_from_csv($row);
        }

        $records = $DB->get_records(estudante::TABLE);
        $this->assertCount(12, $records, '12 students on the CSV');

        $elias = estudante::get_by_identifier(21);
        $this->assertInstanceOf(estudante::class, $elias);
        $this->assertEquals('<PERSON>', $elias->get('source_nome'));
        $this->assertEquals('<EMAIL>', $elias->get('source_email'));
        $this->assertEquals('DF', $elias->get('source_estado'));
        $this->assertEquals(21, (int)$elias->get('source_codaluno'));
    }

    /**
     * @group !currents
     *
     * @return void
     */
    public function test_estudante_upsert_with_hash_comparison(): void {
        global $DB;

        $this->resetAfterTest(true);

        $filepath = __DIR__ . '/fixtures/data/test-usuarios.csv';

        $reader = new csv_reader($filepath);

        foreach ($reader->read() as $row) {
            estudante::upsert_from_csv($row);
        }

        $this->assertCount(12, $DB->get_records(estudante::TABLE));

        $reader2 = new csv_reader($filepath);
        foreach ($reader2->read() as $row) {
            $result = estudante::upsert_from_csv($row);
            $this->assertFalse($result);
        }

        $updatedRow = null;
        $reader3 = new csv_reader($filepath);
        foreach ($reader3->read() as $row) {
            if ($row['source_codaluno'] == 21) {
                $row['source_nome'] = 'Elias A. O. dos Santos'; // alteração
                $updatedRow = $row;
                break;
            }
        }

        $this->assertNotNull($updatedRow, 'Linha com codaluno 21 não encontrada');
        $result = estudante::upsert_from_csv($updatedRow);
        $this->assertTrue($result, 'Registro deveria ter sido atualizado');

        $elias = estudante::get_by_identifier(21);
        $this->assertEquals('Elias A. O. dos Santos', $elias->get('source_nome'));
        $this->assertEquals(1, (int) $elias->get('needs_update'));
    }
}
