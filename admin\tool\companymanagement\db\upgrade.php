<?php
defined('MOODLE_INTERNAL') || die();

/**
 * Upgrade the companymanagement plugin.
 *
 * @param int $oldversion The old version of the companymanagement plugin
 * @return bool
 */
function xmldb_tool_companymanagement_upgrade($oldversion)
{
    global $CFG, $DB;

    $dbman = $DB->get_manager();

    if ($oldversion < 2025010701) {

        // Define field deleted to be added to tool_companymanagement.
        $table = new xmldb_table('tool_companymanagement');
        $field = new xmldb_field('deleted', XMLDB_TYPE_INTEGER, '1', null, XMLDB_NOTNULL, null, '0', 'timemodified');

        // Conditionally launch add field deleted.
        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        // Companymanagement savepoint reached.
        upgrade_plugin_savepoint(true, 2025010701, 'tool', 'companymanagement');
    }

    if ($oldversion < 2025052300) {
        // Define index cnpj_idx (not unique) to be added to tool_companymanagement.
        $table = new xmldb_table('tool_companymanagement');
        $index = new xmldb_index('cnpj_idx', XMLDB_INDEX_NOTUNIQUE, ['cnpj']);

        // Conditionally launch add index cnpj_idx.
        if (!$dbman->index_exists($table, $index)) {
            $dbman->add_index($table, $index);
        }

        // Companymanagement savepoint reached.
        upgrade_plugin_savepoint(true, 2025052300, 'tool', 'companymanagement');
    }

    if ($oldversion < 2025052301) {        
        $table = new xmldb_table('tool_companymanagement');

        // Changing nullability of field name on table tool_companymanagement to null.
        $field = new xmldb_field('name', XMLDB_TYPE_CHAR, '150', null, null, null, null, 'cnpj');
        $dbman->change_field_notnull($table, $field);

        // Changing nullability of field state on table tool_companymanagement to null.
        $field = new xmldb_field('state', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'name');
        $dbman->change_field_notnull($table, $field);

        // Companymanagement savepoint reached.
        upgrade_plugin_savepoint(true, 2025052301, 'tool', 'companymanagement');
    }

    return true;
}
