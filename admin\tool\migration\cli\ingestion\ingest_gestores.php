<?php

use tool_migration\models\gestor;

define("CLI_SCRIPT", true);
require_once(__DIR__ . '/../../../../../config.php');

raise_memory_limit(MEMORY_HUGE);

$filepath = __DIR__ . '/data/gestores.csv';
$reader = new tool_migration\importers\readers\csv_reader($filepath);

$counter = 0;
foreach ($reader->read() as $row) {
    try {
        $counter += (int) gestor::upsert_from_csv($row);
    } catch (\Throwable $th) {
        var_dump($th);
        break;
        mtrace($th);
    }
}

print_r("\nATUALIZADOS: $counter");