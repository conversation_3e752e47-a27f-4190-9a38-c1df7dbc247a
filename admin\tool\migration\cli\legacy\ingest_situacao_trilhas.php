<?php

use tool_migration\models\progresso_trilha;

require_once(__DIR__ . '/config.php');

foreach (progresso_trilha::get_pending_legacy_upserts($limit) as $progresso_trilha) {
    try {
        $record = $progresso_trilha->to_record();

        $legacyid = \local_legacy\models\situacao_trilha::upsert_from_migration_table_data($record);

        if($legacyid){
            $progresso_trilha->set('instanceid', $legacyid);
            $progresso_trilha->set('needs_update', 0);
            $progresso_trilha->save();

            $identifier = $progresso_trilha->get('source_id');
            mtrace("Situação de usuário na trilha $identifier importado para o local_legacy");
        }
    } catch (\Throwable $th) {
        mtrace($th->getMessage());
    }
}