<style>
	body.format-gallery.path-course .courseinfo-box{
		min-height: auto !important;
	}

	body.format-gallery #menubar#menubar-mobile:not(.expanded), 
	body.format-gallery #menubar-mobile#menubar-mobile:not(.expanded) {
		z-index: 100;
	}
</style>

<div class="course-info ">
	<div class="courseimage overflow-hidden">
		<img src="{{image}}" {{#coverposition}}style="object-position:{{.}}"{{/coverposition}}>
	</div>
	
	<div class="courseinfo-box w-100">
		<div class="courselogo">
			<p class="display-4 mb-0">{{fullnamedisplay}}</p>
		</div>
				
		{{#showsummary}}
		<div class="lead mb-4">{{{summary}}}</div>
		{{/showsummary}}
		
		<div class="mt-4">
			<form method="get" action="" class="filters">
				<input type="hidden" name="id" value="{{id}}">

				<div class="d-flex flex-column flex-md-row align-items-stretch align-items-md-end">
					<div class="mr-3">
						<label for="input-search" class="form-label d-block small">Buscar:</label>
						<input type="text" class="form-control rounded-sm" id="input-search" name="activitysearch" placeholder="Buscar..." value="{{searchterm}}" />
					</div> 
					<div class="text-left mr-0 mr-md-3 mb-2 mb-md-0">
						<label for="input-section" class="form-label d-block small">Categoria:</label>
						<select class="custom-select rounded-sm w-100" id="input-section" name="section">
							<option value="">Todos</option>
							{{#sections}}
								<option value="{{num}}" {{#selected}}selected{{/selected}}>{{name}}</option>
							{{/sections}}
						</select>
					</div> 
					{{#filters}}
					<div class="text-left mr-0 mr-md-3 mb-2 mb-md-0">
						<label for="input-nivel" class="form-label d-block small">Nível ocupacional:</label>
						<select class="custom-select rounded-sm w-100" id="input-nivel" name="nivel_ocupacional">
							<option value="">Todos</option>
							{{#niveis_ocupacionais}}
								<option value="{{value}}" {{#selected}}selected{{/selected}}>{{value}}</option>
							{{/niveis_ocupacionais}}
						</select>
					</div>
					<div class="text-left mr-0 mr-md-3 mb-2 mb-md-0">
						<label for="input-uf" class="form-label d-block small">UF:</label>
						<select class="custom-select rounded-sm w-100" id="input-uf" name="uf">
							<option value="">Todas</option>
							{{#uf_list}}
								<option value="{{key}}" {{#selected}}selected{{/selected}}>{{value}}</option>
							{{/uf_list}}
						</select>
					</div>
										<div class="text-left mr-0 mr-md-3 mb-2 mb-md-0">
						<label for="input-formato" class="form-label d-block small">Formato de Solução:</label>
						<select class="custom-select rounded-sm w-100" id="input-formato" name="solution_format">
							<option value="">Todos </option>
							{{#formato_solucao}}
								<option value="{{value}}" {{#selected}}selected{{/selected}}>{{value}}</option>
							{{/formato_solucao}}
						</select>
					</div>
					<div>
						<a href="{{viewurl}}" class="btn btn-dark mt-2 mt-md-0"><i class="fa-solid fa-filter-circle-xmark pr-1"></i> Limpar</a>
					</div>
					{{/filters}}
				</div>
			</form>

			<!-- Abas de navegação -->
			<div class="course-tabs mt-4">
				<ul class="nav nav-tabs" id="courseTabs" role="tablist">
					<li class="nav-item">
						<a class="nav-link " 
						   href="#"
						   data-section=""
						   role="tab">
							Todos
						</a>
					</li>
					{{#sections}}
					<li class="nav-item">
						<a class="nav-link {{#selected}}active{{/selected}}" 
						   href="#"
						   data-section="{{num}}"
						   role="tab">
							{{name}}
						</a>
					</li>
					{{/sections}}
				</ul>
			</div>
		</div>
	</div>
</div>

{{#js}}
	require(['jquery'], function($){
		$("#page-enrol-index .box.generalbox")
			.wrapAll("<div class='enrolmethods-wrapper'></div>");

		$(".filters").on("change", "select", function(){
			this.form.submit();
		});

		// Seleciona a primeira aba automaticamente se nenhuma estiver selecionada
		function selectFirstTab() {
			// Verifica se já existe uma seção selecionada
			var currentSection = $('#input-section').val();
			
			// Se não houver seção selecionada, seleciona a aba "Todos"
			if (currentSection === '') {
				var $todosTab = $('#courseTabs .nav-link').first();
				
				// Apenas atualiza as classes visuais, sem submeter o formulário
				if ($todosTab.length) {
					$('#courseTabs .nav-link').removeClass('active');
					$todosTab.addClass('active');
				}
			}
		}

		// Adiciona comportamento das abas
		$('#courseTabs .nav-link').on('click', function(e) {
			e.preventDefault();
			var $this = $(this);
			
			// Remove active de todas as abas
			$('#courseTabs .nav-link').removeClass('active');
			// Adiciona active na aba clicada
			$this.addClass('active');
			
			// Verifica se a seção selecionada é diferente da atual
			var currentSection = $('#input-section').val();
			var newSection = $this.data('section');
			
			if (currentSection !== newSection) {
				// Atualiza o select de categoria
				$('#input-section').val(newSection);
				
				// Submete o formulário manualmente
				$('.filters').submit();
			}
		});

		// Executa a seleção da primeira aba quando a página carrega
		$(document).ready(function() {
			selectFirstTab();
		});
	})
{{/js}}