{"name": "tool_migration", "version": "1.0.0", "description": "", "main": "", "directories": {"test": "tests"}, "scripts": {"test": "cd ../../../ && php ./vendor/bin/phpunit --testsuite tool_migration_testsuite --exclude skip --testdox-html admin/tool/migration/tests/tests.html", "testcurrent": "cd ../../../ && php ./vendor/bin/phpunit --testsuite tool_migration_testsuite --group current", "initphpunit": "cd ../../../ && php admin/tool/phpunit/cli/init.php", "upgrade": "cd ../../cli/ && php upgrade.php --non-interactive"}, "author": "", "license": ""}