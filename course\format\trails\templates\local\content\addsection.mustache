{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/content/addsection

    Displays the add section button inside a course.

    Example context (json):
    {
        "showaddsection": true,
        "id": 42,
        "insertafter": true,
        "num": 0,
        "increase": {
            "url": "#"
        },
        "decrease": {
            "url": "#"
        },
        "addsections": {
            "url": "#",
            "title": "Add section",
            "newsection": 3
        }
    }
}}
{{#showaddsection}}
<div class="mdl-left pt-3 changenumsections bulk-hidden border-top">
    {{#increase}}
    <a href="{{{url}}}" class="increase-sections">
        {{#pix}}t/switch_plus, moodle, {{#str}} increasesections, moodle {{/str}}{{/pix}}
    </a>
    {{/increase}}
    {{#decrease}}
    <a href="{{{url}}}" class="reduce-sections">
        {{#pix}}t/switch_minus, moodle, {{#str}} reducesections, moodle {{/str}}{{/pix}}
    </a>
    {{/decrease}}
    {{#addsections}}
    <a
        href="{{{url}}}"
        class="add-sections btn bg-glassy text-center"
        data-add-sections="{{title}}"
        data-new-sections="{{newsection}}"
        data-action="addSection"
        {{#insertafter}} data-id="{{id}}" {{/insertafter}}
    >
        {{title}}
    </a>
    {{/addsections}}
</div>
{{/showaddsection}}
