SELECT
    Id AS codturma,
    name                        AS nometurma,
    Period_From                 AS datainicio,
    Period_To                   AS datafim,
    Vacancies                   AS numeromaximoalunos,
    ActivityCollectionId        AS codtrilha,
    RegistrationDate            AS criado,
    ModificationDate            AS modificado,
    Active                      AS disponivel,
    Description                 AS descricao,
    PreSubscription_Period_From AS datainicioprematricula
FROM [dbo.LMS].TRN_ActivityCollectionClasses;