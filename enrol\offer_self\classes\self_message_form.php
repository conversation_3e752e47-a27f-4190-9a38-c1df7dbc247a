<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace enrol_offer_self;

use moodleform;
use local_offermanager\persistent\offer_class_model;

defined('MOODLE_INTERNAL') || die();

/**
 * Class self_message_form
 *
 * @package    enrol_offer_self
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class self_message_form extends moodleform
{

    public function definition()
    {
        $mform = $this->_form;
        $data = $this->_customdata;

        $offerclass =  offer_class_model::get_record(['id' => $data->offerclassid]);
        $instance = $offerclass->get_enrol_instance();

        $description = $offerclass->get_description_html();
        $enrolperiod_str = $offerclass->get_formated_enrol_period();

        $plugin = $offerclass->get_plugin();

        $heading = $plugin->get_instance_name($instance);

        $mform->addElement('header', 'selfheader', $heading);

        if ($description) {
            $mform->addElement(
                'static',
                'description',
                get_string('description'),
                $description
            );
        }

        $mform->addElement(
            'static',
            'enrolperiod',
            get_string('enrolperiod', 'local_offermanager'),
            $enrolperiod_str
        );

        $mform->addElement('static', 'info', '', $data->info);
        $mform->closeHeaderBefore('info');
    }
}
