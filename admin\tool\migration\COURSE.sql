SELECT
    c.CODCURSO AS source_id,
    c.NOME AS source_nome,
    CASE 
        WHEN c.NOME LIKE '%v[0-9]' COLLATE Latin1_General_CI_AI
            AND RIGHT(c.NOME, 2) LIKE 'v%' COLLATE Latin1_General_CI_AI
            THEN RIGHT(c.NOME, 2)
        ELSE NULL
    END AS source_version,
    c.CODGRUPO AS source_codgrupo,
    c.IDCURSO AS source_idcurso,
    c.DISPONIVEL AS source_disponivel,
    c.NOMECURSOMENU AS source_nomecursomenu,
    c.<PERSON>AZ<PERSON> AS source_prazo,
    c.CRIADO AS source_criado,
    c.<PERSON><PERSON><PERSON> AS source_modificado,
    c.CODUSUARIOGESTOR AS source_codusuariogestor,
    c.STATUS AS source_status,
    c.CURSOPRESENCIAL AS source_cursopresencial,
    c.CARGAHORARIA AS source_cargahoraria,
    c.ExibeNoCatalogo AS source_exibenocatalogo,
    c.REMATRICULA AS source_rematricula,
    c.<PERSON><PERSON>QUE<PERSON><PERSON> AS source_frequencia,
    c.<PERSON>ERMI<PERSON>R<PERSON><PERSON>TRICULA AS source_permitirrematricula,
    c.<PERSON>TER<PERSON>L<PERSON><PERSON>IMOREMATRICULA AS source_intervalominimorematricula,
    c.POSSUIRECICLAGEM AS source_possuireciclagem,
    c.NUMDIASRECICLAGEM AS source_numdiasreciclagem,
    c.ACESSOAPOSPRAZO AS source_acessoaposprazo,
    c.CARGAHORARIOCURSO AS source_cargahorariocurso,
    c.CODCURSOEXTERNO AS source_codcursoexterno,
    c.EXIBEAVISOFIMCURSO AS source_exibeavisofimcurso,
    c.DEFINEMEDIAAPROVACAOTURMA AS source_definemediaaprovacaoturma,
    c.DESCRICAO AS source_descricao,
    c.TEMCARGAHORARIAMIN AS source_temcargahorariamin,
    c.CARGAHORARIAMIN AS source_cargahorariamin,
    c.Keywords AS source_keywords,
    c.CodEmpresaFornecedor AS source_codempresafornecedor,
    dc.DegreedId AS source_degreedid,
    c.CODSISTEMALEGADO AS source_codsistemalegado

    -- Características 
    (SELECT cc.DESCRICAO FROM dbUcSebrae.dbo.CURSOCARACTERISTICA cc WHERE cc.CODCURSO = c.CODCURSO AND cc.CODCARACT = 10132) AS source_apresentacao,
    (SELECT cc.DESCRICAO FROM dbUcSebrae.dbo.CURSOCARACTERISTICA cc WHERE cc.CODCURSO = c.CODCURSO AND cc.CODCARACT = 10133) AS source_objetivos,
    (SELECT cc.DESCRICAO FROM dbUcSebrae.dbo.CURSOCARACTERISTICA cc WHERE cc.CODCURSO = c.CODCURSO AND cc.CODCARACT = 10134) AS source_conteudo_programatico,
    (SELECT cc.DESCRICAO FROM dbUcSebrae.dbo.CURSOCARACTERISTICA cc WHERE cc.CODCURSO = c.CODCURSO AND cc.CODCARACT = 10135) AS source_nivel_complexidade,
    (SELECT cc.DESCRICAO FROM dbUcSebrae.dbo.CURSOCARACTERISTICA cc WHERE cc.CODCURSO = c.CODCURSO AND cc.CODCARACT = 10136) AS source_termo_aceite,
    (SELECT cc.DESCRICAO FROM dbUcSebrae.dbo.CURSOCARACTERISTICA cc WHERE cc.CODCURSO = c.CODCURSO AND cc.CODCARACT = 10137) AS source_ficha_tecnica,
    (SELECT cc.DESCRICAO FROM dbUcSebrae.dbo.CURSOCARACTERISTICA cc WHERE cc.CODCURSO = c.CODCURSO AND cc.CODCARACT = 10138) AS source_requisitos,
    (SELECT cc.DESCRICAO FROM dbUcSebrae.dbo.CURSOCARACTERISTICA cc WHERE cc.CODCURSO = c.CODCURSO AND cc.CODCARACT = 10139) AS source_criterios_avaliacao,
    (SELECT cc.DESCRICAO FROM dbUcSebrae.dbo.CURSOCARACTERISTICA cc WHERE cc.CODCURSO = c.CODCURSO AND cc.CODCARACT = 10140) AS source_publico,
    (SELECT cc.DESCRICAO FROM dbUcSebrae.dbo.CURSOCARACTERISTICA cc WHERE cc.CODCURSO = c.CODCURSO AND cc.CODCARACT = 10141) AS source_carga_horaria,
    (SELECT cc.DESCRICAO FROM dbUcSebrae.dbo.CURSOCARACTERISTICA cc WHERE cc.CODCURSO = c.CODCURSO AND cc.CODCARACT = 10142) AS source_area_subarea

FROM 
    dbUcSebrae.dbo.CURSOS c
LEFT JOIN 
    dbUcSebrae.dbo.DegreedContent dc 
    ON dc.SolutionId = c.CODCURSO
    AND dc.LMSType = 'course'
    AND dc.Deleted = 0;
