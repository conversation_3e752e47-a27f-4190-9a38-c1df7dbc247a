<?php namespace tool_migration\models;

defined('MOODLE_INTERNAL') || die();

class segmento_turma_curso extends abstract_migratable_entity {
    const TABLE = 'eadtech_seg_turmas_curso';

    public static function define_identifier(): string {
        throw new \coding_exception("eadtech_seg_turmas_curso is a relationship table");
    }

    public function get_idnumber(): string {
        return md5('LEGADO-' . $this->get('source_codturma') . '-' . $this->get('source_codsegmento'));
    }

    protected static function define_model_properties(): array {
        return [
            'source_codturma' => [
                'type' => PARAM_INT,
            ],
            'source_codsegmento' => [
                'type' => PARAM_INT,
            ],
            'source_codprogramaturma' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
        ];
    }

    public function to_legacy(): object {
        $record = (object)[
            'codturma' => $this->get('source_codturma'),
            'codsegmento' => $this->get('source_codsegmento'),
            'codprogramaturma' => $this->get('source_codprogramaturma'),
        ];

        if ($id = $this->get('instanceid')) {
            $record->id = $id;
        }

        return $record;
    }
}
