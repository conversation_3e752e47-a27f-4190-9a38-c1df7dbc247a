<?php namespace tool_migration\services;

use coding_exception;
use tool_migration\models\abstract_migratable_entity;

/**
 * Cache for mapping original entities (source_id) to their Moodle instance IDs.
 */
class migratable_entity_map {

    /** [classname => [source_id => instanceid|null],] */
    protected static array $map = [];

    /**
     * Get the Moodle instance ID for a given migratable entity and source ID.
     *
     * @param class-string<abstract_migratable_entity> $classname Fully-qualified class name of the entity.
     * @param int $source_id The ID from the external/source system.
     * @return int|null The Moodle instance ID, or null if not found.
     * @throws coding_exception If the class is not a subclass of abstract_migratable_entity.
     */
    public static function get_instanceid(string $classname, int $source_id): ?int {
        global $DB;

        if (!is_subclass_of($classname, abstract_migratable_entity::class)) {
            throw new coding_exception("Class $classname must extend abstract_migratable_entity.");
        }

        // Check if already cached
        if (isset(self::$map[$classname][$source_id])) {
            return self::$map[$classname][$source_id];
        }

        // Attempt to load from DB
        $instanceid = $DB->get_field($classname::TABLE, 'instanceid', ['source_id' => $source_id],) ?: null;

        // Cache result (even null)
        if(!isset(self::$map[$classname])){
            self::$map[$classname] = [];
        }
        self::$map[$classname][$source_id] = $instanceid;

        return $instanceid;
    }

    /**
     * Manually set a mapping in the cache.
     *
     * @param class-string<abstract_migratable_entity> $classname
     * @param int $source_id
     * @param int|null $instanceid
     */
    public static function set_instanceid(string $classname, int $source_id, ?int $instanceid): void {
        self::$map[$classname][$source_id] = $instanceid;
    }

    /**
     * Clears cache
     */
    public static function reset(?string $classname = null): void {
        if($classname === null){
            self::$map = [];
            return;
        }

        self::$map[$classname] = [];
    }
}
