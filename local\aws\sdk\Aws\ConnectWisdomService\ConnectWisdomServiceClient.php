<?php
namespace Aws\ConnectWisdomService;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Connect Wisdom Service** service.
 * @method \Aws\Result createAssistant(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAssistantAsync(array $args = [])
 * @method \Aws\Result createAssistantAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAssistantAssociationAsync(array $args = [])
 * @method \Aws\Result createContent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createContentAsync(array $args = [])
 * @method \Aws\Result createKnowledgeBase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createKnowledgeBaseAsync(array $args = [])
 * @method \Aws\Result createSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSessionAsync(array $args = [])
 * @method \Aws\Result deleteAssistant(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAssistantAsync(array $args = [])
 * @method \Aws\Result deleteAssistantAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAssistantAssociationAsync(array $args = [])
 * @method \Aws\Result deleteContent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteContentAsync(array $args = [])
 * @method \Aws\Result deleteKnowledgeBase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteKnowledgeBaseAsync(array $args = [])
 * @method \Aws\Result getAssistant(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAssistantAsync(array $args = [])
 * @method \Aws\Result getAssistantAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAssistantAssociationAsync(array $args = [])
 * @method \Aws\Result getContent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getContentAsync(array $args = [])
 * @method \Aws\Result getContentSummary(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getContentSummaryAsync(array $args = [])
 * @method \Aws\Result getKnowledgeBase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getKnowledgeBaseAsync(array $args = [])
 * @method \Aws\Result getRecommendations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getRecommendationsAsync(array $args = [])
 * @method \Aws\Result getSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSessionAsync(array $args = [])
 * @method \Aws\Result listAssistantAssociations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAssistantAssociationsAsync(array $args = [])
 * @method \Aws\Result listAssistants(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAssistantsAsync(array $args = [])
 * @method \Aws\Result listContents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listContentsAsync(array $args = [])
 * @method \Aws\Result listKnowledgeBases(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listKnowledgeBasesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result notifyRecommendationsReceived(array $args = [])
 * @method \GuzzleHttp\Promise\Promise notifyRecommendationsReceivedAsync(array $args = [])
 * @method \Aws\Result queryAssistant(array $args = [])
 * @method \GuzzleHttp\Promise\Promise queryAssistantAsync(array $args = [])
 * @method \Aws\Result removeKnowledgeBaseTemplateUri(array $args = [])
 * @method \GuzzleHttp\Promise\Promise removeKnowledgeBaseTemplateUriAsync(array $args = [])
 * @method \Aws\Result searchContent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise searchContentAsync(array $args = [])
 * @method \Aws\Result searchSessions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise searchSessionsAsync(array $args = [])
 * @method \Aws\Result startContentUpload(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startContentUploadAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateContent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateContentAsync(array $args = [])
 * @method \Aws\Result updateKnowledgeBaseTemplateUri(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateKnowledgeBaseTemplateUriAsync(array $args = [])
 */
class ConnectWisdomServiceClient extends AwsClient {}
