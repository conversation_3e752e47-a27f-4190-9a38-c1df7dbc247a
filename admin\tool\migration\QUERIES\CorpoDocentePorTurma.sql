-- CURSOS
SELECT
    TA.CODTURMA,
    TA.<PERSON><PERSON><PERSON><PERSON>,
    TA.DATAALOCACAO,
    CD.CARGO
FROM TURMAS_CORPO AS TA
    LEFT JOIN CORPODOCENTE AS CD ON CD.CODCORPO = TA.CODCORPO

-- TRILHAS
SELECT
  ACT.ID AS codtrilha,
  class.id AS codturma,
  CD.CODCORPO AS codcorpo
FROM [dbo.LMS].TRN_CoachActivityCollectionClass AS CACC
INNER JOIN [dbo.LMS].USR_Users_Coach AS UC ON (UC.Id = CACC.Coaches_Id)
INNER JOIN CORPODOCENTE AS CD ON (CD.CODCORPO = UC.LegacyCoachId)
INNER JOIN [dbo.LMS].TRN_ActivityCollectionClasses AS class ON (class.id = CACC.ActivityCollectionClasses_Id)
INNER JOIN [dbo.LMS].TRN_Activities AS ACT ON (ACT.ID = CACC.ActivityCollectionClasses_ActivityCollectionId);