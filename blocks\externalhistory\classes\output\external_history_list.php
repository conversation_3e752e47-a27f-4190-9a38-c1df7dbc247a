<?php

namespace block_externalhistory\output;

use renderable;
use templatable;
use renderer_base;
use block_externalhistory\models\external_history;

class external_history_list implements renderable, templatable {

    protected int $userid;
    /** @var external_history[] */
    protected array $histories;

    public function __construct(int $userid, int $limit = 0) {
        $this->userid = $userid;
        $this->histories = external_history::list_user_histories($userid, $limit);
    }

    /**
     * Export data for Mustache template.
     *
     * @param renderer_base $output
     * @return array Template data
     */
    public function export_for_template(renderer_base $output): array {
        return [
            'cards' => array_map(
                fn($history) => $history->export_for_template($output),
                $this->histories
            ,)
        ];
    }
}
