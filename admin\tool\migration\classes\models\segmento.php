<?php namespace tool_migration\models;

use tool_migration\util\ingestion_helper;

defined('MOODLE_INTERNAL')||die();

class segmento extends abstract_migratable_entity{
    const TABLE = 'eadtech_segmentos';

    public static function define_identifier(): string {
        return 'source_codsegmento';
    }
    
    protected static function define_model_properties(): array{
        return [
            'source_codsegmento' => [
                'type' => PARAM_INT,
            ],
            'source_nomesegmento' => [
                'type' => PARAM_TEXT,
            ],
            'source_criado' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_modificado' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_codusuariogestor' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
        ];
    }

    public function get_idnumber(): string{
        return 'LEGADO-' . $this->get('source_codsegmento');
    }

    public function to_audience(): object {
        global $USER;

        $audience = (object)[
            'name' => $this->get('source_nomesegmento'),
            'timecreated' => ingestion_helper::str_to_timestamp($this->get('source_criado')),
            'timemodified' => time(),
            'usermodified' => $USER->id,
            'cronlastruntime' => time(),
        ];

        if($id = $this->get('instanceid')){
            $audience->id = $id;
        }

        return $audience;
    }
}
