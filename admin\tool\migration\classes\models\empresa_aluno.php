<?php namespace tool_migration\models;

use tool_migration\util\ingestion_helper;

defined('MOODLE_INTERNAL') || die();

class empresa_aluno extends abstract_migratable_entity {
    const TABLE = 'eadtech_empresas_alunos';

    public static function define_identifier(): string {
        return 'source_codempresa_aluno';
    }

    public function get_idnumber(): string{
        return 'LEGADO-' . $this->get('source_codempresa_aluno');
    }

    protected static function define_model_properties(): array {
        return [
            'source_codempresa_aluno' => [
                'type' => PARAM_INT,
            ],
            'source_codaluno' => [
                'type' => PARAM_INT,
            ],
            'source_codempresa' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_datacadastro' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_excluido' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_empresaprincipal' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_datainclusao' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_datamodificacao' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
        ];
    }

    public function to_legacy(): object {
        $record = (object)[
            'codaluno' => $this->get('source_codaluno'),
            'codempresa' => $this->get('source_codempresa'),
            'datacadastro' => ingestion_helper::str_to_timestamp($this->get('source_datacadastro')),
            'excluido' => ingestion_helper::str_to_bool($this->get('source_excluido')),
            'empresaprincipal' => ingestion_helper::str_to_bool($this->get('source_empresaprincipal')),
            'datainclusao' => ingestion_helper::str_to_timestamp($this->get('source_datainclusao')),
            'datamodificacao' => ingestion_helper::str_to_timestamp($this->get('source_datamodificacao')),
        ];

        if($id = $this->get('instanceid')){
            $record->id = $id;
        }

        return $record;
    }
}
