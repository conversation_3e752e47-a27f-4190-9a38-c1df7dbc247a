USE [dbUc<PERSON><PERSON><PERSON><PERSON><PERSON>];
GO

SELECT
    T.CODTURMA                           AS source_id,
    T.NOMETURMA                          AS source_nometurma,
    T.NOMETURMAMENU                      AS source_nometurmamenu,
    T.DATAINIC<PERSON>                         AS source_datainicio,
    T.<PERSON><PERSON>                            AS source_datafim,
    T.<PERSON><PERSON>IMOALUNOS                 AS source_numeromaximoalunos,
    T.CODCURSO                           AS source_codcurso,
    T.CRIADO                             AS source_criado,
    T.DISPONIVEL                         AS source_disponivel,
    T.MODIFICADO                         AS source_modificado,
    T.CODUSUARIOGESTOR                   AS source_codusuariogestor,
    T.LIVRE                              AS source_livre,
    T.LOCALTURMA                         AS source_localturma,
    T.COD_CURSO_PEOPLE                   AS source_cod_curso_people,
    T.COD_TURMA_PEOPLE                   AS source_cod_turma_people,
    T.COD_UNID_NEG                       AS source_cod_unid_neg,
    T.DEPENDENCIA                        AS source_dependencia,
    T.STATUS                             AS source_status,
    T.<PERSON>AT<PERSON><PERSON><PERSON><PERSON><PERSON>ENTO                AS source_datalimite<PERSON><PERSON>nto,
    T.<PERSON><PERSON><PERSON><PERSON><PERSON>                AS source_datalimiteinscricao,
    T.<PERSON>                          AS source_descricao,
    T.AUTOMATICA                         AS source_automatica,
    T.PREMATRICULAAPROVAAUTOMATICA       AS source_prematriculaaprovaautomatica,
    T.SEGUIRFLUXOAPROVACAO               AS source_seguirfluxoaprovacao,
    T.PREMATRICULA                       AS source_prematricula,
    T.VALIDAPREMATRICULA                 AS source_validaprematricula,
    T.ObedeceInicioSugCur                AS source_obedeceiniciosugcur,
    T.AcessoSomenteLeitura               AS source_acessosomenteleitura,
    T.CODCOORDENACAO                     AS source_codcoordenacao,
    T.DATAVENDASHOPPING                  AS source_datavendashopping,
    T.DATAINICIOPREMATRICULA             AS source_datainicioprematricula,
    T.DATAFIMPREMATRICULA                AS source_datafimprematricula,
    T.PERMITIRFILAESPERA                 AS source_permitirfilaespera,
    T.CARGAHORARIA                       AS source_cargahoraria,
    T.NUMDIASRECICLAGEM                  AS source_numdiasreciclagem,
    T.CAMPOPARAORDENACAOBIBLIOTECA       AS source_campoparaordenacaobiblioteca,
    T.COMUNIDADEGRUPOTRABALHO            AS source_comunidadegrupotrabalho,
    T.CODDONOCOMGRUPOTRABALHO            AS source_coddonocomgrupotrabalho,
    T.CADASTROLOTE                       AS source_cadastrolote,
    T.ACESSOAPOSPRAZO                    AS source_acessoaposprazo,
    T.DATAFORUMTOPICOESPELHADO           AS source_dataforumtopicoespeLHado,
    T.CODTURMAPAI                        AS source_codturmapai,
    T.REPLICADATA                        AS source_replicadata,
    T.REPLICADATAPREINSCRICAO            AS source_replicadatapreinscricao,
    T.DHATUALIZACAOMANUAL                AS source_dhatualizacaomanual,
    T.MEDIA                              AS source_media,
    T.DEFINEMEDIATURMA                   AS source_definemediaturma,
    T.RECUPERACAO                        AS source_recuperacao,
    T.CompoeTrilhaDeAprendizagem         AS source_compoetrilhadeaprendizagem,
    T.PrazoDiasSugeridosTurma            AS source_prazodiasugeridosturma,
    T.ModeloEquipamentoTreinamento       AS source_modeloequipamentotreinamento,
    T.ActivityCollectionClassId          AS source_activitycollectionclassid,
    T.ActivityCollectionId               AS source_activitycollectionid,
    T.CODPERIODOACADEMICO                AS source_codperiodoacademico,
    T.CANCELADA                          AS source_cancelada,
    T.NUMEROMINIMOALUNOS                 AS source_numerominimoalunos,

    ISNULL(MC.DESCRICAO, '')             AS source_motivo_cancelamento,
    T.TIPOTURMA                          AS source_tipoturma,

    ISNULL(E.SIGLA,   '')                AS source_estado,
    ISNULL(P.DESCRICAO,'')               AS source_pais,
    ISNULL(FIL.DESCRICAO,'')             AS source_filial,
    ISNULL(O.DESCRICAO,'')               AS source_orgao

FROM [dbUcSebraeFull].[dbo].[TURMA]                              AS T
LEFT JOIN [dbUcSebraeFull].[dbo].[TURMA_MOTIVO_CANCELAMENTO] AS MC ON T.CODMOTIVO = MC.CODMOTIVO
LEFT JOIN [dbUcSebraeFull].[dbo].[ESTADOS]                    AS E  ON T.CODESTADOS = E.CODESTADOS
LEFT JOIN [dbUcSebraeFull].[dbo].[PAISES]                     AS P  ON T.CODPAIS    = P.CODPAISES
LEFT JOIN [dbUcSebraeFull].[dbo].[FILIAL]                     AS FIL ON T.CODFILIAL  = FIL.CODFILIAL
LEFT JOIN [dbUcSebraeFull].[dbo].[ORGAO]                      AS O  ON T.CODORGAO   = O.CODORGAO

ORDER BY T.CODTURMA;
GO
