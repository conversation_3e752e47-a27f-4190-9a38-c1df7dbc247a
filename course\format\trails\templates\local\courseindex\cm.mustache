{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/courseindex/cm

    Displays a course index course-module entry.

    Example context (json):
    {
        "id": "12",
        "name": "Announcements",
        "url": "#",
        "visible": 1,
        "isactive": 1,
        "uniqid": "0",
        "accessvisible": 1,
        "hascmrestrictions": 0,
        "indent": 1
    }
}}
<li class="courseindex-item courseindex-item-cm rounded pl-2 mx-2 d-flex align-items-center{{!
        }}{{#isactive}} active{{/isactive}}{{!
        }}{{#hascmrestrictions}} restrictions{{/hascmrestrictions}}{{!
        }}{{^accessvisible}} dimmed{{/accessvisible}}{{!
        }}{{#indent}} indented {{/indent}}"
    id="{{uniqid}}-course-index-cm-{{id}}"
    data-for="cm"
    data-id="{{id}}"
    role="treeitem"
>
	<a {{#url}} href="{{{url_check}}}" {{/url}}{{^url}} href="#{{{anchor}}}" data-anchor="true" {{/url}}
     	class="activityiconcontainer smaller {{purpose}} courseicon align-self-start mr-2 {{btn_show_availability_info_class}}"
	{{#onclick}}onclick="{{{onclick}}}"{{/onclick}}
    >
		{{{icon}}}
	</a>
	
    {{#uservisible}}
        <a
            class="courseindex-link text-truncate {{btn_show_availability_info_class}}"
            {{#url}} href="{{{url_check}}}" {{/url}}{{^url}} href="#{{{anchor}}}" data-anchor="true" {{/url}}
            data-for="cm_name"
            tabindex="-1"
            title="{{name}}"
            {{#onclick}}onclick="{{{onclick}}}"{{/onclick}}
        >
            {{{name}}}
        </a>
    {{/uservisible}}
	
    {{^uservisible}}
        <a 
            class="courseindex-link text-truncate {{btn_show_availability_info_class}}" 
            {{#url}} href="{{{url_check}}}" {{/url}}{{^url}} href="#{{{anchor}}}" data-anchor="true" {{/url}}
            data-for="cm_name" 
            tabindex="-1" 
            title="{{name}}"
            {{#onclick}}onclick="{{{onclick}}}"{{/onclick}}
        >
            {{{name}}}
        </a>
    {{/uservisible}}
	
    <span class="courseindex-locked ml-1" data-for="cm_name">
        {{#pix}} t/locked, core {{/pix}}
    </span>
    <span class="dragicon ml-auto">{{#pix}}i/dragdrop{{/pix}}</span>
    <span class="completioninfo ml-auto" data-for="cm_completion" data-value="NaN"></span>
    
    {{#modavailability}}
	{{$ core_courseformat/local/content/cm/availability }}
		{{> format_trails/local/content/cm/availability }}
	{{/ core_courseformat/local/content/cm/availability }}
    {{/modavailability}}

</li>
{{#js}}
require(['format_trails/local/courseindex/cm'], function(component) {
    component.init('{{uniqid}}-course-index-cm-{{id}}');
});
{{/js}}
