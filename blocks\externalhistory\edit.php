<?php
require(__DIR__ . '/../../config.php');
require_login();

use block_externalhistory\models\external_history;
use block_externalhistory\forms\externalhistory_form;
use context_user;
use moodle_url;

$id = optional_param('id', 0, PARAM_INT);

$PAGE->set_url(new moodle_url('/blocks/externalhistory/edit.php', ['id' => $id]));
$PAGE->set_context(context_system::instance());
$PAGE->set_title(get_string('editentry', 'block_externalhistory'));
$PAGE->set_heading(get_string('title', 'block_externalhistory'));

$draftitemid = file_get_submitted_draft_itemid('certfile');
$formdata = ['userid' => $USER->id, 'certfile' => $draftitemid];

if ($id) {
    $history = external_history::get_record(['id' => $id, 'userid' => $USER->id], IGNORE_MISSING);
    if (!$history) {
        throw new \moodle_exception('invalidrecord', 'block_externalhistory');
    }

    $formdata = (array)$history->to_record();

    // Copying certificate filearea to draft
    $params = $history->get_certificate_file_parameters();

    file_prepare_draft_area(
        $draftitemid,
        $params['contextid'],
        $params['component'],
        $params['filearea'],
        $params['itemid'],
        [
            'subdirs'       => false,
            'maxbytes'      => 0,
            'maxfiles'      => 1,
        ]
    );

    $formdata['certfile'] = $draftitemid;
}

$mform = new externalhistory_form($formdata);
$mform->set_data($formdata);

if ($mform->is_cancelled()) {
    redirect(new moodle_url('/blocks/externalhistory/index.php'));
}

if ($data = $mform->get_data()) {
    external_history::from_externalhistory_form($mform);
    redirect(new moodle_url('/blocks/externalhistory/index.php'),
             get_string('changessaved', 'block_externalhistory'),
             null, \core\output\notification::NOTIFY_SUCCESS);
}

echo $OUTPUT->header();
echo $mform->display();
echo $OUTPUT->footer();
