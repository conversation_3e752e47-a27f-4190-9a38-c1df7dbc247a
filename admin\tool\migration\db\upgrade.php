<?php

function xmldb_tool_migration_upgrade($oldversion) {
    global $DB;
    require_once(__DIR__ . '/upgradelib.php');
    $dbman = $DB->get_manager();

    if ($oldversion < 2025022001) {
        $tables = [
            'eadtech_estudantes',
            'eadtech_cursos',
            'eadtech_trilhas',
            'eadtech_gestores',
        ];
        foreach ($tables as $table_name) {
            $table = new xmldb_table($table_name);
            $field = new xmldb_field('legacyid', XMLDB_TYPE_INTEGER, '10', null, null, null, '0');

            if (!$dbman->field_exists($table, $field)) {
                $dbman->add_field($table, $field);
            }
        }
        
        upgrade_plugin_savepoint(true, 2025022001, 'tool', 'migration');
    }

    return true;
}
