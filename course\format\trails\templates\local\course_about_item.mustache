{{!
	{
        "aboutcourse_data": [
            {
                "index": 0,
                "shortname": "",
                "name": "",
                "data": "",
                "term": {
                    "accepted": false,
                    "tag": ""
                }
            }
        ]
    }
}}

<li id="section-about-{{ shortname }}" class="section course-section main clearfix border-bottom w-100 py-3" 
    data-sectionid="about" data-sectionreturnid="{{sectionreturnid}}" data-for="section" data-id="{{ shortname }}" data-number="{{ shortname }}">

    <div class="course-section-header d-flex" data-for="section_title" data-id="{{ shortname }}" data-number="{{ shortname }}">

        <div class="d-flex align-items-start position-relative custom-link">
            <a role="button" data-toggle="collapse" data-for="sectiontoggler" href="#coursecontentcollapseabout{{ shortname }}" id="collapssesectionabout{{ shortname }}"
                class="icons-collapse-expand justify-content-center {{#contentcollapsed}} collapsed {{/contentcollapsed}}"
                aria-expanded="{{^contentcollapsed}}true{{/contentcollapsed}}{{#contentcollapsed}}false{{/contentcollapsed}}"
                aria-controls="coursecontentcollapseabout{{ shortname }}" aria-label="Campo extra">

                <span class="expanded-icon icon-no-margin p-2" title="{{#str}} collapse, core {{/str}}">
                    {{#pix}} t/expandedchevron, core {{/pix}}
                </span>
                <span class="collapsed-icon icon-no-margin p-2" title="{{#str}} expand, core {{/str}}">
                    <span class="dir-rtl-hide">{{#pix}} t/collapsedchevron, core {{/pix}}</span>
                    <span class="dir-ltr-hide">{{#pix}} t/collapsedchevron_rtl, core {{/pix}}</span>
                </span>

                <h4 class="sectionname course-content-item d-flex align-self-stretch align-items-center ml-3 mb-0"
                    id="sectionid-about-title" data-for="section_title" data-id="{{ shortname }}" data-number="1">
                    {{ name }}
                    {{# term }}
                        <span class="ml-3">{{& tag }}</span>
                    {{/ term }}
                </h4>
            </a>
            
        </div>
    </div>

    <div id="coursecontentcollapseabout{{ shortname }}" class="content {{^sitehome}}course-content-item-content collapse {{^contentcollapsed}}show{{/contentcollapsed}}{{/sitehome}}">
        <div class="my-3 px-2">
            {{{ data }}}
        </div>
    </div>
</li>
