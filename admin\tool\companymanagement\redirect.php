<?php

/**
 * @package    tool
 * @subpackage companymanagement
 * @copyright  2025 Revvo
 */

require('../../../config.php');

$action = optional_param("action", "", PARAM_TEXT);
$url = $CFG->wwwroot . "/admin/tool/companymanagement/index.php";
$sitecontext = context_system::instance();

switch ($action) {
	case "update":
		redirect($url, get_string('message:company_updated','tool_companymanagement'));
		break;

	case "create":
		redirect($url, get_string('message:company_created','tool_companymanagement'));
		break;

	case "delete":
		redirect($url, get_string('message:company_deleted_simple','tool_companymanagement'));
		break;

	default:
		redirect($url);
		break;
}
