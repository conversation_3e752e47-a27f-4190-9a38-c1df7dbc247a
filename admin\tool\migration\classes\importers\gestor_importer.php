<?php namespace tool_migration\importers;

use tool_migration\models\gestor;

require_once($CFG->dirroot . '/user/profile/lib.php');
require_once($CFG->dirroot . '/user/lib.php');

class gestor_importer extends abstract_importer {

    public function import_pending(int $limit = 0) : void {
        foreach (gestor::get_pending_imports($limit) as $gestor) {
            try {
                if($this->process_deleted_user($gestor)){
                    continue;
                }

                if($this->find_existing_user($gestor)){
                    continue;
                }
                
                $this->upsert_user($gestor);
            } catch (\Throwable $th) {
                $username = $gestor->get_username();
                $this->output("Erro ao importar gestor \"$username\": " .$th->getMessage(), 1);
            }
        }
    }

    public function update_pending(int $limit = 0) : void {
        foreach (gestor::get_pending_updates($limit) as $gestor) {
            try {
                $this->upsert_user($gestor);
            } catch (\Throwable $th) {
                $username = $gestor->get_username();
                $this->output("Erro ao atualizar gestor \"$username\": " .$th->getMessage(), 1);
            }
        }
    }
    


    protected function find_existing_user(gestor $gestor) : bool {
        global $DB;

        $user = $DB->get_record('user', ['username' => $gestor->get_username()], 'id');
        
        if(!empty($user)){
            $gestor->set('instanceid', $user->id);
            $gestor->save();

            $username = $gestor->get_username();
            $this->output("Gestor $username relacionado com o usuário $user->id", 1);
            return true;
        }
        return false;
    }

    protected function process_deleted_user(gestor $gestor) : bool {
        if($gestor->is_deleted()){
            $gestor->set('instanceid', -1);
            $gestor->save();

            $username = $gestor->get_username();
            $this->output("Gestor $username processado como deletado", 1);
            return true;
        }
        return false;
    }

    protected function upsert_user(gestor $gestor){
        $user = $gestor->to_user();

        if(empty($user->id)){
            $user->id = user_create_user($user, false, false);
            $user->password = null;
            profile_save_data($user);
            $gestor->mark_as_imported($user->id);
            $this->output("Gestor $user->username adicionado", 1);
            return;
        }

        profile_save_data($user);
        user_update_user($user, false, true);
        $gestor->mark_as_updated();

        $this->output("Gestor $user->username atualizado", 1);
    }
}