SELECT 
    U.CODALUNO AS source_id,
    ISNULL(U.NOME, '') AS source_nome,
    ISNULL(U.EMAIL, '') AS source_email,
    ISNULL(E.SIGLA, '') AS source_estado,
    ISNULL(P.DESCRICAO, '') AS source_pais,
    ISNULL(U.SEXO, '') AS source_sexo,
    ISNULL(CONVERT(VARCHAR, U.NASCIMENTO, 120), '') AS source_nascimento,
    ISNULL(ES.DESCRICAO, '') AS source_escolaridade,
    ISNULL(EC.DESCRICAO, '') AS source_estadocivil,
    ISNULL(RE.DESCRICAO, '') AS source_renda,
    ISNULL(CONVERT(VARCHAR, U.DATACADASTRO, 120), '') AS source_datacadastro,
    ISNULL(U.EMPRESA, '') AS source_empresa,
    ISNULL(CG.DESCRICAO, '') AS source_cargo,
    ISNULL(CONVERT(VARCHAR, U.CODSEGMENTO), '') AS source_codsegmento, -- Outro em [USUARIOSSEGMENTOS]
    ISNULL(U.CPF, '') AS source_cpf,
    ISNULL(U.CAMPOLIVRE2, '') AS source_email2,
    ISNULL(U.CAMPOLIVRE3, '') AS source_celular2,
    ISNULL(U.CAMPOLIVRE4, '') AS source_cargo2,
    ISNULL(TL1.DESCRICAO, '') AS source_sexo2,
    ISNULL(TL3.DESCRICAO, '') AS source_perfilocupational,
    ISNULL(TL7.DESCRICAO, '') AS source_status2,
    ISNULL(TL9.DESCRICAO, '') AS source_estado2,
    CASE 
        WHEN U.STATUS = 'A' THEN 'ativo'
        WHEN U.STATUS = 'I' THEN 'inativo'
        ELSE 'pendente'
    END AS source_status,
    ISNULL(F.DESCRICAO, '') AS source_filial,
    U.DATAEXPIRACAO,
    ISNULL(CONVERT(VARCHAR, U.DATAEXPIRACAO, 120), '') AS source_dataexpiracao,
    ISNULL(U.CELULAR, '') AS source_celular,
    ISNULL(CONVERT(VARCHAR, U.DATAADMISSAO, 120), '') AS source_dataadmissao,
    ISNULL(CONVERT(VARCHAR, U.DataAlteracao, 120), '') AS source_dataalteracao,
    ISNULL(U.NOMESOCIAL, '') AS source_nomesocial
FROM 
    USUARIOS U WITH (NOLOCK)
LEFT JOIN ESTADOS E ON U.CODESTADOS = E.CODESTADOS
LEFT JOIN PAISES P ON U.CODPAIS = P.CODPAISES
LEFT JOIN FILIAL F ON U.CODFILIAL = F.CODFILIAL
LEFT JOIN CARGO CG ON U.CODCARGO = CG.CODCARGO
LEFT JOIN ESCOLARIDADE ES ON U.CODESCOLARIDADE = ES.CODESCOLARIDADE
LEFT JOIN ESTADOCIVIL EC ON U.CODESTADOCIVIL = EC.CODESTADOCIVIL
LEFT JOIN RENDA RE ON U.CODRENDA = RE.CODRENDA
LEFT JOIN TABELALIVRE1 TL1 ON U.CODTABELALIVRE1 = TL1.CODTABELALIVRE1
LEFT JOIN TABELALIVRE3 TL3 ON U.CODTABELALIVRE3 = TL3.CODTABELALIVRE3
LEFT JOIN TABELALIVRE7 TL7 ON U.CODTABELALIVRE7 = TL7.CODTABELALIVRE7
LEFT JOIN TABELALIVRE9 TL9 ON U.CODTABELALIVRE9 = TL9.CODTABELALIVRE9;