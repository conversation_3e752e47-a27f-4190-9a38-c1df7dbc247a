<?php

function xmldb_block_externalhistory_upgrade($oldversion) {
    global $DB;
    $dbman = $DB->get_manager();

    if ($oldversion < 2025052500) {
        $table = new xmldb_table('block_externalhistory');

        if ($dbman->table_exists($table)) {
            $dbman->drop_table($table);
        }

         // Adding fields to table block_externalhistory.
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('solutionname', XMLDB_TYPE_CHAR, '196', null, null, null, null);
        $table->add_field('format', XMLDB_TYPE_CHAR, '50', null, null, null, null);
        $table->add_field('description', XMLDB_TYPE_TEXT, null, null, null, null, null);
        $table->add_field('hourscompleted', XMLDB_TYPE_INTEGER, '10', null, null, null, null);
        $table->add_field('startdate', XMLDB_TYPE_INTEGER, '10', null, null, null, null);
        $table->add_field('enddate', XMLDB_TYPE_INTEGER, '10', null, null, null, null);
        $table->add_field('institution', XMLDB_TYPE_CHAR, '90', null, null, null, null);
        $table->add_field('status', XMLDB_TYPE_INTEGER, '10', null, null, null, '1');

        // Adding keys to table block_externalhistory.
        $table->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);

        // Adding indexes to table block_externalhistory.
        $table->add_index('userid', XMLDB_INDEX_NOTUNIQUE, ['userid']);

        // Adding table
        $dbman->create_table($table);

        // Externalhistory savepoint reached.
        upgrade_block_savepoint(true, 2025052500, 'externalhistory');
    }


    return true;
}
