<?php

namespace tool_companymanagement\services;

use local_ssystem\constants\uf;
use tool_companymanagement\models\company;
use tool_companymanagement\repositories\company_repository;
use local_ssystem\util\data_formatter_trait;
use moodle_exception;
use tool_companymanagement\util\cnpj_helper;

class company_service
{
    use data_formatter_trait;

    protected company_repository $repository;

    /**
     * Constructs a new instance of the class.
     *
     * @param company_repository $repository The company repository instance.
     */
    public function __construct(company_repository $repository)
    {
        $this->repository = $repository;
    }

    public function get_repository() : company_repository {
        return $this->repository;
    }

    /**
     * Creates a new company with the given data.
     *
     * @param array|object $data The data to create the company with.
     * @throws moodle_exception If an error occurs during the creation process.
     * @return company The newly created company.
     */
    public function create(array|object $data): company
    {
        global $DB;

        $data = $this->transform($data);

        $this->validate($data);

        $transaction = $DB->start_delegated_transaction();

        try {
            $company = $this->repository->create($data);

            $transaction->allow_commit();

            return $company;
        } catch (moodle_exception $e) {
            $transaction->rollback($e);

            throw $e;
        }
    }

    /**
     * Updates a company with the given data.
     *
     * @param array|object $data The data to update the company with.
     * @throws moodle_exception If the company is not found.
     * @return company The updated company.
     */
    public function update(array|object $data): company
    {
        global $DB;

        $data = $this->transform($data);

        $this->validate($data);

        $transaction = $DB->start_delegated_transaction();

        try {
            $company = $this->repository->find($data->id);

            if (!$company) {
                throw new moodle_exception('company:not_found', 'tool_companymanagement');
            }

            $company->update_from_record($data);

            $this->repository->save($company);

            $transaction->allow_commit();

            return $company;
        } catch (moodle_exception $e) {
            $transaction->rollback($e);

            throw $e;
        }
    }

    /**
     * Deletes a company by its ID.
     *
     * @param int $id The ID of the company to delete.
     * @throws moodle_exception If the company is not found.
     * @throws moodle_exception If the company has pending redemptions.
     * @return void
     */
    public function delete(int $id): void
    {
        $company = $this->repository->find($id);

        if (!$company) {
            throw new moodle_exception('company:not_found', 'tool_companymanagement');
        }

        // TODO: Implement 2.3.1.3 (Anexo 1)

        $this->repository->delete($company);
    }

    /**
     * Transforms the given data into a standardised object.
     *
     * @param array|object $data The data to transform.
     * @return object The transformed object.
     */
    protected function transform(array|object $data): object
    {
        if (is_array($data)) {
            $data = (object) $data;
        }

        return (object) [
            'id' => $data->id ?? 0,
            'name' => $data->name,
            'cnpj' => $this->unformat_CNPJ($data->cnpj),
            'state' => $data->state,
        ];
    }

    /**
     * Validates the provided company data.
     *
     * @param object $data The company data to validate.
     * @throws moodle_exception If the data is invalid.
     * @return void
     */
    protected function validate(object $data): void
    {
        $is_valid_cnpj = $this->repository->is_valid_CNPJ($data->cnpj);

        if (!$is_valid_cnpj) {
            throw new moodle_exception('cnpjinvalid', 'tool_companymanagement');
        }

        $exists = $this->repository->find_by_CNPJ($data->cnpj);

        if ($exists && !isset($data->id) || $exists && $exists->get('id') !== $data->id) {
            throw new moodle_exception('cnpjexists', 'tool_companymanagement');
        }

        if(!empty($data->state) && !in_array($data->state, uf::get_ufs())) {
            throw new moodle_exception('stateinvalid', 'tool_companymanagement');
        }
    }

    protected function is_valid_CNPJ(string $cnpj) : ?company {
        return $this->repository->find_by_CNPJ($cnpj);
    }

    public function get_available_companies() : array {
        return $this->repository->list([
            'deleted' => 0
        ]);
    }

    public function get_companies_by_cnpj(array $cnpjs) : array {
        return $this->repository->get_companies_by_cnpj($cnpjs);
    }

    /**
     * Normalize, validate via CNPJ helper, lookup and create missing companies.
     *
     * @param string|array $cnpjs  A single CNPJ, comma-separated string of CNPJs, or array of CNPJ strings.
     * @return string            comma-separated normalized raw CNPJ strings that exist or were just created.
     */
    public function validate_cnpjs_and_create_missing_companies(string|array $cnpjs): string {
        if (is_string($cnpjs)) {
            $cnpjs = explode(',', $cnpjs);
        }

        // Strip non-digits, trim, remove empties, and deduplicate.
        $normalized = array_unique(array_filter(array_map(
            fn(string $raw) => preg_replace('/\D/', '', trim($raw)),
            $cnpjs
        ), fn(string $cnpj) => $cnpj !== ''));

        if (empty($normalized)) {
            return '';
        }

        // Filter only CNPJs that pass the helper’s check-digit validation.
        $valid_cnpjs = array_filter(
            $normalized,
            fn(string $cnpj) => cnpj_helper::validate($cnpj)
        );

        if (empty($valid_cnpjs)) {
            return '';
        }

        // Batch-fetch existing companies.
        $existing = $this->repository->get_companies_by_cnpj($valid_cnpjs, true);
        $map = [];
        foreach ($existing as $company) {
            $map[$company->get_raw_cnpj()] = $company;
        }

        // For each valid CNPJ: if not found, create a new company.
        $result = [];
        foreach ($valid_cnpjs as $cnpj) {
            if (isset($map[$cnpj])) {
                $result[] = $cnpj; // Deleted cnpjs are kept, it can be changed later
            } else {
                $newdata = (object)[
                    'name'  => '',
                    'cnpj'  => $cnpj,
                    'state' => '',
                ];
                $created = $this->create($newdata);
                $result[] = $created->get_raw_cnpj();
            }
        }

        return implode(',', $result);
    }
}
