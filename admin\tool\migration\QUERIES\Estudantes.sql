SELECT
    U<PERSON>CODALUNO AS CODALUNO,
    U.NOME,
    U.EMAIL,
    TL9.<PERSON><PERSON><PERSON><PERSON><PERSON> AS ESTADO,
    U.SEXO,
    U.NASCIMENTO,
    ESC.DESCRICAO AS ESCOLARIDADE,
    EC.DESCRICAO AS ESTADOCIVIL,
    <PERSON><PERSON><PERSON> AS RENDA,
    U.DATACADASTRO,
    CNPJS.CNPJ_LISTA,
    '' AS CARGO,
    CG.DESCRICAO AS NIVELOCUPACIONAL,
    U.CPF,
    U.CAMPOLIVRE2 AS EMAILSECUNDARIO,
    U.CELULAR,
    U.CAMPOLIVRE3,
    TL3.DESCRICAO AS PERFIL,
    U.STATUS,
    FL.DESCRICAO AS FILIAL,
    U.DATAADMISSAO,
    U.DATAEXPIRACAO,
    U.DataAlteracao,
    U.NOMESOCIAL,
    P.DESCRICAO AS PAIS,
    '' AS FUNCAO,
    U.Degreed_UsuarioAtivo,
    E.DESCRICAO AS UFSEBRAE,
    U.DataAlteracao AS DATAMODIFICACAO
FROM
    USUARIOS (NOLOCK) AS U
LEFT JOIN ESTADOS (NOLOCK) AS E ON E.CODESTADOS = U.CODESTADOS
LEFT JOIN ESCOLARIDADE (NOLOCK) AS ESC ON ESC.CODESCOLARIDADE = U.CODESCOLARIDADE
LEFT JOIN ESTADOCIVIL (NOLOCK) AS EC ON EC.CODESTADOCIVIL = U.CODESTADOCIVIL
LEFT JOIN PAISES (NOLOCK) AS P ON P.CODPAISES = U.CODPAIS
LEFT JOIN RENDA (NOLOCK) AS R ON R.CODRENDA = U.CODRENDA
LEFT JOIN CARGO (NOLOCK) AS CG ON CG.CodCargo = U.CodCargo
LEFT JOIN TABELALIVRE3 (NOLOCK) AS TL3 ON TL3.CODTABELALIVRE3 = U.CODTABELALIVRE3
LEFT JOIN FILIAL (NOLOCK) AS FL ON FL.CODFILIAL = U.CODFILIAL
LEFT JOIN TABELALIVRE9 (NOLOCK) AS TL9 ON TL9.CODTABELALIVRE9 = U.CODTABELALIVRE9

OUTER APPLY (
    SELECT STRING_AGG(EM.CNPJ, ',') AS CNPJ_LISTA
    FROM EMPRESA_ALUNO (NOLOCK) AS EA
    INNER JOIN EMPRESA (NOLOCK) AS EM ON EM.CODEMPRESA = EA.CODEMPRESA
    WHERE EA.CODALUNO = U.CODALUNO
      AND EA.EXCLUIDO = 'N'
      AND EM.EXCLUIDO = 'N'
) AS CNPJS

WHERE
    U.WebAula = 0;