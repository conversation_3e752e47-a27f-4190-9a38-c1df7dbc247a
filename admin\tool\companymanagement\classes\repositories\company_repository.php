<?php

namespace tool_companymanagement\repositories;

use tool_companymanagement\models\company;
use tool_companymanagement\util\cnpj_helper;
use tool_lfxp\helpers\database\pageable_trait;
use tool_lfxp\helpers\database\query_builder;
use tool_lfxp\helpers\database\sortable_trait;
use \tool_lfxp\models\persistence\entity_repository;

class company_repository extends entity_repository
{
    use pageable_trait, sortable_trait;

    /**
     * Returns the specific entity class.
     *
     * @return string
     */
    protected static function define_entity_class(): string
    {
        return company::class;
    }

    /**
     * Gets a company instance by its id.
     *
     * @param integer $companyid
     * @return company|null
     */
    public function find(int $companyid): ?company
    {
        $entity_class = static::define_entity_class();

        $conditions = ['id' => $companyid];

        if ($entity_class::has_property('deleted')) {
            $conditions['deleted'] = 0;
        }

        if ($company = $this->get($conditions)) {
            return $company;
        }

        return null;
    }

    /**
     * Gets a company instance by its CNPJ.
     *
     * @param string $cnpj The CNPJ to search for.
     * @return company The company instance if found, or null otherwise.
     */
    public function find_by_CNPJ(string $cnpj): ?company
    {
        $cnpj = preg_replace('/\D/', '', $cnpj);
        $entity_class = static::define_entity_class();

        $conditions = ['cnpj' => $cnpj];

        if ($entity_class::has_property('deleted')) {
            $conditions['deleted'] = 0;
        }

        if ($company = $this->get($conditions)) {
            return $company;
        }

        return null;
    }

    /**
     * Retrieves a paginated list of company data based on the search criteria.
     *
     * This method constructs a query to fetch company records from the database.
     * It allows filtering by company name using a 'like' search, and excludes
     * deleted records if the 'deleted' property is present in the entity class.
     *
     * @param string $search Optional search term to filter company names.
     * @return array An array of company records matching the search criteria.
     */

    public function get_paginted_data(string $search = ''): array
    {
        $entity_class = static::define_entity_class();

        $query = new query_builder($entity_class);

        if (!empty($search)) {
            $query->whereLike('name', "%{$search}%");

            $cnpj_search = preg_replace('/\D/', '', $search);
            if(!empty($cnpj_search)){
                $query->whereLike('cnpj', "%{$cnpj_search}%", "OR");
            }
        }

        if ($entity_class::has_property('deleted')) {
            $query->where('deleted', 0);
        }

        return $this->list_select($query->build(), $query->getBindings(), $this->get_sort(), $this->get_order(), $this->get_offset(), $this->get_pagesize());
    }

    /**
     * Search for autocomplete
     *
     * @param string $search
     * @param integer $limit
     * @return company[]
     */
    public function search_companies(string $search = '', int $limit = 0): array
    {
        $entity_class = static::define_entity_class();

        $query = new query_builder($entity_class);

        if (!empty($search)) {
            $query->whereLike('name', "%{$search}%");

            $cnpj_search = preg_replace('/\D/', '', $search);
            if(!empty($cnpj_search)){
                $query->whereLike('cnpj', "%{$cnpj_search}%", "OR");
            }
        }

        if ($entity_class::has_property('deleted')) {
            $query->where('deleted', 0);
        }

        return $this->list_select($query->build(), $query->getBindings(), 'name', 'ASC', 0, $limit);
    }

    /**
     * Retrieves the total count of companies matching the search criteria.
     *
     * @param string $search An optional search string to filter companies by name.
     *                       If provided, only companies with names matching the search
     *                       string will be considered.
     * @return int The total number of companies matching the search criteria.
     */

    public function get_paginted_total(string $search = ''): int
    {
        $entity_class = static::define_entity_class();

        $query = new query_builder($entity_class);

        if (!empty($search)) {
            $query->whereLike('name', "%{$search}%");

            $cnpj_search = preg_replace('/\D/', '', $search);
            if(!empty($cnpj_search)){
                $query->whereLike('cnpj', "%{$cnpj_search}%", "OR");
            }
        }

        if ($entity_class::has_property('deleted')) {
            $query->where('deleted', 0);
        }

        return $this->count_select($query->build(), $query->getBindings());
    }

    /**
     * Validates a CNPJ using its check digits.
     *
     * @param string $cnpj The CNPJ to validate.
     * @return bool True if the CNPJ is valid, false otherwise.
     */
    public static function is_valid_CNPJ(string $cnpj): bool
    {
        return cnpj_helper::validate($cnpj);
    }

    public function get_companies_by_cnpj(string|array $cnpjs, bool $include_deleted = false) : array {
        global $DB;

        if(is_string($cnpjs)){
            $cnpjs = [preg_replace('/\D/', '', $cnpjs)];
        }else{
            $cnpjs = array_map(fn($c) => preg_replace('/\D/', '', $c), $cnpjs);
        }

        if (empty($cnpjs)) {
            return [];
        }

        $entity_class = static::define_entity_class();
        $table = $entity_class::TABLE;
        $companies = [];

        [$insql, $params] = $DB->get_in_or_equal($cnpjs, SQL_PARAMS_NAMED);
        $select = "cnpj $insql";
        if (!$include_deleted && $entity_class::has_property('deleted')) {
            $select .= " AND deleted = 0";
        }

        foreach ($DB->get_records_select($table, $select, $params) as $raw_company) {
            $companies[] = static::create_new_entity_class($raw_company);
        }

        return $companies;
    }
}
