<?php

/* 
Objetivos:

- Adicionar uma submissão em um assign por um usuário (texto e ou arquivo).
- Corrigir com o professor, dando nota e escrevendo um feedback.

*/

require_once(__DIR__ . '/../../../../config.php');

use tool_migration\helpers\assign_helper;


$assign = assign_helper::get_assign_from_id(7);

$student = $DB->get_record('user', ['id' => 1999]);
$teacher = $DB->get_record('user', ['id' => 2]);
$image_path = __DIR__ . "/490433387_1232393231584703_4971005209286330263_n.jpg";

$submission = assign_helper::create_submission($assign, $student, [
    'text' => "MINHA SUBMISSÃO!",
    'filepath' => $image_path,
    'timecreated' => 10000000,
    'timestarted' => 22222222,
]);

$status = assign_helper::grade_submission($assign, $teacher, $student, [
    'grade' => 10,
    'text' => "Faltou tompero!",
    'attemptnumber' => $submission->attemptnumber,
    'timecreated' => 10000000,
    'timemodified' => 22222222,
]);

var_dump($status);