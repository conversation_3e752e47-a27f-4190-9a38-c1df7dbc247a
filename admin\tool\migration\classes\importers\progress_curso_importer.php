<?php namespace tool_migration\importers;

use tool_migration\models\progresso_curso;
use Exception;

require_once($CFG->dirroot . '/user/profile/lib.php');
require_once($CFG->dirroot . '/user/lib.php');

class progress_curso_importer extends abstract_importer {

    public function import_pending(int $limit = 0) : void {
        foreach (progresso_curso::get_pending_imports($limit) as $progresso_curso) {
            try {               
                $this->upsert_legacy_record($progresso_curso);
            } catch (\Throwable $th) {
                $identifier = $progresso_curso->get($progresso_curso::define_identifier());
                $this->output("Erro ao importar situação de matrícula \"$identifier\": " .$th->getMessage(), 1);
            }
        }
    }

    public function update_pending(int $limit = 0) : void {
        foreach (progresso_curso::get_pending_updates($limit) as $progresso_curso) {
            try {
                $this->upsert_legacy_record($progresso_curso);
            } catch (\Throwable $th) {
                $identifier = $progresso_curso->get($progresso_curso::define_identifier());
                $this->output("Erro ao atualizar situação de matrícula \"$identifier\": " .$th->getMessage(), 1);
            }
        }
    }


    /**
     * Marks as obsolete (course_completion_id = -1, user_enrolment_id = -1)
     * all old registers that would be overwritten by newer versions.
     */
    public function prune_obsolete_completions(): void {
        global $DB;
        $this->output("Marcando registros obsoletos...", 0);

        $sql = "
            UPDATE {eadtech_progresso_cursos} pc
            JOIN (
                SELECT
                    source_codcurso,
                    source_codaluno,
                    MAX(source_codsituacaoalunocurso) AS max_situacao
                FROM {eadtech_progresso_cursos}
                GROUP BY source_codcurso, source_codaluno
            ) AS latest
            ON latest.source_codcurso = pc.source_codcurso
            AND latest.source_codaluno = pc.source_codaluno
            SET
                pc.course_completion_id = -1,
                pc.user_enrolment_id    = -1
            WHERE
                pc.source_codsituacaoalunocurso < latest.max_situacao
        ";

        $DB->execute($sql);
    }

    public function import_pending_completion(int $limit = 0) : void {
        $this->output("Importando matrículas e conclusões...", 0);

        foreach (progresso_curso::get_pending_completion_imports($limit) as $progresso_curso) {
            try {               
                $this->upsert_completion($progresso_curso);
            } catch (\Throwable $th) {
                $identifier = $progresso_curso->get($progresso_curso::define_identifier());
                $this->output("Erro ao importar conclusão de curso \"$identifier\": " .$th->getMessage(), 1);
            }
        }
    }

    public function update_pending_completion(int $limit = 0) : void {
        $this->output("Importando matrículas e conclusões...", 0);
        foreach (progresso_curso::get_pending_completion_updates($limit) as $progresso_curso) {
            try {
                $this->upsert_completion($progresso_curso);
            } catch (\Throwable $th) {
                $identifier = $progresso_curso->get($progresso_curso::define_identifier());
                $this->output("Erro ao atualizar conclusão de curso \"$identifier\": " .$th->getMessage(), 1);
            }
        }
    }
    
    protected function upsert_legacy_record(progresso_curso $progresso_curso){
        return; // TODO
    }


    
    protected function upsert_completion(progresso_curso $progresso_curso){
        $previous = $this->get_previously_imported_completion($progresso_curso);

        if($previous){
            return $this->replace_completion($progresso_curso, $previous);
        }        

        if($progresso_curso->get('course_completion_id')){
            return $this->update_completion($progresso_curso);
        }

        return $this->insert_completion($progresso_curso);
    }


    protected function get_previously_imported_completion(progresso_curso $progresso_curso): ?progresso_curso {
        global $DB;

        $select = "source_codcurso = :codcurso
                AND source_codaluno = :codaluno
                AND course_completion_id > 0
                AND source_codsituacaoalunocurso != :codsituacaoalunocurso";

        $params = [
            'codcurso' => $progresso_curso->get('source_codcurso'),
            'codaluno' => $progresso_curso->get('source_codaluno'),
            'codsituacaoalunocurso' => $progresso_curso->get('source_codsituacaoalunocurso'),
        ];

        $records = $DB->get_records_select(
            progresso_curso::TABLE,
            $select,
            $params,
            'source_codsituacaoalunocurso DESC',
            '*',
            0,
            1
        );

        if ($records) {
            return new progresso_curso(0, reset($records));
        }

        return null;
    }


    protected function replace_completion(progresso_curso $progresso_curso, progresso_curso $previous){
        global $DB;

        $identifier = $progresso_curso->get($progresso_curso::define_identifier());

        if($previous->get('source_codsituacaoalunocurso') > $progresso_curso->get('source_codsituacaoalunocurso')){
            $progresso_curso->set('user_enrolment_id', -1);
            $progresso_curso->set('course_completion_id', -1);
            $progresso_curso->save();

            $this->output("A matricula $identifier é obsoleta e foi ignorada", 1);
            return;
        }

        try {
            $transaction = $DB->start_delegated_transaction();

            $progresso_curso->set('user_enrolment_id', $previous->get('user_enrolment_id'));
            $progresso_curso->set('course_completion_id', $previous->get('course_completion_id'));
            $progresso_curso->save();

            $previous->set('user_enrolment_id', -1);
            $previous->set('course_completion_id', -1);
            $previous->save();

            // Enrol
            $user_enrolment = $progresso_curso->to_user_enrolment();
            $DB->update_record('user_enrolments', $user_enrolment);
            $this->output("Matrícula em curso $identifier atualizada", 1);
            
            // Completion
            $completion = $progresso_curso->to_course_completion();
            $DB->update_record('course_completions', $completion);
            $progresso_curso->mark_as_updated();            
            $this->output("Conclusão de curso $identifier atualizada", 1);

            $transaction->allow_commit();
        } catch (Exception $e) {
            $transaction->rollback($e);
        }
    }

    protected function insert_completion(progresso_curso $progresso_curso){
        global $DB;

        $identifier = $progresso_curso->get($progresso_curso::define_identifier());

        if(!$progresso_curso->is_completed()){
            // Don't bother with nom-completed records
            $progresso_curso->set('user_enrolment_id', -1);
            $progresso_curso->set('course_completion_id', -1);
            $progresso_curso->save();

            $this->output("A matricula $identifier não está completa e foi ignorada", 1);
            return;
        }


        try {
            $transaction = $DB->start_delegated_transaction();
            
            // Enrol
            $user_enrolment = $progresso_curso->to_user_enrolment();
            // Checking if a previous enrolment exists:
            $user_enrolment->id = $DB->get_field('user_enrolments', 'id', ['enrolid' => $user_enrolment->enrolid, 'userid' => $user_enrolment->userid]);
            if(empty($user_enrolment->id)){
                $user_enrolment->id = $DB->insert_record('user_enrolments', $user_enrolment);
            }else{
                $this->output("Reutilizando user_enrolments.id anterior", 2);
            }
            $progresso_curso->mark_enrolment_as_imported($user_enrolment->id);
            $this->output("Matrícula em curso $identifier adicionada", 1);
            
            // Completion
            $completion = $progresso_curso->to_course_completion();
            // Checking if a previous completion exists:
            $completion->id = $DB->get_field('course_completions', 'id', ['course' => $completion->course, 'userid' => $completion->userid]);
            if(empty($completion->id)){
                $completion->id = $DB->insert_record('course_completions', $completion);
            }else{
                $this->output("Reutilizando course_completions.id anterior", 2);
            }        
            $progresso_curso->mark_completion_as_imported($completion->id);            
            $this->output("Conclusão de curso $identifier adicionada", 1);

            $transaction->allow_commit();
        } catch (Exception $e) {
            $transaction->rollback($e);
        }
    }


    protected function update_completion(progresso_curso $progresso_curso){
        global $DB;

        try {
            $transaction = $DB->start_delegated_transaction();
            $identifier = $progresso_curso->get($progresso_curso::define_identifier());
            
            // Enrol
            $user_enrolment = $progresso_curso->to_user_enrolment();
            $DB->update_record('user_enrolments', $user_enrolment);
            $this->output("Matrícula em curso $identifier atualizada", 1);
            
            // Completion
            $completion = $progresso_curso->to_course_completion();
            $DB->update_record('course_completions', $completion);
            $progresso_curso->mark_as_updated();            
            $this->output("Conclusão de curso $identifier atualizada", 1);

            $transaction->allow_commit();
        } catch (Exception $e) {
            $transaction->rollback($e);
        }
    }
}