<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Contains the default section controls output class.
 *
 * @package   format_trails
 * @copyright 2020 <PERSON><PERSON>n <PERSON> <<EMAIL>>
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace format_trails\output\courseformat\content\section;

use context_course;
use core_courseformat\output\local\content\section\controlmenu as controlmenu_base;
use moodle_url;

/**
 * Base class to render a course section menu.
 *
 * @package   format_trails
 * @copyright 2020 <PERSON><PERSON><PERSON> <<EMAIL>>
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class controlmenu extends controlmenu_base
{

    /** @var \core_courseformat\base the course format class */
    protected $format;

    /** @var \section_info the course section class */
    protected $section;

    /**
     * Generate the edit control items of a section.
     *
     * This method must remain public until the final deprecation of section_edit_control_items.
     *
     * @return array of edit control items
     */
    // public function section_control_items()
    // {

    //     $format = $this->format;
    //     $section = $this->section;
    //     $coursecontext = $format->get_context();

    //     $controls = [];
    //     if ($section->section && has_capability('moodle/course:setcurrentsection', $coursecontext)) {
    //         $controls['highlight'] = $this->get_highlight_control();
    //     }

    //     $parentcontrols = parent::section_control_items();

    //     // echo "<pre>"; var_dump($this->section, $parentcontrols); die;

    //     // If the edit key exists, we are going to insert our controls after it.
    //     if (array_key_exists("edit", $parentcontrols)) {
    //         $merged = [];
    //         // We can't use splice because we are using associative arrays.
    //         // Step through the array and merge the arrays.
    //         foreach ($parentcontrols as $key => $action) {
    //             $merged[$key] = $action;
    //             if ($key == "edit") {
    //                 // If we have come to the edit key, merge these controls here.
    //                 $merged = array_merge($merged, $controls);
    //             }
    //         }

    //         return $merged;
    //     } else {
    //         return array_merge($controls, $parentcontrols);
    //     }
    // }

    /**
     * Generate the edit control items of a section.
     *
     * It is not clear this kind of controls are still available in 4.0 so, for now, this
     * method is almost a clone of the previous section_control_items from the course/renderer.php.
     *
     * This method must remain public until the final deprecation of section_edit_control_items.
     *
     * @return array of edit control items
     */
    public function section_control_items()
    {
        global $USER;

        $format = $this->format;
        $section = $this->section;
        $course = $format->get_course();
        $sectionreturn = $format->get_section_number();
        $user = $USER;

        $usecomponents = $format->supports_components();
        $coursecontext = context_course::instance($course->id);
        $numsections = $format->get_last_section_number();
        $isstealth = $section->section > $numsections;

        $baseurl = course_get_url($course, $sectionreturn);
        $baseurl->param('sesskey', sesskey());

        $controls = [];

        $sectionname = mb_strtolower($format->get_custom_section_name());

        if (!$isstealth && has_capability('moodle/course:update', $coursecontext, $user)) {
            if (
                $section->section > 0
                && get_string_manager()->string_exists('editsection', 'format_' . $format->get_format())
            ) {
                $streditsection = get_string('editsection', 'format_' . $format->get_format(), $sectionname);
            } else {
                $streditsection = get_string('editsection');
            }

            $controls['edit'] = [
                'url'   => new moodle_url('/course/editsection.php', ['id' => $section->id, 'sr' => $sectionreturn]),
                'icon' => 'i/settings',
                'name' => $streditsection,
                'pixattr' => ['class' => ''],
                'attr' => ['class' => 'icon edit'],
            ];

            if ($section->section && has_capability('moodle/course:setcurrentsection', $coursecontext)) {
                $controls['highlight'] = $this->get_highlight_control();
            }

            $duplicatesectionurl = clone ($baseurl);
            $duplicatesectionurl->param('section', $section->section);
            $duplicatesectionurl->param('duplicatesection', $section->section);
            $controls['duplicate'] = [
                'url' => $duplicatesectionurl,
                'icon' => 't/copy',
                'name' => get_string('duplicate'),
                'pixattr' => ['class' => ''],
                'attr' => ['class' => 'icon duplicate'],
            ];
        }

        if ($section->section) {
            $url = clone ($baseurl);
            if (!$isstealth) {
                if (has_capability('moodle/course:sectionvisibility', $coursecontext, $user)) {
                    $strhidefromothers = get_string('hidefromothers', 'format_' . $course->format, $sectionname);
                    $strshowfromothers = get_string('showfromothers', 'format_' . $course->format, $sectionname);
                    if ($section->visible) { // Show the hide/show eye.
                        $url->param('hide', $section->section);
                        $controls['visiblity'] = [
                            'url' => $url,
                            'icon' => 'i/hide',
                            'name' => $strhidefromothers,
                            'pixattr' => ['class' => ''],
                            'attr' => [
                                'class' => 'icon editing_showhide',
                                'data-sectionreturn' => $sectionreturn,
                                'data-action' => ($usecomponents) ? 'sectionHide' : 'hide',
                                'data-id' => $section->id,
                                'data-swapname' => $strshowfromothers,
                                'data-swapicon' => 'i/show',
                            ],
                        ];
                    } else {
                        $url->param('show',  $section->section);
                        $controls['visiblity'] = [
                            'url' => $url,
                            'icon' => 'i/show',
                            'name' => $strshowfromothers,
                            'pixattr' => ['class' => ''],
                            'attr' => [
                                'class' => 'icon editing_showhide',
                                'data-sectionreturn' => $sectionreturn,
                                'data-action' => ($usecomponents) ? 'sectionShow' : 'show',
                                'data-id' => $section->id,
                                'data-swapname' => $strhidefromothers,
                                'data-swapicon' => 'i/hide',
                            ],
                        ];
                    }
                }

                if (!$sectionreturn && has_capability('moodle/course:movesections', $coursecontext, $user)) {
                    if ($usecomponents) {
                        // This tool will appear only when the state is ready.
                        $url = clone ($baseurl);
                        $url->param('movesection', $section->section);
                        $url->param('section', $section->section);
                        $controls['movesection'] = [
                            'url' => $url,
                            'icon' => 'i/dragdrop',
                            'name' => get_string('move', 'moodle'),
                            'pixattr' => ['class' => ''],
                            'attr' => [
                                'class' => 'icon move waitstate',
                                'data-action' => 'moveSection',
                                'data-id' => $section->id,
                            ],
                        ];
                    }
                    // Legacy move up and down links for non component-based formats.
                    $url = clone ($baseurl);
                    if ($section->section > 1) { // Add a arrow to move section up.
                        $url->param('section', $section->section);
                        $url->param('move', -1);
                        $strmoveup = get_string('moveup');
                        $controls['moveup'] = [
                            'url' => $url,
                            'icon' => 'i/up',
                            'name' => $strmoveup,
                            'pixattr' => ['class' => ''],
                            'attr' => ['class' => 'icon moveup whilenostate'],
                        ];
                    }

                    $url = clone ($baseurl);
                    if ($section->section < $numsections) { // Add a arrow to move section down.
                        $url->param('section', $section->section);
                        $url->param('move', 1);
                        $strmovedown = get_string('movedown');
                        $controls['movedown'] = [
                            'url' => $url,
                            'icon' => 'i/down',
                            'name' => $strmovedown,
                            'pixattr' => ['class' => ''],
                            'attr' => ['class' => 'icon movedown whilenostate'],
                        ];
                    }
                }
            }

            if (course_can_delete_section($course, $section)) {
                if (get_string_manager()->string_exists('deletesection', 'format_' . $course->format)) {
                    $strdelete = get_string('deletesection', 'format_' . $course->format, $sectionname);
                } else {
                    $strdelete = get_string('deletesection');
                }
                $url = new moodle_url(
                    '/course/editsection.php',
                    [
                        'id' => $section->id,
                        'sr' => $sectionreturn,
                        'delete' => 1,
                        'sesskey' => sesskey(),
                    ]
                );
                $controls['delete'] = [
                    'url' => $url,
                    'icon' => 'i/delete',
                    'name' => $strdelete,
                    'pixattr' => ['class' => ''],
                    'attr' => [
                        'class' => 'icon editing_delete',
                        'data-action' => 'deleteSection',
                        'data-id' => $section->id,
                    ],
                ];
            }
        }
        if (
            has_any_capability([
                'moodle/course:movesections',
                'moodle/course:update',
                'moodle/course:sectionvisibility',
            ], $coursecontext)
        ) {
            $sectionlink = new moodle_url(
                '/course/view.php',
                ['id' => $course->id],
                "sectionid-{$section->id}-title"
            );
            $controls['permalink'] = [
                'url' => $sectionlink,
                'icon' => 'i/link',
                'name' => get_string('sectionlink', 'course'),
                'pixattr' => ['class' => ''],
                'attr' => [
                    'class' => 'icon',
                    'data-action' => 'permalink',
                ],
            ];
        }

        return $controls;
    }

    /**
     * Return the course url.
     *
     * @return moodle_url
     */
    protected function get_course_url(): moodle_url
    {
        $format = $this->format;
        $section = $this->section;
        $course = $format->get_course();
        $sectionreturn = $format->get_section_number();

        if ($sectionreturn) {
            $url = course_get_url($course, $section->section);
        } else {
            $url = course_get_url($course);
        }
        $url->param('sesskey', sesskey());
        return $url;
    }

    /**
     * Return the specific section highlight action.
     *
     * @return array the action element.
     */
    protected function get_highlight_control(): array
    {
        $format = $this->format;
        $section = $this->section;
        $course = $format->get_course();
        $url = $this->get_course_url();

        $highlightoff = get_string('highlightoff');
        $highlighton = get_string('highlight');

        if ($course->marker == $section->section) {  // Show the "light globe" on/off.
            $url->param('marker', 0);
            $result = [
                'url' => $url,
                'icon' => 'i/marked',
                'name' => $highlightoff,
                'pixattr' => ['class' => ''],
                'attr' => [
                    'class' => 'editing_highlight',
                    'data-action' => 'sectionUnhighlight',
                    'data-id' => $section->id,
                    'data-swapname' => $highlighton,
                    'data-swapicon' => 'i/marker',
                ],
            ];
        } else {
            $url->param('marker', $section->section);
            $result = [
                'url' => $url,
                'icon' => 'i/marker',
                'name' => $highlighton,
                'pixattr' => ['class' => ''],
                'attr' => [
                    'class' => 'editing_highlight',
                    'data-action' => 'sectionHighlight',
                    'data-id' => $section->id,
                    'data-swapname' => $highlightoff,
                    'data-swapicon' => 'i/marked',
                ],
            ];
        }
        return $result;
    }
}
