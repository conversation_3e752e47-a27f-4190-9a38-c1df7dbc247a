<?php

if(!defined('CLI_SCRIPT')){
    define('CLI_SCRIPT', true);
}

require_once(__DIR__ . '/../../../../config.php');
require_once($CFG->libdir . '/clilib.php');

cli_writeln("===> Iniciando preparação para migração...");

// Disabling cron
set_config('cron_enabled', 0);
cli_writeln("✔ Cron desativado");

// Disabling logstore
set_config('enabled_stores', '', 'tool_log');
cli_writeln("✔ Logstore desativado");

// Enabling maintenance
set_config('maintenance_enabled', 1);
unset_config('maintenance_later');
cli_writeln("✔ Modo de manutenção ativado");

cli_writeln("===> Ambiente pronto para migração.");