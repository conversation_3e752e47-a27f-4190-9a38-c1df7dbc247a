<?php namespace tool_migration\models;

defined('MOODLE_INTERNAL') || die();

use tool_migration\util\ingestion_helper;
use \local_ssystem\constants\custom_profile_fields;

class estudante extends abstract_migratable_entity {
    const TABLE = 'eadtech_estudantes';

    const STATUS_INACTIVE = 'I';
    const STATUS_DELETED = 'E';
    const STATUS_ACTIVE = 'A';

    public static function define_identifier(): string {
        return 'source_codaluno';
    }

    protected static function define_model_properties(): array {
        return [
            'source_codaluno' => [
                'type' => PARAM_INT,
            ],
            'source_nome' => [
                'type' => PARAM_TEXT,
            ],
            'source_email' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_estado' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_sexo' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_nascimento' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_escolaridade' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_estadocivil' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_renda' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_datacadastro' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_cnpj_lista' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_cargo' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_nivelocupacional' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_cpf' => [
                'type' => PARAM_TEXT,
            ],
            'source_emailsecundario' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_celular' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_campolivre3' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_perfil' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_status' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_filial' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_dataadmissao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_dataexpiracao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_dataalteracao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_nomesocial' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_pais' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_funcao' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_degreed_usuarioativo' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_ufsebrae' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_datamodificacao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
        ];
    }

    public function get_idnumber(): string {
        return 'LEGADO-' . $this->get('source_codaluno');
    }

    protected function get_email() : string {
        $email = ingestion_helper::format_email($this->get('source_email'));

        if(empty($email)){
            $email = ingestion_helper::generate_fake_email($this->get('source_codaluno'));
        }

        return $email;
    }


    /**
     * To an user object
     *
     * @return object
     */
    public function to_user() : object {
        global $CFG;

        $user = ingestion_helper::split_fullname($this->get('source_nome'));
        $user->idnumber = $this->get_idnumber();
        $user->username = ingestion_helper::only_numbers($this->get('source_cpf'));
        $user->email = $this->get_email();
        $user->alternatename = $this->get('source_nomesocial');
        $user->confirmed = 1;
        $user->suspended = (int) $this->is_suspended();
        $user->country = 'BR';
        $user->lang = get_string_manager()->translation_exists('pt_br') ? 'pt_br' : 'en';
        $user->auth = 'amei';
        $user->mnethostid = $CFG->mnet_localhost_id;

        foreach ($this->get_custom_fields() as $key => $value) {
            $key = "profile_field_$key";
            $user->$key = $value;
        }

        if($id = $this->get('instanceid')){
            $user->id = $id;
        }

        return $user;
    }

    protected function is_suspended() : bool {
        return $this->get('source_status') != self::STATUS_ACTIVE;
    }

    protected function get_custom_fields() : array {
        $fields = [];

        if($estado = $this->get('source_estado')){
            $key = custom_profile_fields::STATE_FIELD;
            $fields[$key] = $estado;
        }

        if($sexo = $this->get('source_sexo')){
            $key = custom_profile_fields::GENDER_FIELD;
            $fields[$key] = self::format_gender($sexo);
        }

        if($nascimento = $this->get('source_nascimento')){
            $key = custom_profile_fields::BIRTHDATE_FIELD;
            $fields[$key] = ingestion_helper::str_to_timestamp($nascimento);
        }

        if($cadastro = $this->get('source_datacadastro')){
            $key = custom_profile_fields::REGISTRATION_DATE_FIELD;
            $fields[$key] = ingestion_helper::str_to_timestamp($cadastro);
        }

        if($nivel_ocupacional = $this->get('source_nivelocupacional')){
            $key = custom_profile_fields::OCCUPATIONAL_LEVEL_FIELD;
            $fields[$key] = $nivel_ocupacional;
        }

        $email2 = ingestion_helper::format_email($this->get('source_emailsecundario'));
        if(!empty($email2)){
            $key = custom_profile_fields::SECONDARY_EMAIL_FIELD;
            $fields[$key] = $email2;
        }

        if($celular = $this->get('source_celular')){
            $key = custom_profile_fields::CELLPHONE_FIELD;
            $fields[$key] = ingestion_helper::format_phone($celular);
        }

        if($perfil_ocupational = $this->get('source_perfil')){
            $key = custom_profile_fields::OCCUPATIONAL_PROFILE_FIELD;
            $fields[$key] = $perfil_ocupational;
        }

        if($status = $this->get('source_status')){
            $key = custom_profile_fields::STATUS_TYPE_FIELD;
            if($status = self::format_status($status)){
                $fields[$key] = $status;
            }
        }

        if($expiracao = $this->get('source_dataexpiracao')){
            $key = custom_profile_fields::EXPIRATION_DATE_FIELD;
            $fields[$key] = ingestion_helper::str_to_timestamp($expiracao);
        }

        if($ufsebrae = $this->get('source_ufsebrae')){
            $key = custom_profile_fields::UFSEBRAE_FIELD;
            $fields[$key] = $ufsebrae;
        }

        if($cnpjs = $this->get('source_cnpj_lista')){
            $key = custom_profile_fields::RELATED_COMPANY_FIELD;
            $fields[$key] = $cnpjs;
        }

        return $fields;
    }


    public static function format_gender(string $gender) : string {
        $gender = mb_strtoupper($gender);

        $female = ['F', 'FEMININO'];
        $male = ['M', 'MASCULINO'];

        if(in_array($gender, $male)){
            return custom_profile_fields::GENDER_MALE;
        }

        if(in_array($gender, $female)){
            return custom_profile_fields::GENDER_FEMALE;
        }

        return custom_profile_fields::GENDER_OTHER;
    }

    protected static function format_status(string $status) : string {
        return match ($status) {
            self::STATUS_INACTIVE => custom_profile_fields::STATUS_TYPE_INACTIVE,
            self::STATUS_ACTIVE => custom_profile_fields::STATUS_TYPE_ACTIVE,
            default => '',
        };
    }
}
