<?php

require(__DIR__ . '/../../config.php');

require_login();

$PAGE->set_url(new moodle_url('/blocks/externalhistory/index.php'));
$PAGE->set_context(context_system::instance());
$PAGE->requires->css('/blocks/externalhistory/styles.css');
$PAGE->navbar->add(get_string('title', 'block_externalhistory'));
$PAGE->set_heading($SITE->fullname);
echo $OUTPUT->header();

$renderable = new block_externalhistory\output\external_history_list($USER->id);
echo $PAGE->get_renderer('block_externalhistory')->render_external_history_list($renderable);

echo $OUTPUT->footer();