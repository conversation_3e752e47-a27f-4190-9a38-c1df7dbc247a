<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Contains the default activity item from a section.
 *
 * @package   core_courseformat
 * @copyright 2020 Ferran <PERSON>cio <<EMAIL>>
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace format_learningflix\output\courseformat\content\section;

use core_courseformat\output\local\content\section\cmitem as cmitem_base;
use stdClass;

/**
 * Base class to render a section activity in the activities list.
 *
 * @package   core_courseformat
 * @copyright 2020 <PERSON>rran <PERSON> <<EMAIL>>
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class cmitem extends cmitem_base
{
    /**
     * Get the name of the template to use for this templatable.
     *
     * @param \renderer_base $renderer The renderer requesting the template name
     * @return string
     */
    public function get_template_name(\renderer_base $renderer): string
    {
        $fullpath = str_replace('\\', '/', get_class($this));

        $specialrenderers = '@^.*/output/(local|courseformat)/(?<template>.+)$@';
        $matches = null;

        if (preg_match($specialrenderers, $fullpath, $matches)) {
            return "format_learningflix/local/{$matches['template']}";
        }

        throw new \coding_exception("Unable to determine template name for class " . get_class($this));
    }

    /**
     * Export this data so it can be used as the context for a mustache template.
     *
     * @param renderer_base $output typically, the renderer that's calling this function
     * @return stdClass data context for a mustache template
     */
    public function export_for_template(\renderer_base $output): stdClass
    {
        global $CFG, $USER;

        $format = $this->format;
        $course = $format->get_course();
        $mod = $this->mod;

        $data = new stdClass();
        $data->cms = [];

        $completionenabled = $course->enablecompletion == COMPLETION_ENABLED;
        $showactivityconditions = $completionenabled && $course->showcompletionconditions == COMPLETION_SHOW_CONDITIONS;
        $showactivitydates = !empty($course->showactivitydates);

        // This will apply styles to the course homepage when the activity information output component is displayed.
        $hasinfo = $showactivityconditions || $showactivitydates;

        $item = new $this->cmclass($format, $this->section, $mod, $this->displayoptions);

        $intro = format_learningflix_get_activity_intro($mod->modname, $mod->instance, $mod->id);

        require_once($CFG->dirroot . '/admin/tool/lfxp/lib/modlib.php');
        $coverurl = tool_lfxp_get_cm_image($mod->id);

        require_once($CFG->libdir . '/completionlib.php');
        $completion = new \completion_info($course);
        $completiondata = $completion->get_data($mod, true, $USER->id);

        return (object)[
            'id' => $mod->id,
            'anchor' => "module-{$mod->id}",
            'module' => $mod->modname,
            'extraclasses' => $mod->extraclasses,
            'cmformat' => $item->export_for_template($output),
            'hasinfo' => $hasinfo,
            'indent' => ($format->uses_indentation()) ? $mod->indent : 0,
            'coverurl' => $coverurl,
            'player' => ($mod->modname === 'scorm' || $mod->modname === 'supervideo' || $mod->modname === 'videos'),
            'completionstatus' => [
                'state' => $completiondata->completionstate
            ]
        ];
    }
}
