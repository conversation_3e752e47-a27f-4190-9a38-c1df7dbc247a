<?php
defined('MOODLE_INTERNAL') || die();

/**
 * Upgrade the coursemanagement plugin.
 *
 * @param int $oldversion The old version of the coursemanagement plugin
 * @return bool
 */
function xmldb_tool_coursemanagement_upgrade($oldversion) {
    global $CFG, $DB;

    require_once($CFG->dirroot."/admin/tool/coursemanagement/installlib.php");

    if ($oldversion < 2023090803) {
        $systemcontext = context_system::instance();
		$roleid = $DB->get_field("role", "id", ["shortname" => "client"]);

		assign_capability('moodle/backup:backupcourse', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/restore:restorecourse', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/category:manage', CAP_ALLOW, $roleid, $systemcontext->id);
        assign_capability('moodle/category:viewhiddencategories', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:activityvisibility', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:bulkmessaging', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:changecategory', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:changefullname', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:changeidnumber', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:changeshortname', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:changesummary', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:configuredownloadcontent', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:create', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:creategroupconversations', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:delete', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:downloadcoursecontent', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:enrolconfig', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:enrolreview', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:ignoreavailabilityrestrictions', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:manageactivities', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:managefiles', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:managegroups', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:managescales', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:markcomplete', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:movesections', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:overridecompletion', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:recommendactivity', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:renameroles', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:reset', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:reviewotherusers', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:sectionvisibility', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:setcurrentsection', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:setforcedlanguage', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:tag', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:update', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:useremail', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:view', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:viewhiddenactivities', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:viewhiddencourses', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:viewhiddengroups', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:viewhiddensections', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:viewhiddenuserfields', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:viewparticipants', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:viewscales', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:viewsuspendedusers', CAP_ALLOW, $roleid, $systemcontext->id);
		assign_capability('moodle/course:visibility', CAP_ALLOW, $roleid, $systemcontext->id);

        upgrade_plugin_savepoint(true, 2023090803, 'tool', 'coursemanagement');
    }

    if ($oldversion < 2023090804) {
		tool_coursemanagement_create_course_fields("Administração");
        upgrade_plugin_savepoint(true, 2023090804, 'tool', 'coursemanagement');
    }

    if ($oldversion < 2023090806) {
		$configdata = new \stdClass;
		$configdata->required = '0';
		$configdata->uniquevalues = '0';
		$configdata->locked = '1';
		$configdata->visibility = '0';
		$configdata->checkbydefault = '0';

		$data = new \stdClass;
		$data->id = $DB->get_field("customfield_field", "id", ["shortname" => "learningflix_course"]);
		$data->configdata = json_encode($configdata);

		$DB->update_record("customfield_field", $data);

        upgrade_plugin_savepoint(true, 2023090806, 'tool', 'coursemanagement');
    }

    if ($oldversion < 2023090807) {
        // Modificar o template para ocultar o botão "Gerenciar Áreas de interesse" para usuários sem permissão
        $template_path = $CFG->dirroot . '/admin/tool/coursemanagement/templates/view.mustache';

        if (file_exists($template_path)) {
            // Ler o conteúdo do arquivo
            $template_content = file_get_contents($template_path);

            // Procurar pelo botão "Gerenciar Áreas de interesse"
            $button_pattern = '/<a href="{{wwwroot}}\/admin\/tool\/interestareas\/index\.php" class="btn btn-primary mt-auto">{{#str}}pluginname, tool_interestareas{{\/str}}<\/a>/';

            // Substituir pelo botão com verificação de capacidade
            $button_replacement = '{{#hasinterestareascap}}<a href="{{wwwroot}}/admin/tool/interestareas/index.php" class="btn btn-primary mt-auto">{{#str}}pluginname, tool_interestareas{{/str}}</a>{{/hasinterestareascap}}';

            // Realizar a substituição
            $modified_content = preg_replace($button_pattern, $button_replacement, $template_content);

            // Verificar se a substituição foi bem-sucedida
            if ($modified_content !== $template_content) {
                // Salvar o arquivo modificado
                file_put_contents($template_path, $modified_content);
            }
        }

        // Modificar o renderer para adicionar a verificação de capacidade
        $renderer_path = $CFG->dirroot . '/admin/tool/coursemanagement/classes/output/renderer.php';

        if (file_exists($renderer_path)) {
            // Ler o conteúdo do arquivo
            $renderer_content = file_get_contents($renderer_path);

            // Procurar pelo método que prepara os dados para o template
            $data_pattern = '/public function output\(\)\s*{\s*global \$CFG, \$OUTPUT, \$DB;\s*/';

            // Verificar se o padrão foi encontrado
            if (preg_match($data_pattern, $renderer_content)) {
                // Adicionar a verificação de capacidade
                $data_replacement = 'public function output() {
        global $CFG, $OUTPUT, $DB;

        // Verificar se o usuário tem a capacidade para gerenciar áreas de interesse
        $this->data->hasinterestareascap = has_capability(\'tool/interestareas:manage\', context_system::instance());
        ';

                // Realizar a substituição
                $modified_renderer = preg_replace($data_pattern, $data_replacement, $renderer_content);

                // Verificar se a substituição foi bem-sucedida
                if ($modified_renderer !== $renderer_content) {
                    // Salvar o arquivo modificado
                    file_put_contents($renderer_path, $modified_renderer);
                }
            }
        }

        // Limpar caches
        purge_all_caches();

        upgrade_plugin_savepoint(true, 2023090807, 'tool', 'coursemanagement');
    }

    return true;
}
