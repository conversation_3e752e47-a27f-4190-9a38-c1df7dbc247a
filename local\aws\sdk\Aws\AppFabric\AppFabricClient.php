<?php
namespace Aws\AppFabric;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AppFabric** service.
 * @method \Aws\Result batchGetUserAccessTasks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchGetUserAccessTasksAsync(array $args = [])
 * @method \Aws\Result connectAppAuthorization(array $args = [])
 * @method \GuzzleHttp\Promise\Promise connectAppAuthorizationAsync(array $args = [])
 * @method \Aws\Result createAppAuthorization(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAppAuthorizationAsync(array $args = [])
 * @method \Aws\Result createAppBundle(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAppBundleAsync(array $args = [])
 * @method \Aws\Result createIngestion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createIngestionAsync(array $args = [])
 * @method \Aws\Result createIngestionDestination(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createIngestionDestinationAsync(array $args = [])
 * @method \Aws\Result deleteAppAuthorization(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAppAuthorizationAsync(array $args = [])
 * @method \Aws\Result deleteAppBundle(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAppBundleAsync(array $args = [])
 * @method \Aws\Result deleteIngestion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteIngestionAsync(array $args = [])
 * @method \Aws\Result deleteIngestionDestination(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteIngestionDestinationAsync(array $args = [])
 * @method \Aws\Result getAppAuthorization(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAppAuthorizationAsync(array $args = [])
 * @method \Aws\Result getAppBundle(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAppBundleAsync(array $args = [])
 * @method \Aws\Result getIngestion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getIngestionAsync(array $args = [])
 * @method \Aws\Result getIngestionDestination(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getIngestionDestinationAsync(array $args = [])
 * @method \Aws\Result listAppAuthorizations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAppAuthorizationsAsync(array $args = [])
 * @method \Aws\Result listAppBundles(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAppBundlesAsync(array $args = [])
 * @method \Aws\Result listIngestionDestinations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listIngestionDestinationsAsync(array $args = [])
 * @method \Aws\Result listIngestions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listIngestionsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result startIngestion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startIngestionAsync(array $args = [])
 * @method \Aws\Result startUserAccessTasks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startUserAccessTasksAsync(array $args = [])
 * @method \Aws\Result stopIngestion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopIngestionAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateAppAuthorization(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAppAuthorizationAsync(array $args = [])
 * @method \Aws\Result updateIngestionDestination(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateIngestionDestinationAsync(array $args = [])
 */
class AppFabricClient extends AwsClient {}
