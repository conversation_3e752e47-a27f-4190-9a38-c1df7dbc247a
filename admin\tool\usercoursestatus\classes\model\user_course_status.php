<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Version metadata for the repository_pluginname plugin.
 *
 * @package   repository_pluginname
 * @copyright 2025, author_fullname <author_link>
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace tool_usercoursestatus\model;

use core\persistent;
use context_system;
use lang_string;
use tool_usercoursestatus\utils\constants;
use tool_usercoursestatus\event\user_course_status_created;
use tool_usercoursestatus\event\user_course_status_updated;
use tool_usercoursestatus\query\builder;

/**
 * Class status
 *
 * @package    tool_usercoursestatus
 * @copyright  2024 Revvo
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class user_course_status extends persistent
{
	/** @var string */
	const TABLE = 'tool_usercoursestatus';

	protected string|null $string = null;

	/**
	 * Return the definition of the properties of this model.
	 *
	 * @return array
	 */
	protected static function define_properties()
	{
		return [
			'userid' => [
				'type' => PARAM_INT,
				'null' => NULL_NOT_ALLOWED,
			],
			'courseid' => [
				'type' => PARAM_INT,
				'null' => NULL_NOT_ALLOWED,
			],
			'status' => [
				'type' => PARAM_INT,
				'null' => NULL_NOT_ALLOWED,
				'choices' => constants::get_status_constants()
			]
		];
	}

	/**
	 * Validate the courseid ID.
	 *
	 * @param int $value The value.
	 * @return true|lang_string
	 */
	protected function validate_courseid($value)
	{
		global $DB;

		$course = $DB->get_record('course', ['id' => $value]);

		if (!$course) {
			return new lang_string('invalidcourseid', 'error');
		}

		return true;
	}

	/**
	 * Validate the userid ID.
	 *
	 * @param int $value The value.
	 * @return true|lang_string
	 */
	protected function validate_userid($value)
	{
		global $USER;

		$user = \core_user::get_user($value);

		if (!$user) {
			return new lang_string('invaliduserid', 'error');
		}

		return true;
	}

	/**
	 * Validate the status.
	 *
	 * @param int $value The value.
	 * @return true|lang_string
	 */
	protected function validate_status($value)
	{
		$validStatuses = constants::get_status_constants();
		if (!in_array($value, $validStatuses)) {
			return new lang_string('invalidstatus', 'tool_usercoursestatus');
		}

		return true;
	}

	/**
	 * Trigger the created event after created.
	 *
	 * @return int The ID of the created record.
	 */
	public function after_create()
	{
		global $USER;

		$event = user_course_status_created::create([
			'objectid' => $this->get('id'),
			'context' => context_system::instance(),
			'userid' => $USER->id,
			'other' => [
				'userid' => $this->get('userid'),
				'courseid' => $this->get('courseid'),
				'status' => $this->get('status')
			]
		]);

		return $event->trigger();
	}

	/**
	 * Atualiza o registro e dispara um evento de atualização com o status anterior.
	 *
	 * @param int $newStatus O novo status a ser definido.
	 * @return bool True se a atualização for bem-sucedida.
	 */
	public function update_with_event($new_status)
	{
		global $USER;

		$previous_status = $this->get('status');

		$this->set('status', $new_status);

		$result = parent::update();

		$event = user_course_status_updated::create([
			'objectid' => $this->get('id'),
			'context' => context_system::instance(),
			'userid' => $USER->id,
			'other' => [
				'userid' => $this->get('userid'),
				'courseid' => $this->get('courseid'),
				'previous_status' => $previous_status,
				'new_status' => $new_status
			]
		]);

		$event->trigger();

		return $result;
	}

	/**
	 * Update the status only if it is different from the current value.
	 *
	 * @param int $status The new status.
	 * @return bool True if the status was updated, false otherwise.
	 */
	public function update_status_if_different($status = null)
	{

		$current_status = (int) $this->get('status');

		if (is_null($status)) {
			$status_instance = $this->calculate_status();

			$status = $status_instance ? (int) $status_instance->status : constants::STATUS_ENROLED;
		}

		if ($current_status !== $status) {
			return $this->update_with_event($status);
		}

		return false;
	}

	/**
	 * Find the usercourse status
	 *
	 * @return object
	 */
	public function calculate_status()
	{
		global $DB;

		$builder = new builder($this->get('userid'), $this->get('courseid'));

		$query = $builder->get_query();
		$params = $builder->get_params();

		return $DB->get_record_sql($query, $params);
	}
}
