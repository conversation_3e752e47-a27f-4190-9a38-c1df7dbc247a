/**
 * Show more action for availablity information.
 *
 * @module     format_trails/availability_more
 * @copyright  2023
 */

/**
 * Availability info selectors.
 */
const Selectors = {
  availabilityinfo:
    "#page-course-view-trails #region-main .notifications .alert.alert-danger .availabilityinfo-error ul",
  availabilityinfoshowmore:
    "#region-main .notifications .alert.alert-danger .availabilityinfo-error ul li.showmore",
  dataregion: "availability-multiple",
  dataaction: "showmore",
};
/**
 * Initialise the eventlister for the showmore action on availability information.
 *
 * @method  init
 */
export const init = () => {
  window.onload = function () {
    document
      .querySelectorAll(Selectors.availabilityinfo)
      .forEach((e) => e.setAttribute("data-region", Selectors.dataregion));

    document
      .querySelectorAll(Selectors.availabilityinfoshowmore)
      .forEach((e) => e.setAttribute("data-action", Selectors.dataaction));
  };
};
