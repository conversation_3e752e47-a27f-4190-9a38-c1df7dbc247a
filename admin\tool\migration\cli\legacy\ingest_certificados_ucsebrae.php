<?php

require_once(__DIR__ . '/config.php');

use local_legacy\models\certificate\certificate;
use local_legacy\models\certificate\certificate_issue;

$filepath = "$csv_path/certificados-ucsebrae.csv.csv";
$reader = new tool_migration\importers\readers\csv_reader($filepath);



$counter = 0;
foreach ($reader->read('') as $row) {
    try {

    } catch (\Throwable $th) {
        mtrace($th->getMessage());
    }
}

print_r("\nIMPORTADOS: $counter");


function get_certificate_id($codcurso) : int {
    $name = "UCSEBRAE_$codcurso";
    if($certificate = certificate::get_record(['name' => $name])){
        return $certificate->get('id');
    }
+
    $certificate = new certificate(0, (object)[
        'name' => $name,
        'solutiontype'
    ]);
}