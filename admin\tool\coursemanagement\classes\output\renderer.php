<?php
/**
 * @package    tool
 * @subpackage coursemanagement
 * @copyright  2023 Revvo
 */

namespace tool_coursemanagement\output;

use plugin_renderer_base;
use local_courseblockapi\traits\course_trait;

require_once($CFG->dirroot."/admin/tool/coursemanagement/lib.php");

class renderer extends plugin_renderer_base{
    use course_trait;

	public function __construct() {
		global $CFG;

        $this->data = new \stdClass();
		$this->data->wwwroot = $CFG->wwwroot;
		$this->data->sesskey = sesskey();
		$this->data->current_url = new \moodle_url("/admin/tool/coursemanagement/index.php");
	}

	public function output(){
		global $OUTPUT, $DB, $CFG;

		// Verificar se o usuário tem a capacidade para gerenciar áreas de interesse
		$this->data->hasinterestareascap = has_capability('tool/interestareas:manage', \context_system::instance());

		$search = optional_param('search', "", PARAM_TEXT);
		$categories = $this->course_category_tree($search)[0]["categories"];
		$categorylist = [];

		foreach(\core_course_category::make_categories_list() as $id => $cat){
			$categorylist[] = [
				"id" => $id,
				"name" => $cat,
			];
		}

		$this->data->search = $search;
		$this->data->total_categories = $DB->count_records_sql("SELECT COUNT(*) FROM {course_categories}");
		$this->data->total_courses = $this->count_courses_in_category();
		$this->data->categories = $this->renderTree($categories, null, false, $search);
		$this->data->categorylist = $categorylist;

		return $OUTPUT->render_from_template('tool_coursemanagement/view', $this->data);
	}

	public function renderTree($categories, int $parentid = null, bool $is_child = false, string $search = null){
		global $OUTPUT, $PAGE;

		$ul_params = [
			"open_ul" => true,
			"id" => $parentid,
			"is_child" => $is_child,
		];

		$list = $OUTPUT->render_from_template('tool_coursemanagement/category', $ul_params);

		foreach($categories as $category) {
			$total_courses = $this->count_courses_in_category($category["id"], $search);

			if($search && !$total_courses){
				continue;
			}
			$category['returnurl'] = $PAGE->url->out(false);
			$category['wwwroot'] = $this->data->wwwroot;
			$category['sesskey'] = $this->data->sesskey;
			$category['total_courses'] = $total_courses;
			$category['is_child'] = $is_child;
			$category['content'] = $category;
			$category['has_learningflix'] = category_has_course_learningflix($category["id"]);
			$category['canedit'] = !$category['has_learningflix'] || is_siteadmin() ? true : false;
			$category['children'] = $this->renderTree($category['categories'], $category['id'], true, $search);
			$category['has_collapsibles'] = $category['has_courses'] || $category['has_children'] ? true : false;
			$list .= $OUTPUT->render_from_template('tool_coursemanagement/category', $category);
		}

		$list .= $list ? $OUTPUT->render_from_template('tool_coursemanagement/category', ["close_ul" => true]) : "";

		return $list;
	}

	public function course_category_tree(string $search = null, ?\core_course_category $core_category = null, array &$branch = []){
		global $DB;

		if($core_category === null){
			$core_category = \core_course_category::top();
		}

		$children = $core_category->get_children();
		$courses = $core_category->get_courses();

		$cat_branch = [
			'id' => $core_category->id,
			'name' => $core_category->name,
			'parent' => $core_category->parent,
			'visible' => $core_category->visible,
			'url' => new \moodle_url("/course/index.php", ["categoryid" => $core_category->id]),
			'has_children' => !empty($children),
			'categories' => [],
			'has_courses' => !empty($courses),
		];

		foreach ($children as $child_category) {
			$this->course_category_tree($search, $child_category, $cat_branch['categories']);
		}

		// // Adding courses
		foreach ($courses as $course) {
			if(tool_coursemanagement_is_learningflix($course->id) && !is_siteadmin()){
				continue;
			}

			if(
			  $search && (
			  !strstr(strtolower($course->fullname), strtolower($search)) &&
			  !strstr(strtolower($course->shortname), strtolower($search))
			  )
			){
				continue;
			}

			$cat_branch['courses'][] = [
				'id' => $course->id,
				'fullname' => $course->fullname,
				'visible' => $course->visible,
				'category' => $course->category,
				'image' => $this->get_course_image($course, "object")->card,
				'url' => $this->get_course_url($course->id),
			];
		}

		$branch[] = $cat_branch;

		return $branch;
	}

	public function count_courses_in_category($categoryid = null, $search = ""){
		global $DB;

		$and_search  = "";
		$and_search .= $categoryid ? "AND (ct.path LIKE '/{$categoryid}/%' OR c.category = {$categoryid}) " : "";
		$and_search .= $search ? "AND (c.fullname LIKE '%{$search}%' OR c.shortname LIKE '%{$search}%') " : "";

		$count = $DB->count_records_sql("
			SELECT COUNT(*)
			FROM {course} c
			JOIN {course_categories} ct ON (ct.id = c.category)
			WHERE 1 = 1
			{$and_search}
		");

		return $count;
	}
}