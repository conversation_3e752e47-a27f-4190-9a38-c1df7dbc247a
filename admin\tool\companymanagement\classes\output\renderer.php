<?php

namespace tool_companymanagement\output;

use context_system;
use tool_companymanagement\repositories\company_repository;
use plugin_renderer_base;
use stdClass;
use moodle_url;

class renderer extends plugin_renderer_base
{
	/**
	 * Return the output for this renderer.
	 */
	public function output()
	{
		global $OUTPUT, $CFG, $PAGE;

		$data = $this->prepare_data();
		$data->wwwroot = $CFG->wwwroot;
		$data->sesskey = sesskey();

		$PAGE->requires->js_call_amd('tool_companymanagement/form', 'init', []);

		return $OUTPUT->render_from_template('tool_companymanagement/view', $data);
	}

	/**
	 * Prepare and return data for rendering.
	 *
	 * @return stdClass
	 */
	private function prepare_data()
	{
		global $OUTPUT;

		$params = $this->get_request_params();
		$repository = new company_repository();
		$this->update_repository_params($repository, $params);

		$rows = $repository->get_paginted_data($params['search']);
		$count = $repository->get_paginted_total($params['search']);

		$table_rows = $this->format_rows($rows);

		$data = new stdClass();
		$data->current_url = new moodle_url("/admin/tool/companymanagement/index.php", $params);
		$data->total = $count;
		$data->search = $params['search'];
		$data->sort = $params['sort'];
		$data->dir = $params['dir'];
		$data->table = $this->build_table($table_rows, $params, $count);
		$data->paging_bar = $OUTPUT->paging_bar($count, $params['page'], $params['perpage'], $data->current_url);
		$data->can_create = has_capability('tool/companymanagement:create', context_system::instance());
		$data->can_update = has_capability('tool/companymanagement:update', context_system::instance());
		$data->can_delete = has_capability('tool/companymanagement:delete', context_system::instance());

		return $data;
	}

	/**
	 * Get and sanitize request parameters.
	 *
	 * @return array
	 */
	private function get_request_params()
	{
		return [
			'page' => optional_param('page', 0, PARAM_INT),
			'perpage' => optional_param('perpage', 30, PARAM_INT),
			'sort' => optional_param('sort', 'name', PARAM_RAW),
			'dir' => optional_param('dir', SORT_ASC, PARAM_INT),
			'search' => optional_param('search', '', PARAM_TEXT),
		];
	}

	/**
	 * Updates the repository with pagination and sorting parameters.
	 *
	 * @param object $repository The repository instance to update.
	 * @param array $params An associative array containing 'page', 'perpage', 'sort', and 'dir' keys.
	 */
	private function update_repository_params(&$repository, $params)
	{
		$repository->set_page($params['page']);
		$repository->set_pagesize($params['perpage']);
		$repository->set_sort($params['sort']);
		$repository->set_order($params['dir']);
	}

	/**
	 * Format rows for table rendering.
	 *
	 * @param array $rows
	 * @return array
	 */
	private function format_rows(array $rows)
	{
		$formatted_rows = [];

		foreach ($rows as $row) {

			$table_row = new stdClass();
			$table_row->id = $row->get('id');
			$table_row->name = $row->get('name');
			$table_row->cnpj = $row->get('cnpj');
			$table_row->state = $row->get('state');
			$formatted_rows[] = $table_row;
		}

		return $formatted_rows;
	}

	/**
	 * Build table data structure.
	 *
	 * @param array $rows
	 * @param array $params
	 * @param int $count
	 * @return stdClass
	 */
	private function build_table(array $rows, array $params, int $count)
	{
		$table = new stdClass();
		$table->has_data = (bool) $count;
		$table->columns = $this->build_columns($params['sort'], $params['dir']);
		$table->rows = array_values($rows);

		return $table;
	}

	/**
	 * Build column metadata for table.
	 *
	 * @param string $current_sort
	 * @param string $current_dir
	 * @return array
	 */
	private function build_columns(string $current_sort, int $current_dir)
	{
		$columns = [
			'name' => get_string('company_name', 'tool_companymanagement'), //"Razão social"
			'cnpj' => "CNPJ",
			'state' => get_string('uf', 'tool_companymanagement'), // UF
		];

		$table_columns = [];
		foreach ($columns as $field => $name) {
			$toggleSort = $current_dir === SORT_ASC ? SORT_DESC : SORT_ASC;
			$sortDirection = $current_dir === SORT_ASC ? "up" : "down";

			$table_columns[] = (object) [
				'field' => $field,
				'name' => $name,
				'sort' => $current_sort === $field ? $toggleSort : SORT_ASC,
				'sorted' => $current_sort === $field ? $sortDirection : "",
				'sorted_up' => $current_sort === $field && $sortDirection === "up",
				'sorted_down' => $current_sort === $field && $sortDirection === "down",
				'sortable' => true,
			];
		}

		return $table_columns;
	}
}
