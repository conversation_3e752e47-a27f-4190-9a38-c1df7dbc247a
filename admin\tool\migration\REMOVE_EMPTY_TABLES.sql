USE dbUcSebrae;

-- Variável que armazenará os comandos
DECLARE @sql NVARCHAR(MAX) = N'';

-- 1. <PERSON>abilit<PERSON> todas as FKs do banco
SELECT @sql += '
ALTER TABLE [' + OBJECT_SCHEMA_NAME(parent_object_id) + '].[' + OBJECT_NAME(parent_object_id) + '] 
NOCHECK CONSTRAINT [' + name + '];'
FROM sys.foreign_keys;

-- 2. Gera os DROPs das tabelas vazias
SELECT @sql += '
IF EXISTS (
    SELECT 1
    FROM sys.partitions p
    JOIN sys.tables t2 ON t2.object_id = p.object_id
    JOIN sys.schemas s2 ON t2.schema_id = s2.schema_id
    WHERE s2.name = ''' + s.name + ''' AND t2.name = ''' + t.name + ''' AND p.index_id IN (0,1)
    GROUP BY s2.name, t2.name
    HAVING SUM(p.rows) = 0
)
BEGIN
    PRINT ''Dropping [' + s.name + '].[' + t.name + ']'';
    DROP TABLE [' + s.name + '].[' + t.name + '];
END;
'
FROM 
    sys.tables t
JOIN 
    sys.schemas s ON t.schema_id = s.schema_id
LEFT JOIN 
    sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0,1)
GROUP BY 
    s.name, t.name
HAVING 
    SUM(p.rows) = 0;

-- 3. Reativa as FKs (apenas nas que ainda existem)
SELECT @sql += '
IF OBJECT_ID(''' + OBJECT_SCHEMA_NAME(parent_object_id) + '.' + OBJECT_NAME(parent_object_id) + ''') IS NOT NULL
BEGIN
    ALTER TABLE [' + OBJECT_SCHEMA_NAME(parent_object_id) + '].[' + OBJECT_NAME(parent_object_id) + '] 
    WITH CHECK CHECK CONSTRAINT [' + name + '];
END;'
FROM sys.foreign_keys;

-- Executa tudo
EXEC sp_executesql @sql;
