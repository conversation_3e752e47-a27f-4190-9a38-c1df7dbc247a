[x] Migrar usuários e gestores
[x] Migrar segmentos como públicos-alvo
[x] Migrar membros de públicos-alvo
[x] Migrar cursos legados
[x] Migrar situações de matrícula finais em cursos legados
[x] Migrar trilhas legadas
[x] Migrar situações de matrícula finais em trilhas legadas
[x] Modificar plugins de empresas
[+] Modificar integração SGF (Empresas)
    [x] Implementar
    [] Testes unitários
[] Modificar integração SGF (lógica)
    [] Implementar
    [] Testes unitários
[x] Migrar empresas
[x] Migrar somente empresas dos usuários
[] Migrar cursos externos (extracurriculares)
[] Criar plugin local_legacy
    [+] Estrutura para armazenar cursos
    [+] Estrutura para armazenar trilhas
    [+] Estrutura para armazenar turmas de cursos
    [+] Estrutura para armazenar turmas de trilhas
    [+] Estrutura para progresso de usuários em cursos
    [+] Estrutura para progresso de usuários em trilhas
    [+] Estrutura para segmentos
    [+] Estrutura para usuários
    [+] Estrutura para usuários x segmentos
    [+] Estrutura turmas x segmentos
    [+] Estrutura para empresas
    [+] Estrutura para usuários x empresas
    [] Estrutura para trabalhos e feedbacks dos professores
        [+] Trabalhos
        [+] Trabalhos x alunos
    [+] Estrutura para armazenar pesquisas de satisfação
        [+] Feedback V1
        [+] Feedbacks de cursos
        [+] Feedbacks de trilhas
    [+] Estrutura para armazenar corpo docente
    [+] Estrutura de correlação curso x corpo docente
    [+] Estrutura de correlação de trilha x corpo docente
    [] Notas de atividades detalhadas por curso
    [] Notas de atividade detalhadas por trilha
    [] Funcionalidade de certificados
        [] Estrutura para certificados de trilha
        [] Estrutura para certificados de curso
        [] Implementar plugin
    [] Validação de certificados
[] Modificar página de certificados para buscar no local_legacy

# QUERIES A SEREM EXPORTADAS (EM ORDEM):
[x] Empresas.sql -> empresas.csv
[x] Estudantes.sql -> estudantes.csv
[] Gestores.sql -> gestores.csv
[x] Cursos.sql -> cursos.csv
[x] Trilhas.sql -> trilhas.csv
[] CursosExternos.sql -> cursos-externos.csv
[x] CorpoDocente.sql -> corpo-docente.csv
[x] Segmentos.sql -> segmentos.csv
[x] UsuariosPorSegmento.sql -> usuarios-segmentos.csv
[x] EmpresaAluno.sql -> empresa-aluno.csv
[x] TurmasDeCursos.sql -> turmas-cursos.csv
[x] TurmasDeTrilhas.sql -> turmas-trilhas.csv
[x] CorpoDocentePorTurma.sql -> corpo-docente-curso.csv
[x] CorpoDocentePorTurma.sql -> corpo-docente-trilha.csv
[x] PesquisasDeSatisfacaoV1.sql -> pesquisa-satisfacao-v1.csv
[x] TurmasPorSegmentos.sql -> segmentos-por-turma-curso.csv
[x] TurmasPorSegmentos.sql -> segmentos-por-turma-trilha.csv
[x] Trabalhos.sql -> trabalhos.csv
[x] TrabalhosAlunos.sql -> trabalhos-alunos.csv
[x] ProgressoDeAlunosEmCursos.sql -> progresso-de-alunos-em-cursos.csv
[x] ProgressoDeAlunosEmTrilhas.sql -> progress-de-alunos-em-trilhas.csv
[] CertificadosTrilhas.sql -> certificados-trilhas.csv
[] CertificadosDeCursos.sql -> certificados-cursos.csv
[x] PesquisasDeSatisfacaoV2.sql -> pesquisas-satisfacao-v2-cursos.csv
[x] PesquisasDeSatisfacaoV2.sql -> pesquisas-satisfacao-v2-trilhas.csv

// Tem que fazer as queries para notas detalhadas

# IMPORTADOS PARA O MOODLE (EM ORDEM):
[] Empresas
    [] ingestion/ingest_empresas.php
    [] import/import_empresas.php
[] Estudantes
    [] ingestion/ingest_estudantes.php
    [] import/import_estudantes.php
[] Gestores
    [] ingestion/ingest_gestores.php
    [] import/import_gestores.php
[] Trilhas
    [] ingestion/ingest_trilhas.php
    [] import/import_trilhas.php
[] Segmentos
    [] ingestion/ingest_segmentos.php
    [] import/import_segmentos.php
[] Usuários x Segmentos
    [] ingestion/ingest_usuarios_segmentos.php
    [] import/import_usuarios_segmentos.php
[] Cursos externos
    [] PENDENTE
    [] PENDENTE
[] Progresso em cursos
    [] ingestion/ingest_progresso_em_cursos.php
    [] import/import_conclusoes_de_cursos.php
[] Progresso em trilhas
    [] ingestion/ingest_progresso_em_trilhas.php
    [] import/import_conclusoes_de_trilhas.php

# IMPORTADOS PARA O LOCAL_LEGACY (EM ORDEM)
[] Empresas
    [] legacy/ingest_empresas.php
[] Alunos
    [] legacy/ingest_alunos.php
[] Cursos
    [] legacy/ingest_cursos.php
[] Trilhas
    [] legacy/ingest_trilhas.php
[] Progresso em cursos
    [] legacy/ingest_situacao_cursos.php
[] Progresso em trilhas
    [] legacy/ingest_situacao_trilhas.php
[] Corpo docente
    [] legacy/ingest_corpo_docente.php
[] Segmentos
    [] legacy/segmento_aluno.php
[] Segmento x aluno
    [] legacy/ingest_segmento_alunos.php
[] Empresa x aluno
    [] legacy/ingest_empresa_alunos.php
[] Turmas de cursos
    [] legacy/ingest_turmas_cursos.php
[] Turmas de trilhas
    [] legacy/ingest_turmas_trilhas.php
[] Corpo docente por turma de curso
    [] legacy/ingest_corpo_docente_curso.php
[] Corpo docente por turma de trilha
    [] legacy/ingest_corpo_docente_trilha.php
[] Trabalhos
    [] legacy/ingest_trabalhos.php
[] Trabalho x aluno
    [] legacy/ingest_trabalhos_alunos.php
[] Segmento x turma (curso)
    [] legacy/ingest_segmentos_turma_curso.php
[] Segmento x turma (trilha)
    [] legacy/ingest_segmentos_turma_trilha.php
[] Pesquisa de satisfação V1
    [] legacy/ingest_feedback_v1.php
[] Pesquisa de satisfação (Cursos)
    [] legacy/ingest_feedback_cursos.php
[] Pesquisa de satisfacao (Trilhas)
    [] legacy/ingest_feedback_trilhas.php
[] Importar certificados UCSEBRAE (legado²)
    [] legacy/ingest_certificados_ucsebrae.php



// Tem que importar o dump de certificados da EADTECH