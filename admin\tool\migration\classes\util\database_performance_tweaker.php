<?php namespace tool_migration\util;

defined('MOODLE_INTERNAL') || die();

class database_performance_tweaker {

    /**
     * Apply session-level performance tweaks for faster inserts.
     * Disables foreign key checks, reduces flush frequency, and disables binlog syncing.
     */
    public static function apply() {
        global $DB;

        $DB->execute("SET SESSION foreign_key_checks = 0");
        $DB->execute("SET SESSION innodb_flush_log_at_trx_commit = 2");
        $DB->execute("SET SESSION sync_binlog = 0");
    }

    /**
     * Restore default session-level settings after migration.
     */
    public static function restore() {
        global $DB;

        $DB->execute("SET SESSION foreign_key_checks = 1");
        $DB->execute("SET SESSION innodb_flush_log_at_trx_commit = 1");
        $DB->execute("SET SESSION sync_binlog = 1");
    }

    /**
     * Disable autocommit mode.
     * Useful when wrapping operations in manual transactions.
     */
    public static function disable_autocommit() {
        global $DB;
        $DB->execute("SET SESSION autocommit = 0");
    }

    /**
     * Enable autocommit mode (default behavior).
     */
    public static function enable_autocommit() {
        global $DB;
        $DB->execute("SET SESSION autocommit = 1");
    }
}