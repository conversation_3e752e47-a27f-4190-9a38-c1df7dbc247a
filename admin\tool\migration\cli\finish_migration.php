<?php

if(!defined('CLI_SCRIPT')){
    define('CLI_SCRIPT', true);
}

require(__DIR__ . '/../../../../config.php');
require_once($CFG->libdir . '/clilib.php');

cli_writeln("===> Finalizando ambiente de migração...");

// Enabling logstore
set_config('enabled_stores', 'logstore_standard', 'tool_log');
cli_writeln("✔ Logstore reativado");

// Rebuilding cache
\cache_helper::purge_all();
cli_writeln("✔ Caches reconstruídos");

// Enabling cron
set_config('cron_enabled', 1);
cli_writeln("✔ Cron reativado");

// Disabling maintenance
set_config('maintenance_enabled', 0);
unset_config('maintenance_later');
cli_writeln("✔ Modo de manutenção desativado");

cli_writeln("===> Migração finalizada com sucesso.");