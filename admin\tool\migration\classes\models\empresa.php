<?php namespace tool_migration\models;

use tool_migration\util\ingestion_helper;

defined('MOODLE_INTERNAL')||die();

class empresa extends abstract_migratable_entity{
    const TABLE = 'eadtech_empresas';

    public static function define_identifier(): string {
        return 'source_codempresa';
    }
    
    protected static function define_model_properties(): array{
        return [
            'source_codempresa' => [
                'type' => PARAM_INT,
            ],
            'source_razao_social' => [
                'type' => PARAM_TEXT,
            ],
            'source_cnpj' => [
                'type' => PARAM_TEXT,
            ],
            'source_datacadastro' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_estado' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_dataabertura' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_excluido' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_dataalteracao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_fornecedorsolucoes' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
             'source_nome_fantasia' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
        ];
    }

    public function get_idnumber(): string{
        return 'LEGADO-' . $this->get('source_codempresa');
    }

    public function to_company(): object {
        $company = (object)[
            'cnpj' => ingestion_helper::only_numbers($this->get('source_cnpj')),
            'name' => $this->get('source_razao_social'),
            'state' => $this->get('source_estado'),
            'timecreated' => ingestion_helper::str_to_timestamp($this->get('source_datacadastro')),
            'timemodified' => time(),
            'deleted' => ingestion_helper::str_to_bool($this->get('source_excluido')),
        ];

        if($id = $this->get('instanceid')){
            $company->id = $id;
        }

        return $company;
    }
}
