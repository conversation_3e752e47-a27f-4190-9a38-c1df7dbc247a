{"version": 3, "file": "placeholder.min.js", "sources": ["../../../src/local/courseindex/placeholder.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Course index placeholder replacer.\n *\n * @module     core_courseformat/local/courseindex/placeholder\n * @class      core_courseformat/local/courseindex/placeholder\n * @copyright  2021 <PERSON><PERSON>n <PERSON> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {BaseComponent} from 'core/reactive';\nimport Templates from 'core/templates';\nimport {getCurrentCourseEditor} from 'core_courseformat/courseeditor';\nimport Pending from 'core/pending';\n\nexport default class Component extends BaseComponent {\n\n    /**\n     * Static method to create a component instance form the mustache template.\n     *\n     * @param {element|string} target the DOM main element or its ID\n     * @param {object} selectors optional css selector overrides\n     * @return {Component}\n     */\n    static init(target, selectors) {\n        return new this({\n            element: document.getElementById(target),\n            reactive: getCurrentCourseEditor(),\n            selectors,\n        });\n    }\n\n    /**\n     * Component creation hook.\n     */\n    create() {\n        // Add a pending operation waiting for the initial content.\n        this.pendingContent = new Pending(`core_courseformat/placeholder:loadcourseindex`);\n    }\n\n    /**\n     * Initial state ready method.\n     *\n     * This stateReady to be async because it loads the real courseindex.\n     *\n     * @param {object} state the initial state\n     */\n    async stateReady(state) {\n\n        // Check if we have a static course index already loded from a previous page.\n        if (!this.loadStaticContent()) {\n            await this.loadTemplateContent(state);\n        }\n    }\n\n    /**\n     * Load the course index from the session storage if any.\n     *\n     * @return {boolean} true if the static version is loaded form the session\n     */\n    loadStaticContent() {\n        // Load the previous static course index from the session cache.\n        const index = this.reactive.getStorageValue(`courseIndex`);\n        if (index.html && index.js) {\n            Templates.replaceNode(this.element, index.html, index.js);\n            this.pendingContent.resolve();\n            return true;\n        }\n        return false;\n    }\n\n    /**\n     * Load the course index template.\n     *\n     * @param {Object} state the initial state\n     */\n    async loadTemplateContent(state) {\n        // Collect section information from the state.\n        const exporter = this.reactive.getExporter();\n        const data = exporter.course(state);\n        try {\n            // To render an HTML into our component we just use the regular Templates module.\n            const {html, js} = await Templates.renderForPromise(\n                'format_trails/local/courseindex/courseindex',\n                data,\n            );\n            Templates.replaceNode(this.element, html, js);\n            this.pendingContent.resolve();\n\n            // Save the rendered template into the session cache.\n            this.reactive.setStorageValue(`courseIndex`, {html, js});\n        } catch (error) {\n            this.pendingContent.resolve(error);\n            throw error;\n        }\n    }\n}\n"], "names": ["Component", "BaseComponent", "target", "selectors", "this", "element", "document", "getElementById", "reactive", "create", "pendingContent", "Pending", "state", "loadStaticContent", "loadTemplateContent", "index", "getStorageValue", "html", "js", "replaceNode", "resolve", "data", "getExporter", "course", "Templates", "renderForPromise", "setStorageValue", "error"], "mappings": ";;;;;;;;mLA6BqBA,kBAAkBC,oCASvBC,OAAQC,kBACT,IAAIC,KAAK,CACZC,QAASC,SAASC,eAAeL,QACjCM,UAAU,0CACVL,sBAORM,cAESC,eAAiB,IAAIC,mFAUbC,OAGRR,KAAKS,2BACAT,KAAKU,oBAAoBF,OASvCC,0BAEUE,MAAQX,KAAKI,SAASQ,wCACxBD,MAAME,OAAQF,MAAMG,yBACVC,YAAYf,KAAKC,QAASU,MAAME,KAAMF,MAAMG,SACjDR,eAAeU,WACb,6BAUWR,aAGhBS,KADWjB,KAAKI,SAASc,cACTC,OAAOX,iBAGnBK,KAACA,KAADC,GAAOA,UAAYM,mBAAUC,iBAC/B,+CACAJ,yBAEMF,YAAYf,KAAKC,QAASY,KAAMC,SACrCR,eAAeU,eAGfZ,SAASkB,8BAA+B,CAACT,UAAMC,QACtD,MAAOS,kBACAjB,eAAeU,QAAQO,OACtBA"}