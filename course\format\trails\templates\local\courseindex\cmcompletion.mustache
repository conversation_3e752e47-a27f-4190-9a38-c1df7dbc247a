{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/courseindex/cmcompletion

    Displays a course index course-module entry.

    Example context (json):
    {
        "state": 1,
        "iscomplete": true,
        "isincomplete": true,
        "isfail": true,
        "hasstate": true
    }
}}
{{#iscomplete}}
    <span class="completioninfo completion_complete" data-for="cm_completion" data-value="{{state}}">
        <i class="fa fa-circle-check fa-lg" title="{{#str}} done, completion {{/str}}"></i>
    </span>
{{/iscomplete}}
{{#isincomplete}}
    <span class="completioninfo completion_incomplete" data-for="cm_completion" data-value="{{state}}">
        <i class="fa fa-circle fa-lg" title="{{#str}} todo, completion {{/str}}"></i>
    </span>
{{/isincomplete}}
{{#isfail}}
    <span class="completioninfo completion_fail" data-for="cm_completion" data-value="{{state}}">
        <i class="fa fa-circle-xmark fa-lg" title="{{#str}} failed, completion {{/str}}"></i>
    </span>
{{/isfail}}
{{^hasstate}}
    <span class="completioninfo completion_none" data-for="cm_completion" data-value="{{state}}"></span>
{{/hasstate}}
