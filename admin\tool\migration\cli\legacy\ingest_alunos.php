<?php

use tool_migration\models\estudante;

require_once(__DIR__ . '/config.php');

foreach (estudante::get_pending_legacy_upserts($limit) as $estudante) {
    try {
        $record = $estudante->to_record();
        $record->userid = $record->instanceid;

        $legacyid = \local_legacy\models\aluno::upsert_from_migration_table_data($record);

        if($legacyid){
            $estudante->set('legacyid', $legacyid);
            $estudante->set('needs_update', 0);
            $estudante->save();

            $cpf = $estudante->get('source_cpf');
            mtrace("Usuário $cpf importado para o local_legacy");
        }
    } catch (\Throwable $th) {
        mtrace($th->getMessage());
    }
}