<?php namespace tool_migration;


defined('MOODLE_INTERNAL') || die();

use tool_migration\models\curso;
use tool_migration\util\ingestion_helper;
use local_degreed_integration\degreed\entities\skill;
use local_ssystem\constants\custom_course_fields;
use text_progress_trace;
use tool_migration\importers\curso_importer;

final class curso_importer_test extends \advanced_testcase {

    protected $data = [];

    protected function setUp(): void {
        parent::setUp();

        $this->resetAfterTest(true);

        $this->data = [
            'source_keywords' => 'Atendimento Digital,Processos e ferramentas de comunicação,canais de comunicação e atendimento,padrões e modelos de atendimento',
            'source_cargahoraria' => '12.00',
            'source_tipo_solucao' => 'Curso presencial',
            'source_nivel_de_complexidade' => '<p><strong>I</strong>ntermediário</p>
        ',
            'source_datacriacao' => '2022-09-29 13:44:46.573',
            'source_datacriacao' => '2023-03-01 11:23:00.540',
            'source_descricao' => 'Atenção - para se inscrever nesta Certificação você deve ter concluído com êxito os cursos pré-requisito para esta fase e ao se inscrever neste curso você concorda com os termos da plataforma e com as regras de participação informadas no botão "saiba mais/ver detalhes/termo de aceite".',
            'source_nome' => 'CCPS - Disciplina de empreendedorismo para a educação profissional - V1 v0',
            'source_nomecursomenu' => 'CCPS - Disciplina de empreendedorismo para a educação profissional - V1',
            'source_apresentacao' => '<p>Sejam bem-vindos ao Primeiro encontro de Agentes de conhecimento de 2022. Este evento marca o lançamento da nova plataforma da Universidade corporativa e abre um espaço para o alinhamento de todos os agentes em relação ao atual modelo de governança da UCSebrae, assim como se configura como um espaço para discussão, levantamento de necessidades e esculta dos agentes a respeito de seus desafios para a gestão da educação corporativa no Sistema Sebrae.</p>

        <p>A&nbsp;<strong>Universidade Corporativa Sebrae</strong>, comprometida com o aprimoramento contínuo das ações de educação corporativa no Sistema Sebrae, contratou recentemente nova plataforma educacional. Contará agora com um serviço que se atualiza com a agilidade do mercado, com suporte eficiente e que apresenta melhor experiência de uso em ambiente desktop e móvel. Essa nova plataforma prevê que os SEBRAE/UF também a utilizem para oferta e gestão de soluções de educação corporativa</p>
        ',
            'source_objetivos' => '<p>Compreender a governança e processo das ações de educação corporativa no Sistema Sebrae a partir do novo ecossistema tecnológico da UCSebrae.<br />
        Estar apto para gerenciar as ações de desenvolvimento de sua UC por meio do Sistema de gestão de ações educacionais - SGAE.</p>
        ',
            'source_conteudo_programatico' => '<p>Visão geral da plataforma para usuário:<br />
        Visão geral; navegação; estrutura; recursos; definições"<br />
        Visão geral da plataforma gestor: navegação, estrutura, definições e relação com governança<br />
        Gestão de usuários: alunos e tutores/professores<br />
        Capacitações presenciais: cadastro de solução<br />
        Capacitações presenciais: turmas de cursos; recursos de turma: certificados padrão; avaliações de conhecimento; pesquisas de satisfação; cadastro de aulas</p>

        <p>Capacitações presenciais: lista de presença; convocação e comunicação<br />
        Capacitações presenciais: realização do evento (tutor/gestor)&nbsp;<br />
        Capacitações presenciais: finalização e lançamento de dados<br />
        Capacitações presenciais: gestão de resultados (relatórios)<br />
        Dúvidas finais e orientação de abertura de demandas</p>

        <p>Governança: aplicação para cursos online<br />
        Capacitações online: cadastro de solução (pós-solicitação de criação)<br />
        Capacitações online: cadastro de questionários e tarefas<br />
        Capacitações online: oferta de turmas (regular e contínua)<br />
        Capacitações online: interação/tutoria para ofertas contínuas</p>

        <p>Capacitações online:&nbsp; gestão de matrículas; finalização e lançamento de dados<br />
        Capacitações Sebrae/NA: gestão de matrículas de soluções ofertadas<br />
        Capacitações presenciais e online:&nbsp; pondo em prática- gestão de resultados<br />
        Gestão de resultados: relatórios e dashboard</p>
        ',
            'source_termo_de_aceite' => '<p>Ao me inscrever nesta capacitação, declaro que:</p>
        <p>I - Participar de ações de capacitação a distância fora do meu horário de jornada de trabalho, é uma opção e liberalidade como empregado do SEBRAE ou como parceiro do projeto ao qual estou vinculado, devendo priorizar a realização de meus estudos durante o experiente;</p>
        <p>II - Minha participação nas capacitações ofertadas pela Universidade corporativa Sebrae é voluntária, salvo em capacitações obrigatórias definidas pela Unidade de gestão de pessoas de meu estado ou de projetos dos quais faço parte em minha relação com o Sebrae;</p>
        <p>III - Não é indicada a participação nos cursos em período de férias ou afastamento por motivos de saúde e outros impedimentos legais;</p>
        <p>IV - Tomei conhecimento dos prazos de oferta de inscrição e do período de duração de cada solução educacional após minha inscrição conforme descrito nos detalhes;</p>
        <p>V - Caso necessite de prazo adicional para conclusão de minha capacitação, abrirei chamado pela Central de ajuda no portal antes do encerramento do prazo vigente da matrícula em curso;</p>
        <p>VI - Caso perca o prazo de solicitação de prorrogação ou reprove em alguma solução, poderei me inscrever novamente na solução, mas deverei reiniciar meus estudos, não sendo aproveitado os registros da matrícula anterior;</p>
        <p>VII - Se não tiver cancelado ou participado do curso até sua conclusão (navegação e atividades avaliativas) minha participação será considerada como abandono ou reprovada.</p>
        <p>Ao me inscrever nesta capacitação, declaro que:</p>
        <p>VIII - Algumas soluções educacionais possuem versão em pdf. para impressão ou leitura Off-line, devendo verificar no ambiente virtual se existe esta opção. A impressão de conteúdos só deve ser utilizada em casos de extrema necessidade para evitarmos consumo excessivo de recursos financeiros e materiais.</p>
        <p>IX - Não repassarei os arquivos em pdf. com conteúdo dos cursos para terceiros.</p>
        <p>X - Tenho ciência do cuidado que devo ter na elaboração de minhas atividades avaliativas, evitando apropriação indevida de atividades de outras pessoas. A publicação em fóruns e outros espaços de socialização de materiais é de minha responsabilidade. Em caso de identificação de cópia ou plágio informarei ao suporte do portal da UC, enviando as evidências.</p>
        <p>XI - A realização da pesquisa de satisfação é insumo necessário para a melhoria constante das soluções educacionais e minha contribuição é crucial.</p>
        <p>XII - O certificado de conclusão ou declaração de participação é gerado unicamente pelo portal da UCSebrae, após comprovação de aproveitamento mínimo de 70% (avaliação ou participação), podendo variar de acordo com a solução.</p>
        <p>XIII - O meu certificado somente terá validade se gerado pelo portal da UCSebrae com o respectivo código de segurança e poderá ser validado por qualquer pessoa por meio do QRcode ou link.</p>
        <p>XIV - Ao participar de algumas soluções educacionais poderei receber em minha conta de e-mails mensagens e outras informações sobre o andamento das atividades. A opção de bloqueio do recebimento desses alertas é de minha responsabilidade dentro do ambiente virtual de aprendizagem.</p>
        <p>Ao me inscrever nesta capacitação afirmo que:</p>
        <p>XV - Dúvidas técnicas e de operação dos sistemas devem ser registradas pelo Fale conosco.</p>',
            'source_ficha_tecnica' => '<p><strong>Conteudistas</strong>:</p>    <p><strong>Unidade de Acesso à Inovação e Tecnologia Sebrae Nacional</strong></p>    <p>Adriana Dantas Gonçalves Analista</p>    <p>Alexandre de Oliveira Ambrosini - Analista</p>    <p>Eliane Maria de Sant`Anna Ferreira Leite Analista</p>    <p><strong>Unidade de Atendimento Individual Sebrae Nacional</strong></p>    <p>Rodrigo Scherer Palácio - Analista UAI</p>    <p><strong>Validação do Conteúdo Técnico:</strong></p>    <p><strong>Centro Sebrae de Sustentabilidade Sebrae Mato Grosso</strong></p>    <p>Suênia Maria Cordeiro de Sousa Gerente</p>    <p>Jéssica Ferrari Analista</p>    <p><strong>Coordenação pedagógica</strong>:&nbsp;Mônica Valéria Costa Caribé Casemiro - Núcleo de Planejamento e produção/UCSebrae.</p>    <p><strong>Produção e Coordenação geral</strong>:&nbsp;Elias Alexandre Oliveira dos Santos</p>    <p>Última atualização: sexta, 9 Ago 2019, 12:17</p>  ',
            'source_requisitos' => '<p>HABILITAÇÃO: Consultoria &nbsp;<br />  ÁREA(S): Desenvolvimento Territorial<br />  SUBÁREA(S): 13.2 &nbsp; Planejamento Territorial&nbsp;</p>  ',
            'source_criterios_de_avaliacao' => '<p>Presença nos dois dias de capacitação.</p>
        ',
            'source_publico' => '<p>Gerente<br />
        Gerente adjunto<br />
        Assessor<br />
        Coordenador</p>
        ',
            'source_area_subarea' => '<p><strong>Área(s): </strong>(a) Empreendedorismo (b) Inovação</p>

        <p><strong>Subárea(s)</strong>: (a) Comportamento Empreendedor (b) Startup</p>

        <p><strong>Habilitação:</strong>Instrutoria</p>

        <p></p>
        ',
            'source_idcurso' => "ABCDEF",
            'source_codcurso' => 123456,
        ];
    }


    /**
     * @group !current
     */
    function test_course_creation(){
        global $DB;

        $coursecat = $this->getDataGenerator()->create_category();

        $curso = new curso(0, (object)$this->data);
        $curso->save();

        $importer = new curso_importer();
        // $importer = new curso_importer(new text_progress_trace());
        $importer->set_category($coursecat->id);
        $course = $importer->upsert_course($curso);

        $newcourse = get_course($course->id);
        \tool_lfxp\helpers\custom_fields\course\custom_course_field::load_custom_fields($newcourse);

        $this->assertEquals(
            12*HOURSECS,
            $newcourse->customfields->{custom_course_fields::WORKLOAD}->get_value()
        );

        $this->assertEquals(
            'LEGADO-' . $this->data['source_idcurso'], 
            $newcourse->idnumber
        );

        $this->assertStringContainsString(
            $this->data['source_nome'], 
            $newcourse->fullname
        );

        $this->assertTrue((bool)$newcourse->enablecompletion);

        $this->assertEquals(
            'Curso presencial',
            $newcourse->customfields->{custom_course_fields::SOLUTION_FORMAT}->export_value()
        );

        $this->assertEquals(
            custom_course_fields::COMPLEXITY_INTERMEDIATE,
            $newcourse->customfields->{custom_course_fields::COMPLEXITY_LEVEL}->export_value()
        );

        $this->assertStringContainsString(
            'Atenção', 
            $newcourse->summary
        );

        $this->assertStringContainsString(
            'Primeiro encontro', 
            $newcourse->customfields->{custom_course_fields::INTRODUCTION}->export_value()
        );

        $this->assertStringContainsString(
            'Compreender a governança', 
            $newcourse->customfields->{custom_course_fields::OBJECTIVES}->export_value()
        );

        $this->assertStringContainsString(
            'Visão geral', 
            $newcourse->customfields->{custom_course_fields::CURRICULUM}->export_value()
        );

        $this->assertStringContainsString(
            'me inscrever nesta', 
            $newcourse->customfields->{custom_course_fields::CONSENT_TERM}->export_value()
        );

        $this->assertStringContainsString(
            'Alexandre de Oliveira Ambrosini', 
            $newcourse->customfields->{custom_course_fields::TECHNICAL_SHEET}->export_value()
        );

        $this->assertStringContainsString(
            'HABILITAÇÃO', 
            $newcourse->customfields->{custom_course_fields::REQUIREMENTS}->export_value()
        );

        $this->assertStringContainsString(
            'nos dois dias de capacitação', 
            $newcourse->customfields->{custom_course_fields::EVALUATION_CRITERIA}->export_value()
        );

        $this->assertStringContainsString(
            'Gerente adjunto', 
            $newcourse->customfields->{custom_course_fields::TARGET_AUDIENCE}->export_value()
        );

        $this->assertStringContainsString(
            'Empreendedorismo',
            $newcourse->customfields->{custom_course_fields::AREA}->export_value()
        );
    }


    /**
     * @group !current
     */
    function test_course_update(){
        global $DB;

        $coursecat = $this->getDataGenerator()->create_category();

        $original_course = $this->getDataGenerator()->create_course([
            'category' => $coursecat->id,
        ]);
        
        $data = (object)$this->data;
        $data->instanceid = $original_course->id;

        $curso = new curso(0, $data);
        $curso->save();

        $importer = new curso_importer();
        $importer->set_category($coursecat->id);
        $course = $importer->upsert_course($curso);

        $newcourse = get_course($course->id);
        \tool_lfxp\helpers\custom_fields\course\custom_course_field::load_custom_fields($newcourse);

        $this->assertEquals(
            12*HOURSECS,
            $newcourse->customfields->{custom_course_fields::WORKLOAD}->get_value()
        );

        $this->assertEquals(
            'LEGADO-' . $this->data['source_idcurso'], 
            $newcourse->idnumber
        );

        $this->assertStringContainsString(
            $this->data['source_nome'], 
            $newcourse->fullname
        );

        $this->assertTrue((bool)$newcourse->enablecompletion);

        $this->assertEquals(
            'Curso presencial',
            $newcourse->customfields->{custom_course_fields::SOLUTION_FORMAT}->export_value()
        );

        $this->assertEquals(
            custom_course_fields::COMPLEXITY_INTERMEDIATE,
            $newcourse->customfields->{custom_course_fields::COMPLEXITY_LEVEL}->export_value()
        );

        $this->assertStringContainsString(
            'Atenção', 
            $newcourse->summary
        );

        $this->assertStringContainsString(
            'Primeiro encontro', 
            $newcourse->customfields->{custom_course_fields::INTRODUCTION}->export_value()
        );

        $this->assertStringContainsString(
            'Compreender a governança', 
            $newcourse->customfields->{custom_course_fields::OBJECTIVES}->export_value()
        );

        $this->assertStringContainsString(
            'Visão geral', 
            $newcourse->customfields->{custom_course_fields::CURRICULUM}->export_value()
        );

        $this->assertStringContainsString(
            'me inscrever nesta', 
            $newcourse->customfields->{custom_course_fields::CONSENT_TERM}->export_value()
        );

        $this->assertStringContainsString(
            'Alexandre de Oliveira Ambrosini', 
            $newcourse->customfields->{custom_course_fields::TECHNICAL_SHEET}->export_value()
        );

        $this->assertStringContainsString(
            'HABILITAÇÃO', 
            $newcourse->customfields->{custom_course_fields::REQUIREMENTS}->export_value()
        );

        $this->assertStringContainsString(
            'nos dois dias de capacitação', 
            $newcourse->customfields->{custom_course_fields::EVALUATION_CRITERIA}->export_value()
        );

        $this->assertStringContainsString(
            'Gerente adjunto', 
            $newcourse->customfields->{custom_course_fields::TARGET_AUDIENCE}->export_value()
        );

        $this->assertStringContainsString(
            'Empreendedorismo',
            $newcourse->customfields->{custom_course_fields::AREA}->export_value()
        );
    }
}
