SELECT
    studentClass.id                                      AS id,
    studentClass.ActivityCollectionId                    AS codigo_da_trilha,
    studentClass.ActivityCollectionClassId               AS codigo_da_turma,
    u.CODALUNO                                           AS codaluno,
    studentClass.RegistrationDate                        AS data_de_matricula,
    studentClass.TrainingState_PercentConcluded          AS porcent_conclusao,
    studentClass.TrainingState_PercentGrade              AS porcent_aproveitamento,
    studentClass.Period_From                             AS prazo_de_acesso,
    studentClass.EnrollmentOrigin                        AS origem_da_matricula,
    studentClass.Period_From                             AS data_de_inicio,
    studentClass.Period_To                               AS data_de_fim,
    studentClass.state                                   AS status_da_matricula,
    CASE
        WHEN (studentClass.State IN (2,5,9)) THEN dbo.GetStudentProgressStatus(
            ISNULL(TOTALPONTUAVEISTRILHA, 0),
            IIF(ISNULL(studentClass.Period_To, class.Period_To) >= GETDATE(), 'S', 'N'),
            CAST(ISNULL(studentClass.TrainingState_PercentConcluded, 0.00) AS DECIMAL),
            CAST(ISNULL(trail.PercentFrequecyForCertification, 0.00) AS DECIMAL),
            CAST(ISNULL(studentClass.TrainingState_PercentGrade, 0.00) AS DECIMAL),
            CAST(ISNULL(trail.PercentGradeForCertification, 0.00) AS DECIMAL),
            IIF(studentClass.TrainingState_ConcludedOn IS NULL, 'N', 'S')
        )
        WHEN studentClass.State = 0 THEN 'Cancelado'
        WHEN studentClass.State = 1 THEN 'Desistente'
        WHEN studentClass.State = 3 THEN 'Trancado'
    END                                                  AS status_de_progresso,
    NULL                                                 AS data_de_cancelamento,
    NULL                                                 AS motivo_do_cancelamento,
    NULL                                                 AS nota_do_usuario,
    studentClass.TrainingState_FirstAccess               AS data_do_primeiro_acesso,
    studentClass.TrainingState_AccessedOn                AS data_do_ultimo_acesso,
    studentClass.TrainingState_ConcludedOn               AS data_de_conclusao,
    studentClass.ModificationDate                        AS data_de_modificacao
FROM [dbo.LMS].TRN_StudentAndActivityCollectionClasses (NOLOCK) AS studentClass
INNER JOIN [dbo.lms].trn_activities_trail (NOLOCK)        AS trail ON (trail.id = studentClass.ActivityCollectionId)
INNER JOIN [dbo.LMS].TRN_Activities (NOLOCK)              AS act   ON (act.id = trail.id)
INNER JOIN [dbo.LMS].TRN_ActivityCollectionClasses (NOLOCK) AS class  ON (class.ActivityCollectionId = studentClass.ActivityCollectionId AND class.id = studentClass.ActivityCollectionClassId)
INNER JOIN TURMA (NOLOCK)                                 AS t     ON (t.ActivityCollectionClassId = class.Id AND t.ActivityCollectionId = trail.id)
INNER JOIN USUARIOS (NOLOCK)                              AS u     ON (u.tran_userid = studentClass.StudentId)
LEFT JOIN (
    SELECT
        ACC.Id,
        COUNT(ACI.ID) AS TOTALPONTUAVEISTRILHA
    FROM [dbo.LMS].TRN_activitycollectionitems (NOLOCK) AS ACI
    INNER JOIN [dbo.LMS].TRN_activitycollectionclasses (NOLOCK) AS ACC ON (ACI.activitycollectionid = ACC.activitycollectionid AND ACI.ActivityCollectionClassId = ACC.Id)
    WHERE ACI.ComposesScore = 1
    GROUP BY ACC.Id
) AS PONTUAVEIS ON (PONTUAVEIS.ID = class.Id)
WHERE u.WebAula = 0 AND class.IsDefaultClass = 0;
