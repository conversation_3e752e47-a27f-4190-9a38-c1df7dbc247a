{{!
	This file is part of Moodle - http://moodle.org/

	Mo<PERSON>le is free software: you can redistribute it and/or modify
	it under the terms of the GNU General Public License as published by
	the Free Software Foundation, either version 3 of the License, or
	(at your option) any later version.

	Moodle is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU General Public License for more details.

	You should have received a copy of the GNU General Public License
	along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
	@template theme_smart/drawers

	Boost drawer template.

	Context variables required for this template:
	* sitename - The name of the site
	* output - The core renderer for the page
	* bodyattributes - attributes for the body tag as a string of html attributes
	* sidepreblocks - HTML for the blocks
	* hasblocks - true if there are blocks on this page
	* courseindexopen - true if the nav drawer should be open on page load
	* regionmainsettingsmenu - HTML for the region main settings menu
	* hasregionmainsettingsmenu - There is a region main settings menu on this page.

	Example context (json):
	{
			"sitename": "<PERSON>od<PERSON>",
			"output": {
					"doctype": "<!DOCTYPE html>",
					"page_title": "Test page",
					"favicon": "favicon.ico",
					"main_content": "<h1>Headings make html validators happier</h1>"
			 },
			"bodyattributes":"",
			"sidepreblocks": "<h2>Blocks html goes here</h2>",
			"hasblocks":true,
			"courseindexopen": true,
			"navdraweropen": false,
			"blockdraweropen": true,
			"regionmainsettingsmenu": "",
			"hasregionmainsettingsmenu": false,
			"addblockbutton": ""
	}
}}
{{> theme_smart/head }}

<body {{{ bodyattributes }}}>
{{> core/local/toast/wrapper}}
<div id="page-wrapper" class="d-print-block">

	{{{ output.standard_top_of_body_html }}}

{{> format_trails/theme/header}}

	{{#courseindex}}
			{{< theme_smart/drawer }}
					{{$id}}theme_smart-drawers-courseindex{{/id}}
					{{$drawerclasses}}drawer drawer-left border-right shadow {{#courseindexopen}}show{{/courseindexopen}}{{/drawerclasses}}
					{{$drawercontent}}
							{{{courseindex}}}
					{{/drawercontent}}
					{{$drawerpreferencename}}drawer-open-index{{/drawerpreferencename}}
					{{$drawerstate}}show-drawer-left{{/drawerstate}}
					{{$tooltipplacement}}right{{/tooltipplacement}}
					{{$closebuttontext}}{{#str}}closecourseindex, core{{/str}}{{/closebuttontext}}
		{{$drawertitle}} {{drawertitle}} {{/drawertitle}}
			{{/ theme_smart/drawer}}
	{{/courseindex}}

	{{#hasblocks}}
			{{< theme_smart/drawer }}
					{{$id}}theme_smart-drawers-blocks{{/id}}
					{{$drawerclasses}}drawer drawer-right border-left shadow{{#blockdraweropen}} show{{/blockdraweropen}}{{/drawerclasses}}
					{{$drawercontent}}
							<section class="d-print-none px-3" aria-label="{{#str}}blocks{{/str}}">
									{{{ addblockbutton }}}
									{{{ sidepreblocks }}}
							</section>
					{{/drawercontent}}
					{{$drawerpreferencename}}drawer-open-block{{/drawerpreferencename}}
					{{$forceopen}}{{#forceblockdraweropen}}1{{/forceblockdraweropen}}{{/forceopen}}
					{{$drawerstate}}show-drawer-right{{/drawerstate}}
					{{$tooltipplacement}}left{{/tooltipplacement}}
					{{$drawercloseonresize}}1{{/drawercloseonresize}}
					{{$closebuttontext}}{{#str}}closeblockdrawer, core{{/str}}{{/closebuttontext}}
			{{/ theme_smart/drawer}}
	{{/hasblocks}}

	{{^hasblocks}}
	{{#output.smart_assistant_enabled}}
		<div 
			 class="drawer drawer-right border-left is-assistant shadow {{#blockdraweropen}}show{{/blockdraweropen}} d-print-none not-initialized"
			 data-region="fixed-drawer"
			 id="theme_smart-drawers-blocks"
			 data-preference="drawer-open-block"
			 data-state="show-drawer-right"
			 data-forceopen="{{#forceblockdraweropen}}1{{/forceblockdraweropen}}"
			 data-close-on-resize="1"
		>
			<button
				class="btn btn-light rounded-circle shadow drawertoggle togglejudy icon-no-margin hidden position-absolute"
				style="right:0.5rem; top:1rem; z-index:10; padding:0.5rem" 
				data-toggler="drawers"
				data-action="closedrawer"
				data-target="theme_smart-drawers-blocks"
				data-toggle="tooltip"
				data-placement="{{$tooltipplacement}}left{{/tooltipplacement}}"
				title="{{$closebuttontext}}{{#str}}closedrawer, core{{/str}}{{/closebuttontext}}"
			>
				{{#pix}} e/cancel, core {{/pix}}
			</button>
			<div class="drawercontent drag-container assistant-container p-0 vh-100" data-usertour="scroller">
				<section class="d-print-none"></section>
			</div>
		</div>
	{{/output.smart_assistant_enabled}}
	{{/hasblocks}}

<div class="site-wrapper">
	<div id="page" data-region="mainpage" data-usertour="scroller" class="drawers {{#courseindexopen}}show-drawer-left{{/courseindexopen}} {{#blockdraweropen}}show-drawer-right{{/blockdraweropen}} drag-container px-0">
		{{#output.course_headeractions}}
		<div class="secondary-navigation d-flex justify-content-end align-items-center d-print-none mt-4">
			<div class="d-flex align-items-center">
				<div class="header-actions-container" data-region="header-actions-container">
					{{#headeractions}}
						<div class="header-action">{{{.}}}</div>
					{{/headeractions}}
				</div>
			</div>
		</div>
		{{/output.course_headeractions}}
		
		<div id="topofscroll" class="main-inner mt-0">
			<div class="drawer-toggles d-flex">
				{{#courseindex}}
					<div class="drawer-toggler drawer-left-toggle open-nav d-print-none">
						<button
							class="btn btn-light icon-no-margin py-1 pr-2"
							data-toggler="drawers"
							data-action="toggle"
							data-target="theme_smart-drawers-courseindex"
							data-toggle="tooltip"
							data-placement="right"
							title="{{#str}}opendrawerindex, core{{/str}}"
						>
							<span class="sr-only">{{#str}}opendrawerindex, core{{/str}}</span>
							{{#pix}} t/index_drawer, moodle {{/pix}}
							<span class="d-block d-md-none label">{{drawertitle}}s</span>
						</button>
					</div>
				{{/courseindex}}
				
				
				{{#hasrightdrawer}}
					<div class="drawer-toggler drawer-right-toggle ml-auto d-print-none">
						<button
							class="btn btn-light icon-no-margin togglejudy py-1 pl-2"
							data-toggler="drawers"
							data-action="toggle"
							data-target="theme_smart-drawers-blocks"
						>
						{{^hasblocks}}
							{{#output.smart_assistant_enabled}}
								<i class="icon icon-assistantgpt"></i>
								<span class="d-block d-md-none label">Ver blocos</span>
							{{/output.smart_assistant_enabled}}
						{{/hasblocks}}
						
						{{#hasblocks}}
							<i class="fa-solid fa-shapes d-inline d-md-none"></i>
							<i class="fa fa-chevron-left py-1 d-none d-md-inline"></i>
							<span class="d-block d-md-none label">{{#str}}opendrawerblocks, core{{/str}}</span>
						{{/hasblocks}}
						</button>
					</div>
				{{/hasrightdrawer}}
			</div>
			
			<div id="page-content" class="pb-3 d-print-block">
				<div id="region-main-box">
					{{#hasregionmainsettingsmenu}}
					<div id="region-main-settings-menu" class="d-print-none">
						<div> {{{ regionmainsettingsmenu }}} </div>
					</div>
					{{/hasregionmainsettingsmenu}}
					<section id="region-main" aria-label="{{#str}}content{{/str}}">

						{{#hasregionmainsettingsmenu}}
							<div class="region_main_settings_menu_proxy"></div>
						{{/hasregionmainsettingsmenu}}
						{{{ output.course_content_header }}}
						{{#overflow}}
							<div class="container-fluid tertiary-navigation">
								<div class="navitem">
									{{> core/url_select}}
								</div>
							</div>
						{{/overflow}}
						
						<div class="mr-auto">
						{{#headercontent}}
							{{> format_trails/theme/activity_header }}
						{{/headercontent}}
						</div>
			
						{{{ output.main_content }}}
						{{{ output.activity_navigation }}}
						{{{ output.course_content_footer }}}

					</section>
				</div>
			</div>
		</div>
		<div class="d-none">{{> theme_smart/footer }}</div>
	</div>
	{{{ output.standard_after_main_region_html }}}
</div>
</div>

<script defer>
	// Acessibility (NVDA) to course cards INCOURSE
	setTimeout(function () {
			function executeScript() {
					// Manipulação do tabindex e navegação por TAB
					(function () {
							// Seleciona o drawer pelo ID
							const drawer = document.getElementById("theme_smart-drawers-courseindex");
	
							if (drawer) {
									// Função para verificar se um elemento <a> é clicável
									function isClickableLink(element) {
											return element.tagName.toLowerCase() === "a" && element.hasAttribute("href") && element.getAttribute("href") !== "#";
									}
	
									// Função para atualizar o tabindex com base no estado do drawer
									function updateTabIndex() {
											const isOpen = drawer.classList.contains("show");
	
											// Seleciona os elementos que devem ser tabuláveis
											const courseItems = drawer.querySelectorAll(".courseindex-item.courseindex-item-cm");
											const chevronItems = drawer.querySelectorAll(".courseindex-chevron.icons-collapse-expand");
	
											if (isOpen) {
													// Para cada item do curso
													courseItems.forEach(function (item) {
															// Define tabindex="0" para o item pai
															item.setAttribute("tabindex", "0");
	
															// Remove tabindex dos filhos que são interativos, exceto links clicáveis
															const focusableChildren = item.querySelectorAll("a, button, [tabindex]");
															focusableChildren.forEach(function (child) {
																	if (child !== item) {
																			if (isClickableLink(child)) {
																					// Se for um link clicável, mantém o tabindex
																					child.setAttribute("tabindex", "0");
																			} else {
																					// Remove o tabindex de elementos não clicáveis
																					child.removeAttribute("tabindex");
																			}
																	}
															});
													});
	
													// Para cada item de chevron
													chevronItems.forEach(function (item) {
															// Define tabindex="0" para o elemento chevron
															item.setAttribute("tabindex", "0");
	
															// Remove tabindex dos filhos que são interativos, exceto links clicáveis
															const focusableChildren = item.querySelectorAll("a, button, [tabindex]");
															focusableChildren.forEach(function (child) {
																	if (child !== item) {
																			if (isClickableLink(child)) {
																					// Se for um link clicável, mantém o tabindex
																					child.setAttribute("tabindex", "0");
																			} else {
																					// Remove o tabindex de elementos não clicáveis
																					child.removeAttribute("tabindex");
																			}
																	}
															});
													});
											} else {
													// Se o drawer estiver fechado, define tabindex="-1" para todos os elementos interativos dentro dele
													const allFocusableElements = drawer.querySelectorAll("a, button, [tabindex]");
													allFocusableElements.forEach(function (element) {
															element.setAttribute("tabindex", "-1");
													});
											}
									}
	
									// Adicionar contador e limite de tentativas para o primeiro MutationObserver
									let mutationAttemptsDrawer = 0;
									const maxAttemptsDrawer = 10;
	
									// Observa mudanças na classe do drawer para detectar abertura/fechamento
									const observerDrawer = new MutationObserver(function (mutations) {
											mutations.forEach(function (mutation) {
													if (mutation.attributeName === "class") {
															updateTabIndex();
															mutationAttemptsDrawer++;
															if (mutationAttemptsDrawer >= maxAttemptsDrawer) {
																	observerDrawer.disconnect();
																	console.warn("Limite de tentativas da MutationObserver do drawer alcançado e desconectado.");
															}
													}
											});
									});
	
									// Inicia a observação do elemento drawer
									observerDrawer.observe(drawer, { attributes: true });
	
									// Chamada inicial para configurar o tabindex corretamente
									updateTabIndex();
							}
					})();
	
					// Manipulação do menu lateral com validações
					(function () {
							// Seleciona o botão que abre o menu
							const openButton = document.querySelector("#topofscroll .btn[data-toggler='drawers']");
	
							// Verifica se o botão existe antes de prosseguir
							if (!openButton) {
									console.warn("Botão de abertura do menu não encontrado.");
									return;
							}
	
							// Cria dinamicamente a região de alerta para o NVDA
							const liveRegion = document.createElement("div");
							liveRegion.setAttribute("aria-live", "polite");
							liveRegion.classList.add("sr-only");
							document.body.appendChild(liveRegion);
	
							// Adiciona os atributos necessários ao botão
							openButton.setAttribute("aria-expanded", "false");
	
							// Função para adicionar e remover texto da live region
							function updateLiveRegionMessage(message) {
									if (liveRegion) { // Verifica se liveRegion foi criado
											// Adiciona o texto à live region
											liveRegion.textContent = message;
	
											// Remove o texto após um curto intervalo
											setTimeout(() => {
													liveRegion.textContent = "";
											}, 1000); // 1 segundo
									} else {
											console.warn("liveRegion não está disponível.");
									}
							}
	
							// Adiciona o evento de clique ao botão de abertura
							openButton.addEventListener("click", () => {
									// Atualiza o atributo aria-expanded para true ou false
									const isExpanded = openButton.getAttribute("aria-expanded") === "true";
									openButton.setAttribute("aria-expanded", !isExpanded);
	
									if (!isExpanded) {
											// Atualiza a live region com a mensagem de abertura
											updateLiveRegionMessage("Menu lateral aberto");
									} else {
											// Atualiza a live region com a mensagem de fechamento
											updateLiveRegionMessage("Menu lateral fechado");
									}
							});
					})();
			}
	
			// Função para verificar se os elementos necessários já estão presentes no DOM
			function waitForElements(selector, callback) {
					const elements = document.querySelectorAll(selector);
					if (elements.length) {
							callback();
					} else {
							// Adicionar contador e limite de tentativas para o MutationObserver dentro de waitForElements
							let mutationAttemptsWait = 0;
							const maxAttemptsWait = 10; // Defina o número máximo de tentativas
	
							const observerWait = new MutationObserver((mutations, obs) => {
									const elems = document.querySelectorAll(selector);
									if (elems.length) {
											callback();
											obs.disconnect();
									} else {
											mutationAttemptsWait++;
											if (mutationAttemptsWait >= maxAttemptsWait) {
													obs.disconnect();
													console.warn(`Limite de tentativas da MutationObserver em waitForElements alcançado para o seletor "${selector}" e desconectado.`);
											}
									}
							});
							observerWait.observe(document.body, {
									childList: true,
									subtree: true
							});
	
							const timeout = setTimeout(() => {
							     observerWait.disconnect();
							     console.warn(`Timeout atingido. MutationObserver em waitForElements desconectado para o seletor "${selector}".`);
							 }, 15000); 
					}
			}
	
			// Chama a função principal quando os elementos estiverem disponíveis
			waitForElements("#theme_smart-drawers-courseindex, #topofscroll .btn[data-toggler='drawers']", executeScript);
	}, 5000);
	</script>

</body>
</html>
{{#js}}
M.util.js_pending('theme_smart/loader');
require(['theme_smart/loader', 'theme_smart/drawer', 'theme_smart/drawers', 'jquery'], function(Loader, Drawer, D, $) {
	Drawer.init();
	M.util.js_complete('theme_smart/loader');

//if($("body").is("#page-mod-page-view") || $("body").is("#page-mod-supervideo-view")){

if($("body").is("#page-mod-page-view")){
	let delaying = $("body").is("#page-mod-supervideo-view") ? 1500 : 0;
	var iframeWidth = 0;
	var iframeHeight = 0;
	
	setTimeout(function(){
		iframeWidth = $("#topofscroll iframe").width();
		iframeHeight = $("#topofscroll iframe").height();
	
		mod_page_iframe_resize();
		$(window).on("resize", mod_page_iframe_resize);
	}, delaying);
}	

function mod_page_iframe_resize(){
	$("iframe").each(function(){
		$(this).closest(".generalbox").addClass("has-iframe");
		
		let headerHeight = $("header").outerHeight();
		let navHeight = $(".secondary-navigation").outerHeight() || 0;
		let modifiedHeight = $(".modified").length ? $(".modified").outerHeight() : 0;
		let containerWidth = $("#topofscroll").width();
		let containerHeight = $(window).height() - headerHeight - navHeight - modifiedHeight - 112;
		
		let newiframeWidth = (iframeWidth * containerHeight) / iframeHeight;
		let newiframeHeight = containerHeight;

		if(newiframeWidth > containerWidth){
			newiframeWidth = containerWidth;
			newiframeHeight = (newiframeWidth * iframeHeight) / iframeWidth;
		}
		
					//console.log({
					//    "width": newiframeWidth+"px",
					//    "height": newiframeHeight+"px",
					//    "headerHeight": headerHeight+"px",
					//    "navHeight": navHeight+"px",
					//    "modifiedHeight": modifiedHeight+"px",
					//    "containerWidth": containerWidth+"px",
					//    "containerHeight": containerHeight+"px",
					//});
		
		$(this).css({
			"width": newiframeWidth+"px",
			"height": newiframeHeight+"px"
		});
	});
}
	
$(".generalbox").each(function(){
	let content = $(this).text().replace(/\s/g, "");
	
	if(content){
		$(this).prepend("<h5 class='mb-0'>{{#str}}resume, format_trails{{/str}}</h5>");
	}
});

$(".path-mod-survey table.surveytable").each(function(){
	$(this).addClass("table table-striped table-hover").removeAttr("width");
	
	if(!$(this).parent().is(".table-responsive")){
		$(this).wrap("<div class='table-responsive'/>");
	}
});

$(".generalbox br").each(function(){
	$("<hr>").insertBefore($(this));
	$(this).remove();
});

});
{{/js}}