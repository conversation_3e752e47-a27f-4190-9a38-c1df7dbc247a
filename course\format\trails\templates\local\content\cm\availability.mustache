{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/content/cm/availability

    Displays a activity availability.

    Example context (json):
    {
        "info": [
            {
                "classes": "",
                "text": "Not available unless: <ul><li>It is on or after <strong>8 June 2012</strong></li></ul>",
                "ishidden": 0,
                "isstealth": 0,
                "isrestricted": 1,
                "isfullinfo": 1
            }
        ],
        "hasmodavailability": true
    }
}}
{{#hasmodavailability}}
    {{#info}}
		<div class="modal-availability-info position-fixed d-none align-items-center justify-content-center w-100 h-100" style="top:0; left: 0; background-color:rgba(0,0,0,0.7); z-index:10000">
			<div class="bg-dark rounded-sm px-0 col-10 col-lg-6 col-xl-4">
				<div class="alert alert-danger border border-danger rounded-sm m-0 py-2 pr-0 d-flex align-items-center justify-content-between">
					<div>
						<p class="mb-0 pt-1 text-white h5">{{activityname}}</p>
						<small>{{#str}}activity_unavailable, format_trails{{/str}}</small>
					</div>
					<button class="btn btn-link btn-hide-availability-info text-danger" style="filter:contrast(0.3) brightness(140%)"><i class="fa fa-xmark"></i></button>
				</div>
				<div class="availabilityinfo small course-description-item {{classes}} border p-3 alert alert-danger m-0 rounded-0 bg-transparent">
				{{^isrestricted}}
					<span class="badge rounded-sm badge-warning custom-link">{{{text}}}</span>
				{{/isrestricted}}
				{{#isrestricted}}
					<div class="description-inner custom-link">
					{{#pix}}t/unlock, core{{/pix}} {{{text}}}
					</div>
				{{/isrestricted}}
				{{#isadmin}}
				<p class="mb-0 mt-3 text-center"><a href="{{url}}" style="filter:contrast(0.5) brightness(140%)">{{#str}}access_activity, format_trails{{/str}}</a></p>
				{{/isadmin}}
				</div>
			</div>

		</div>
    {{/info}}
{{/hasmodavailability}}
