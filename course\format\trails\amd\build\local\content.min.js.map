{"version": 3, "file": "content.min.js", "sources": ["../../src/local/content.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Course index main component.\n *\n * @module     core_courseformat/local/content\n * @class      core_courseformat/local/content\n * @copyright  2020 Ferran Recio <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport { BaseComponent } from \"core/reactive\";\nimport { debounce } from \"core/utils\";\nimport { getCurrentCourseEditor } from \"core_courseformat/courseeditor\";\nimport Config from \"core/config\";\nimport inplaceeditable from \"core/inplace_editable\";\nimport Section from \"core_courseformat/local/content/section\";\nimport CmItem from \"core_courseformat/local/content/section/cmitem\";\nimport Fragment from \"core/fragment\";\nimport Templates from \"core/templates\";\nimport DispatchActions from \"format_trails/local/content/actions\";\nimport * as CourseEvents from \"core_course/events\";\n// The jQuery module is only used for interacting with Boostrap 4. It can we removed when MDL-71979 is integrated.\nimport jQuery from \"jquery\";\nimport Pending from \"core/pending\";\n\nexport default class Component extends BaseComponent {\n  /**\n   * Constructor hook.\n   *\n   * @param {Object} descriptor the component descriptor\n   */\n  create(descriptor) {\n    // Optional component name for debugging.\n    this.name = \"course_format\";\n    // Default query selectors.\n    this.selectors = {\n      SECTION: `[data-for='section']`,\n      SECTION_ITEM: `[data-for='section_title']`,\n      SECTION_CMLIST: `[data-for='cmlist']`,\n      COURSE_SECTIONLIST: `[data-for='course_sectionlist']`,\n      CM: `[data-for='cmitem']`,\n      PAGE: `#page`,\n      TOGGLER: `[data-action=\"togglecoursecontentsection\"]`,\n      COLLAPSE: `[data-toggle=\"collapse\"]`,\n      TOGGLEALL: `[data-toggle=\"toggleall\"]`,\n      // Formats can override the activity tag but a default one is needed to create new elements.\n      ACTIVITYTAG: \"li\",\n      SECTIONTAG: \"li\",\n    };\n    // Default classes to toggle on refresh.\n    this.classes = {\n      COLLAPSED: `collapsed`,\n      // Course content classes.\n      ACTIVITY: `activity`,\n      STATEDREADY: `stateready`,\n      SECTION: `section`,\n    };\n    // Array to save dettached elements during element resorting.\n    this.dettachedCms = {};\n    this.dettachedSections = {};\n    // Index of sections and cms components.\n    this.sections = {};\n    this.cms = {};\n    // The page section return.\n    this.sectionReturn = descriptor.sectionReturn ?? 0;\n    this.debouncedReloads = new Map();\n    this.sectionname = descriptor.sectionname;\n    this.pluralsectionname = descriptor.pluralsectionname;\n  }\n\n  /**\n   * Static method to create a component instance form the mustahce template.\n   *\n   * @param {string} target the DOM main element or its ID\n   * @param {object} selectors optional css selector overrides\n   * @param {number} sectionReturn the content section return\n   * @param {string} sectionname the section name\n   * @param {string} pluralsectionname the plural section name\n   * @return {Component}\n   */\n  static init(\n    target,\n    selectors,\n    sectionReturn,\n    sectionname,\n    pluralsectionname\n  ) {\n    return new Component({\n      element: document.getElementById(target),\n      reactive: getCurrentCourseEditor(),\n      selectors,\n      sectionReturn,\n      sectionname,\n      pluralsectionname,\n    });\n  }\n\n  /**\n   * Initial state ready method.\n   *\n   * @param {Object} state the state data\n   */\n  stateReady(state) {\n    this._indexContents();\n    // Activate section togglers.\n    this.addEventListener(this.element, \"click\", this._sectionTogglers);\n\n    // Collapse/Expand all sections button.\n    const toogleAll = this.getElement(this.selectors.TOGGLEALL);\n    if (toogleAll) {\n      // Ensure collapse menu button adds aria-controls attribute referring to each collapsible element.\n      const collapseElements = this.getElements(this.selectors.COLLAPSE);\n      const collapseElementIds = [...collapseElements].map(\n        (element) => element.id\n      );\n      toogleAll.setAttribute(\"aria-controls\", collapseElementIds.join(\" \"));\n\n      this.addEventListener(toogleAll, \"click\", this._allSectionToggler);\n      this.addEventListener(toogleAll, \"keydown\", (e) => {\n        // Collapse/expand all sections when Space key is pressed on the toggle button.\n        if (e.key === \" \") {\n          this._allSectionToggler(e);\n        }\n      });\n      this._refreshAllSectionsToggler(state);\n    }\n\n    if (this.reactive.supportComponents) {\n      // Actions are only available in edit mode.\n      if (this.reactive.isEditing) {\n        new DispatchActions(this, \"leticia\");\n      }\n\n      // Mark content as state ready.\n      this.element.classList.add(this.classes.STATEDREADY);\n    }\n\n    // Capture completion events.\n    this.addEventListener(\n      this.element,\n      CourseEvents.manualCompletionToggled,\n      this._completionHandler\n    );\n\n    // Capture page scroll to update page item.\n    this.addEventListener(\n      document.querySelector(this.selectors.PAGE),\n      \"scroll\",\n      this._scrollHandler\n    );\n  }\n\n  /**\n   * Setup sections toggler.\n   *\n   * Toggler click is delegated to the main course content element because new sections can\n   * appear at any moment and this way we prevent accidental double bindings.\n   *\n   * @param {Event} event the triggered event\n   */\n  _sectionTogglers(event) {\n    const sectionlink = event.target.closest(this.selectors.TOGGLER);\n    const closestCollapse = event.target.closest(this.selectors.COLLAPSE);\n    // Assume that chevron is the only collapse toggler in a section heading;\n    // I think this is the most efficient way to verify at the moment.\n    const isChevron = closestCollapse?.closest(this.selectors.SECTION_ITEM);\n\n    if (sectionlink || isChevron) {\n      const section = event.target.closest(this.selectors.SECTION);\n      const toggler = section.querySelector(this.selectors.COLLAPSE);\n      const isCollapsed =\n        toggler?.classList.contains(this.classes.COLLAPSED) ?? false;\n\n      if (isChevron || isCollapsed) {\n        // Update the state.\n        const sectionId = section.getAttribute(\"data-id\");\n        this.reactive.dispatch(\n          \"sectionContentCollapsed\",\n          [sectionId],\n          !isCollapsed\n        );\n      }\n    }\n  }\n\n  /**\n   * Handle the collapse/expand all sections button.\n   *\n   * Toggler click is delegated to the main course content element because new sections can\n   * appear at any moment and this way we prevent accidental double bindings.\n   *\n   * @param {Event} event the triggered event\n   */\n  _allSectionToggler(event) {\n    event.preventDefault();\n\n    const target = event.target.closest(this.selectors.TOGGLEALL);\n    const isAllCollapsed = target.classList.contains(this.classes.COLLAPSED);\n\n    const course = this.reactive.get(\"course\");\n    this.reactive.dispatch(\n      \"sectionContentCollapsed\",\n      course.sectionlist ?? [],\n      !isAllCollapsed\n    );\n  }\n\n  /**\n   * Return the component watchers.\n   *\n   * @returns {Array} of watchers\n   */\n  getWatchers() {\n    // Section return is a global page variable but most formats define it just before start printing\n    // the course content. This is the reason why we define this page setting here.\n    this.reactive.sectionReturn = this.sectionReturn;\n\n    // Check if the course format is compatible with reactive components.\n    if (!this.reactive.supportComponents) {\n      return [];\n    }\n    return [\n      // State changes that require to reload some course modules.\n      { watch: `cm.visible:updated`, handler: this._reloadCm },\n      { watch: `cm.stealth:updated`, handler: this._reloadCm },\n      { watch: `cm.sectionid:updated`, handler: this._reloadCm },\n      { watch: `cm.indent:updated`, handler: this._reloadCm },\n      // Update section number and title.\n      { watch: `section.number:updated`, handler: this._refreshSectionNumber },\n      // Collapse and expand sections.\n      {\n        watch: `section.contentcollapsed:updated`,\n        handler: this._refreshSectionCollapsed,\n      },\n      // Sections and cm sorting.\n      { watch: `transaction:start`, handler: this._startProcessing },\n      {\n        watch: `course.sectionlist:updated`,\n        handler: this._refreshCourseSectionlist,\n      },\n      { watch: `section.cmlist:updated`, handler: this._refreshSectionCmlist },\n      // Reindex sections and cms.\n      { watch: `state:updated`, handler: this._indexContents },\n    ];\n  }\n\n  /**\n   * Update section collapsed state via bootstrap 4 if necessary.\n   *\n   * Formats that do not use bootstrap 4 must override this method in order to keep the section\n   * toggling working.\n   *\n   * @param {object} args\n   * @param {Object} args.state The state data\n   * @param {Object} args.element The element to update\n   */\n  _refreshSectionCollapsed({ state, element }) {\n    const target = this.getElement(this.selectors.SECTION, element.id);\n    if (!target) {\n      throw new Error(`Unknown section with ID ${element.id}`);\n    }\n    // Check if it is already done.\n    const toggler = target.querySelector(this.selectors.COLLAPSE);\n    const isCollapsed =\n      toggler?.classList.contains(this.classes.COLLAPSED) ?? false;\n\n    if (element.contentcollapsed !== isCollapsed) {\n      let collapsibleId =\n        toggler.dataset.target ?? toggler.getAttribute(\"href\");\n      if (!collapsibleId) {\n        return;\n      }\n      collapsibleId = collapsibleId.replace(\"#\", \"\");\n      const collapsible = document.getElementById(collapsibleId);\n      if (!collapsible) {\n        return;\n      }\n\n      // Course index is based on Bootstrap 4 collapsibles. To collapse them we need jQuery to\n      // interact with collapsibles methods. Hopefully, this will change in Bootstrap 5 because\n      // it does not require jQuery anymore (when MDL-71979 is integrated).\n      jQuery(collapsible).collapse(element.contentcollapsed ? \"hide\" : \"show\");\n    }\n\n    this._refreshAllSectionsToggler(state);\n  }\n\n  /**\n   * Refresh the collapse/expand all sections element.\n   *\n   * @param {Object} state The state data\n   */\n  _refreshAllSectionsToggler(state) {\n    const target = this.getElement(this.selectors.TOGGLEALL);\n    if (!target) {\n      return;\n    }\n    // Check if we have all sections collapsed/expanded.\n    let allcollapsed = true;\n    let allexpanded = true;\n    state.section.forEach((section) => {\n      allcollapsed = allcollapsed && section.contentcollapsed;\n      allexpanded = allexpanded && !section.contentcollapsed;\n    });\n    if (allcollapsed) {\n      target.classList.add(this.classes.COLLAPSED);\n      target.setAttribute(\"aria-expanded\", false);\n    }\n    if (allexpanded) {\n      target.classList.remove(this.classes.COLLAPSED);\n      target.setAttribute(\"aria-expanded\", true);\n    }\n  }\n\n  /**\n   * Setup the component to start a transaction.\n   *\n   * Some of the course actions replaces the current DOM element with a new one before updating the\n   * course state. This means the component cannot preload any index properly until the transaction starts.\n   *\n   */\n  _startProcessing() {\n    // During a section or cm sorting, some elements could be dettached from the DOM and we\n    // need to store somewhare in case they are needed later.\n    this.dettachedCms = {};\n    this.dettachedSections = {};\n  }\n\n  /**\n   * Activity manual completion listener.\n   *\n   * @param {Event} event the custom ecent\n   */\n  _completionHandler({ detail }) {\n    if (detail === undefined) {\n      return;\n    }\n    this.reactive.dispatch(\"cmCompletion\", [detail.cmid], detail.completed);\n  }\n\n  /**\n   * Check the current page scroll and update the active element if necessary.\n   */\n  _scrollHandler() {\n    const pageOffset = document.querySelector(this.selectors.PAGE).scrollTop;\n    const items = this.reactive\n      .getExporter()\n      .allItemsArray(this.reactive.state);\n    // Check what is the active element now.\n    let pageItem = null;\n    items.every((item) => {\n      const index = item.type === \"section\" ? this.sections : this.cms;\n      if (index[item.id] === undefined) {\n        return true;\n      }\n\n      const element = index[item.id].element;\n      // Activities without url can only be page items in edit mode.\n      if (item.type === \"cm\" && !item.url && !this.reactive.isEditing) {\n        return pageOffset >= element.offsetTop;\n      }\n      pageItem = item;\n      return pageOffset >= element.offsetTop;\n    });\n    if (pageItem) {\n      this.reactive.dispatch(\"setPageItem\", pageItem.type, pageItem.id);\n    }\n  }\n\n  /**\n   * Update a course section when the section number changes.\n   *\n   * The courseActions module used for most course section tools still depends on css classes and\n   * section numbers (not id). To prevent inconsistencies when a section is moved, we need to refresh\n   * the\n   *\n   * Course formats can override the section title rendering so the frontend depends heavily on backend\n   * rendering. Luckily in edit mode we can trigger a title update using the inplace_editable module.\n   *\n   * @param {Object} param\n   * @param {Object} param.element details the update details.\n   */\n  _refreshSectionNumber({ element }) {\n    // Find the element.\n    const target = this.getElement(this.selectors.SECTION, element.id);\n    if (!target) {\n      // Job done. Nothing to refresh.\n      return;\n    }\n    // Update section numbers in all data, css and YUI attributes.\n    target.id = `section-${element.number}`;\n    // YUI uses section number as section id in data-sectionid, in principle if a format use components\n    // don't need this sectionid attribute anymore, but we keep the compatibility in case some plugin\n    // use it for legacy purposes.\n    target.dataset.sectionid = element.number;\n    // The data-number is the attribute used by components to store the section number.\n    target.dataset.number = element.number;\n\n    // Update title and title inplace editable, if any.\n    const inplace = inplaceeditable.getInplaceEditable(\n      target.querySelector(this.selectors.SECTION_ITEM)\n    );\n    if (inplace) {\n      // The course content HTML can be modified at any moment, so the function need to do some checkings\n      // to make sure the inplace editable still represents the same itemid.\n      const currentvalue = inplace.getValue();\n      const currentitemid = inplace.getItemId();\n      // Unnamed sections must be recalculated.\n      if (inplace.getValue() === \"\") {\n        // The value to send can be an empty value if it is a default name.\n        if (\n          currentitemid == element.id &&\n          (currentvalue != element.rawtitle || element.rawtitle == \"\")\n        ) {\n          inplace.setValue(element.rawtitle);\n        }\n      }\n    }\n  }\n\n  /**\n   * Refresh a section cm list.\n   *\n   * @param {Object} param\n   * @param {Object} param.element details the update details.\n   */\n  _refreshSectionCmlist({ element }) {\n    const cmlist = element.cmlist ?? [];\n    const section = this.getElement(this.selectors.SECTION, element.id);\n    const listparent = section?.querySelector(this.selectors.SECTION_CMLIST);\n    // A method to create a fake element to be replaced when the item is ready.\n    const createCm = this._createCmItem.bind(this);\n    if (listparent) {\n      this._fixOrder(\n        listparent,\n        cmlist,\n        this.selectors.CM,\n        this.dettachedCms,\n        createCm\n      );\n    }\n  }\n\n  /**\n   * Refresh the section list.\n   *\n   * @param {Object} param\n   * @param {Object} param.element details the update details.\n   */\n  _refreshCourseSectionlist({ element }) {\n    // If we have a section return means we only show a single section so no need to fix order.\n    if (this.reactive.sectionReturn != 0) {\n      return;\n    }\n    const sectionlist = element.sectionlist ?? [];\n    const listparent = this.getElement(this.selectors.COURSE_SECTIONLIST);\n    // For now section cannot be created at a frontend level.\n    const createSection = this._createSectionItem.bind(this);\n    if (listparent) {\n      this._fixOrder(\n        listparent,\n        sectionlist,\n        this.selectors.SECTION,\n        this.dettachedSections,\n        createSection\n      );\n    }\n  }\n\n  /**\n   * Regenerate content indexes.\n   *\n   * This method is used when a legacy action refresh some content element.\n   */\n  _indexContents() {\n    // Find unindexed sections.\n    this._scanIndex(this.selectors.SECTION, this.sections, (item) => {\n      return new Section(item);\n    });\n\n    // Find unindexed cms.\n    this._scanIndex(this.selectors.CM, this.cms, (item) => {\n      return new CmItem(item);\n    });\n  }\n\n  /**\n   * Reindex a content (section or cm) of the course content.\n   *\n   * This method is used internally by _indexContents.\n   *\n   * @param {string} selector the DOM selector to scan\n   * @param {*} index the index attribute to update\n   * @param {*} creationhandler method to create a new indexed element\n   */\n  _scanIndex(selector, index, creationhandler) {\n    const items = this.getElements(`${selector}:not([data-indexed])`);\n    items.forEach((item) => {\n      if (!item?.dataset?.id) {\n        return;\n      }\n      // Delete previous item component.\n      if (index[item.dataset.id] !== undefined) {\n        index[item.dataset.id].unregister();\n      }\n      // Create the new component.\n      index[item.dataset.id] = creationhandler({\n        ...this,\n        element: item,\n      });\n      // Mark as indexed.\n      item.dataset.indexed = true;\n    });\n  }\n\n  /**\n   * Reload a course module contents.\n   *\n   * Most course module HTML is still strongly backend dependant.\n   * Some changes require to get a new version of the module.\n   *\n   * @param {object} param0 the watcher details\n   * @param {object} param0.element the state object\n   */\n  _reloadCm({ element }) {\n    if (!this.getElement(this.selectors.CM, element.id)) {\n      return;\n    }\n    const debouncedReload = this._getDebouncedReloadCm(element.id);\n    debouncedReload();\n  }\n\n  /**\n   * Generate or get a reload CM debounced function.\n   * @param {Number} cmId\n   * @returns {Function} the debounced reload function\n   */\n  _getDebouncedReloadCm(cmId) {\n    const pendingKey = `courseformat/content:reloadCm_${cmId}`;\n    let debouncedReload = this.debouncedReloads.get(pendingKey);\n    if (debouncedReload) {\n      return debouncedReload;\n    }\n    const pendingReload = new Pending(pendingKey);\n    const reload = () => {\n      this.debouncedReloads.delete(pendingKey);\n      const cmitem = this.getElement(this.selectors.CM, cmId);\n      if (!cmitem) {\n        return;\n      }\n      const promise = Fragment.loadFragment(\n        \"core_courseformat\",\n        \"cmitem\",\n        Config.courseContextId,\n        {\n          id: cmId,\n          courseid: Config.courseId,\n          sr: this.reactive.sectionReturn ?? 0,\n        }\n      );\n      promise\n        .then((html, js) => {\n          Templates.replaceNode(cmitem, html, js);\n          this._indexContents();\n          pendingReload.resolve();\n          return;\n        })\n        .catch();\n    };\n    debouncedReload = debounce(reload, 200);\n    this.debouncedReloads.set(pendingKey, debouncedReload);\n    return debouncedReload;\n  }\n\n  /**\n   * Reload a course section contents.\n   *\n   * Section HTML is still strongly backend dependant.\n   * Some changes require to get a new version of the section.\n   *\n   * @param {details} param0 the watcher details\n   * @param {object} param0.element the state object\n   */\n  _reloadSection({ element }) {\n    const pendingReload = new Pending(\n      `courseformat/content:reloadSection_${element.id}`\n    );\n    const sectionitem = this.getElement(this.selectors.SECTION, element.id);\n    if (sectionitem) {\n      const promise = Fragment.loadFragment(\n        \"core_courseformat\",\n        \"section\",\n        Config.courseContextId,\n        {\n          id: element.id,\n          courseid: Config.courseId,\n          sr: this.reactive.sectionReturn ?? 0,\n        }\n      );\n      promise\n        .then((html, js) => {\n          Templates.replaceNode(sectionitem, html, js);\n          this._indexContents();\n          pendingReload.resolve();\n          return;\n        })\n        .catch();\n    }\n  }\n\n  /**\n   * Create a new course module item in a section.\n   *\n   * Thos method will append a fake item in the container and trigger an ajax request to\n   * replace the fake element by the real content.\n   *\n   * @param {Element} container the container element (section)\n   * @param {Number} cmid the course-module ID\n   * @returns {Element} the created element\n   */\n  _createCmItem(container, cmid) {\n    const newItem = document.createElement(this.selectors.ACTIVITYTAG);\n    newItem.dataset.for = \"cmitem\";\n    newItem.dataset.id = cmid;\n    // The legacy actions.js requires a specific ID and class to refresh the CM.\n    newItem.id = `module-${cmid}`;\n    newItem.classList.add(this.classes.ACTIVITY);\n    container.append(newItem);\n    this._reloadCm({\n      element: this.reactive.get(\"cm\", cmid),\n    });\n    return newItem;\n  }\n\n  /**\n   * Create a new section item.\n   *\n   * This method will append a fake item in the container and trigger an ajax request to\n   * replace the fake element by the real content.\n   *\n   * @param {Element} container the container element (section)\n   * @param {Number} sectionid the course-module ID\n   * @returns {Element} the created element\n   */\n  _createSectionItem(container, sectionid) {\n    const section = this.reactive.get(\"section\", sectionid);\n    const newItem = document.createElement(this.selectors.SECTIONTAG);\n    newItem.dataset.for = \"section\";\n    newItem.dataset.id = sectionid;\n    newItem.dataset.number = section.number;\n    // The legacy actions.js requires a specific ID and class to refresh the section.\n    newItem.id = `section-${sectionid}`;\n    newItem.classList.add(this.classes.SECTION);\n    container.append(newItem);\n    this._reloadSection({\n      element: section,\n    });\n    return newItem;\n  }\n\n  /**\n   * Fix/reorder the section or cms order.\n   *\n   * @param {Element} container the HTML element to reorder.\n   * @param {Array} neworder an array with the ids order\n   * @param {string} selector the element selector\n   * @param {Object} dettachedelements a list of dettached elements\n   * @param {function} createMethod method to create missing elements\n   */\n  async _fixOrder(\n    container,\n    neworder,\n    selector,\n    dettachedelements,\n    createMethod\n  ) {\n    if (container === undefined) {\n      return;\n    }\n\n    // Empty lists should not be visible.\n    if (!neworder.length) {\n      container.classList.add(\"hidden\");\n      container.innerHTML = \"\";\n      return;\n    }\n\n    // Grant the list is visible (in case it was empty).\n    container.classList.remove(\"hidden\");\n\n    // Move the elements in order at the beginning of the list.\n    neworder.forEach((itemid, index) => {\n      let item =\n        this.getElement(selector, itemid) ??\n        dettachedelements[itemid] ??\n        createMethod(container, itemid);\n      if (item === undefined) {\n        // Missing elements cannot be sorted.\n        return;\n      }\n      // Get the current elemnt at that position.\n      const currentitem = container.children[index];\n      if (currentitem === undefined) {\n        container.append(item);\n        return;\n      }\n      if (currentitem !== item) {\n        container.insertBefore(item, currentitem);\n      }\n    });\n\n    // Dndupload add a fake element we need to keep.\n    let dndFakeActivity;\n\n    // Remove the remaining elements.\n    while (container.children.length > neworder.length) {\n      const lastchild = container.lastChild;\n      if (lastchild?.classList?.contains(\"dndupload-preview\")) {\n        dndFakeActivity = lastchild;\n      } else {\n        dettachedelements[lastchild?.dataset?.id ?? 0] = lastchild;\n      }\n      container.removeChild(lastchild);\n    }\n    // Restore dndupload fake element.\n    if (dndFakeActivity) {\n      container.append(dndFakeActivity);\n    }\n  }\n}\n"], "names": ["Component", "BaseComponent", "create", "descriptor", "name", "selectors", "SECTION", "SECTION_ITEM", "SECTION_CMLIST", "COURSE_SECTIONLIST", "CM", "PAGE", "TOGGLER", "COLLAPSE", "TOGGLEALL", "ACTIVITYTAG", "SECTIONTAG", "classes", "COLLAPSED", "ACTIVITY", "STATEDREADY", "dettachedCms", "dettachedSections", "sections", "cms", "sectionReturn", "debouncedReloads", "Map", "sectionname", "pluralsectionname", "target", "element", "document", "getElementById", "reactive", "stateReady", "state", "_indexContents", "addEventListener", "this", "_sectionTogglers", "toogleAll", "getElement", "collapseElementIds", "getElements", "map", "id", "setAttribute", "join", "_allSection<PERSON><PERSON><PERSON>", "e", "key", "_refreshAllSectionsToggler", "supportComponents", "isEditing", "DispatchActions", "classList", "add", "CourseEvents", "manualCompletionToggled", "_completion<PERSON><PERSON>ler", "querySelector", "_<PERSON><PERSON><PERSON><PERSON>", "event", "sectionlink", "closest", "closestCollapse", "isChevron", "section", "toggler", "isCollapsed", "contains", "sectionId", "getAttribute", "dispatch", "preventDefault", "isAllCollapsed", "course", "get", "sectionlist", "getWatchers", "watch", "handler", "_reloadCm", "_refreshSectionNumber", "_refreshSectionCollapsed", "_startProcessing", "_refreshCourseSectionlist", "_refreshSectionCmlist", "Error", "contentcollapsed", "collapsibleId", "dataset", "replace", "collapsible", "collapse", "allcollapsed", "allexpanded", "for<PERSON>ach", "remove", "detail", "undefined", "cmid", "completed", "pageOffset", "scrollTop", "items", "getExporter", "allItemsArray", "pageItem", "every", "item", "index", "type", "url", "offsetTop", "number", "sectionid", "inplace", "inplaceeditable", "getInplaceEditable", "currentvalue", "getValue", "currentitemid", "getItemId", "rawtitle", "setValue", "cmlist", "listparent", "createCm", "_createCmItem", "bind", "_fixOrder", "createSection", "_createSectionItem", "_scanIndex", "Section", "CmItem", "selector", "creationhandler", "_item$dataset", "unregister", "indexed", "_getDebouncedReloadCm", "debouncedReload", "cmId", "<PERSON><PERSON><PERSON>", "pendingReload", "Pending", "delete", "cmitem", "Fragment", "loadFragment", "Config", "courseContextId", "courseid", "courseId", "sr", "then", "html", "js", "replaceNode", "resolve", "catch", "set", "_reloadSection", "sectionitem", "container", "newItem", "createElement", "for", "append", "neworder", "dettachedelements", "createMethod", "length", "innerHTML", "dndFakeActivity", "itemid", "currentitem", "children", "insertBefore", "lastchild", "<PERSON><PERSON><PERSON><PERSON>", "_lastchild$classList", "_lastchild$dataset", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;+oCAuCqBA,kBAAkBC,wBAMrCC,OAAOC,2CAEAC,KAAO,qBAEPC,UAAY,CACfC,+BACAC,0CACAC,qCACAC,qDACAC,yBACAC,aACAC,qDACAC,oCACAC,sCAEAC,YAAa,KACbC,WAAY,WAGTC,QAAU,CACbC,sBAEAC,oBACAC,yBACAd,wBAGGe,aAAe,QACfC,kBAAoB,QAEpBC,SAAW,QACXC,IAAM,QAENC,4CAAgBtB,WAAWsB,qEAAiB,OAC5CC,iBAAmB,IAAIC,SACvBC,YAAczB,WAAWyB,iBACzBC,kBAAoB1B,WAAW0B,8BAcpCC,OACAzB,UACAoB,cACAG,YACAC,0BAEO,IAAI7B,UAAU,CACnB+B,QAASC,SAASC,eAAeH,QACjCI,UAAU,0CACV7B,oBACAoB,4BACAG,wBACAC,sCASJM,WAAWC,YACJC,sBAEAC,iBAAiBC,KAAKR,QAAS,QAASQ,KAAKC,wBAG5CC,UAAYF,KAAKG,WAAWH,KAAKlC,UAAUS,cAC7C2B,UAAW,OAGPE,mBAAqB,IADFJ,KAAKK,YAAYL,KAAKlC,UAAUQ,WACRgC,KAC9Cd,SAAYA,QAAQe,KAEvBL,UAAUM,aAAa,gBAAiBJ,mBAAmBK,KAAK,WAE3DV,iBAAiBG,UAAW,QAASF,KAAKU,yBAC1CX,iBAAiBG,UAAW,WAAYS,IAE7B,MAAVA,EAAEC,UACCF,mBAAmBC,WAGvBE,2BAA2BhB,OAG9BG,KAAKL,SAASmB,oBAEZd,KAAKL,SAASoB,eACZC,iBAAgBhB,KAAM,gBAIvBR,QAAQyB,UAAUC,IAAIlB,KAAKtB,QAAQG,mBAIrCkB,iBACHC,KAAKR,QACL2B,aAAaC,wBACbpB,KAAKqB,yBAIFtB,iBACHN,SAAS6B,cAActB,KAAKlC,UAAUM,MACtC,SACA4B,KAAKuB,gBAYTtB,iBAAiBuB,aACTC,YAAcD,MAAMjC,OAAOmC,QAAQ1B,KAAKlC,UAAUO,SAClDsD,gBAAkBH,MAAMjC,OAAOmC,QAAQ1B,KAAKlC,UAAUQ,UAGtDsD,UAAYD,6BAAAA,gBAAiBD,QAAQ1B,KAAKlC,UAAUE,iBAEtDyD,aAAeG,UAAW,iCACtBC,QAAUL,MAAMjC,OAAOmC,QAAQ1B,KAAKlC,UAAUC,SAC9C+D,QAAUD,QAAQP,cAActB,KAAKlC,UAAUQ,UAC/CyD,0CACJD,qBAAAA,QAASb,UAAUe,SAAShC,KAAKtB,QAAQC,sEAEvCiD,WAAaG,YAAa,OAEtBE,UAAYJ,QAAQK,aAAa,gBAClCvC,SAASwC,SACZ,0BACA,CAACF,YACAF,eAcTrB,mBAAmBc,+BACjBA,MAAMY,uBAGAC,eADSb,MAAMjC,OAAOmC,QAAQ1B,KAAKlC,UAAUS,WACrB0C,UAAUe,SAAShC,KAAKtB,QAAQC,WAExD2D,OAAStC,KAAKL,SAAS4C,IAAI,eAC5B5C,SAASwC,SACZ,sDACAG,OAAOE,+DAAe,IACrBH,gBASLI,0BAGO9C,SAAST,cAAgBc,KAAKd,cAG9Bc,KAAKL,SAASmB,kBAGZ,CAEL,CAAE4B,2BAA6BC,QAAS3C,KAAK4C,WAC7C,CAAEF,2BAA6BC,QAAS3C,KAAK4C,WAC7C,CAAEF,6BAA+BC,QAAS3C,KAAK4C,WAC/C,CAAEF,0BAA4BC,QAAS3C,KAAK4C,WAE5C,CAAEF,+BAAiCC,QAAS3C,KAAK6C,uBAEjD,CACEH,yCACAC,QAAS3C,KAAK8C,0BAGhB,CAAEJ,0BAA4BC,QAAS3C,KAAK+C,kBAC5C,CACEL,mCACAC,QAAS3C,KAAKgD,2BAEhB,CAAEN,+BAAiCC,QAAS3C,KAAKiD,uBAEjD,CAAEP,sBAAwBC,QAAS3C,KAAKF,iBAvBjC,GAqCXgD,8DAAyBjD,MAAEA,MAAFL,QAASA,oBAC1BD,OAASS,KAAKG,WAAWH,KAAKlC,UAAUC,QAASyB,QAAQe,QAC1DhB,aACG,IAAI2D,wCAAiC1D,QAAQe,WAG/CuB,QAAUvC,OAAO+B,cAActB,KAAKlC,UAAUQ,UAC9CyD,2CACJD,qBAAAA,QAASb,UAAUe,SAAShC,KAAKtB,QAAQC,wEAEvCa,QAAQ2D,mBAAqBpB,YAAa,+BACxCqB,4CACFtB,QAAQuB,QAAQ9D,8DAAUuC,QAAQI,aAAa,YAC5CkB,qBAGLA,cAAgBA,cAAcE,QAAQ,IAAK,UACrCC,YAAc9D,SAASC,eAAe0D,mBACvCG,uCAOEA,aAAaC,SAAShE,QAAQ2D,iBAAmB,OAAS,aAG9DtC,2BAA2BhB,OAQlCgB,2BAA2BhB,aACnBN,OAASS,KAAKG,WAAWH,KAAKlC,UAAUS,eACzCgB,kBAIDkE,cAAe,EACfC,aAAc,EAClB7D,MAAMgC,QAAQ8B,SAAS9B,UACrB4B,aAAeA,cAAgB5B,QAAQsB,iBACvCO,YAAcA,cAAgB7B,QAAQsB,gBAAtC,IAEEM,eACFlE,OAAO0B,UAAUC,IAAIlB,KAAKtB,QAAQC,WAClCY,OAAOiB,aAAa,iBAAiB,IAEnCkD,cACFnE,OAAO0B,UAAU2C,OAAO5D,KAAKtB,QAAQC,WACrCY,OAAOiB,aAAa,iBAAiB,IAWzCuC,wBAGOjE,aAAe,QACfC,kBAAoB,GAQ3BsC,8BAAmBwC,OAAEA,mBACJC,IAAXD,aAGClE,SAASwC,SAAS,eAAgB,CAAC0B,OAAOE,MAAOF,OAAOG,WAM/DzC,uBACQ0C,WAAaxE,SAAS6B,cAActB,KAAKlC,UAAUM,MAAM8F,UACzDC,MAAQnE,KAAKL,SAChByE,cACAC,cAAcrE,KAAKL,SAASE,WAE3ByE,SAAW,KACfH,MAAMI,OAAOC,aACLC,MAAsB,YAAdD,KAAKE,KAAqB1E,KAAKhB,SAAWgB,KAAKf,YACtC6E,IAAnBW,MAAMD,KAAKjE,WACN,QAGHf,QAAUiF,MAAMD,KAAKjE,IAAIf,cAEb,OAAdgF,KAAKE,MAAkBF,KAAKG,KAAQ3E,KAAKL,SAASoB,WAGtDuD,SAAWE,KACJP,YAAczE,QAAQoF,WAHpBX,YAAczE,QAAQoF,SAG/B,IAEEN,eACG3E,SAASwC,SAAS,cAAemC,SAASI,KAAMJ,SAAS/D,IAiBlEsC,iCAAsBrD,QAAEA,qBAEhBD,OAASS,KAAKG,WAAWH,KAAKlC,UAAUC,QAASyB,QAAQe,QAC1DhB,cAKLA,OAAOgB,qBAAgBf,QAAQqF,QAI/BtF,OAAO8D,QAAQyB,UAAYtF,QAAQqF,OAEnCtF,OAAO8D,QAAQwB,OAASrF,QAAQqF,aAG1BE,QAAUC,0BAAgBC,mBAC9B1F,OAAO+B,cAActB,KAAKlC,UAAUE,kBAElC+G,QAAS,OAGLG,aAAeH,QAAQI,WACvBC,cAAgBL,QAAQM,YAEH,KAAvBN,QAAQI,aAGRC,eAAiB5F,QAAQe,IACxB2E,cAAgB1F,QAAQ8F,UAAgC,IAApB9F,QAAQ8F,UAE7CP,QAAQQ,SAAS/F,QAAQ8F,YAYjCrC,qDAAsBzD,QAAEA,qBAChBgG,+BAAShG,QAAQgG,kDAAU,GAC3B3D,QAAU7B,KAAKG,WAAWH,KAAKlC,UAAUC,QAASyB,QAAQe,IAC1DkF,WAAa5D,qBAAAA,QAASP,cAActB,KAAKlC,UAAUG,gBAEnDyH,SAAW1F,KAAK2F,cAAcC,KAAK5F,MACrCyF,iBACGI,UACHJ,WACAD,OACAxF,KAAKlC,UAAUK,GACf6B,KAAKlB,aACL4G,UAWN1C,8DAA0BxD,QAAEA,kBAES,GAA/BQ,KAAKL,SAAST,2BAGZsD,yCAAchD,QAAQgD,iEAAe,GACrCiD,WAAazF,KAAKG,WAAWH,KAAKlC,UAAUI,oBAE5C4H,cAAgB9F,KAAK+F,mBAAmBH,KAAK5F,MAC/CyF,iBACGI,UACHJ,WACAjD,YACAxC,KAAKlC,UAAUC,QACfiC,KAAKjB,kBACL+G,eAUNhG,sBAEOkG,WAAWhG,KAAKlC,UAAUC,QAASiC,KAAKhB,UAAWwF,MAC/C,IAAIyB,iBAAQzB,aAIhBwB,WAAWhG,KAAKlC,UAAUK,GAAI6B,KAAKf,KAAMuF,MACrC,IAAI0B,gBAAO1B,QAatBwB,WAAWG,SAAU1B,MAAO2B,iBACZpG,KAAKK,sBAAe8F,kCAC5BxC,SAASa,yBACRA,kCAAAA,KAAMnB,kCAANgD,cAAe9F,UAIWuD,IAA3BW,MAAMD,KAAKnB,QAAQ9C,KACrBkE,MAAMD,KAAKnB,QAAQ9C,IAAI+F,aAGzB7B,MAAMD,KAAKnB,QAAQ9C,IAAM6F,gBAAgB,IACpCpG,KACHR,QAASgF,OAGXA,KAAKnB,QAAQkD,SAAU,EAAvB,IAaJ3D,qBAAUpD,QAAEA,mBACLQ,KAAKG,WAAWH,KAAKlC,UAAUK,GAAIqB,QAAQe,WAGxBP,KAAKwG,sBAAsBhH,QAAQe,GAC3DkG,GAQFD,sBAAsBE,YACdC,mDAA8CD,UAChDD,gBAAkBzG,KAAKb,iBAAiBoD,IAAIoE,eAC5CF,uBACKA,sBAEHG,cAAgB,IAAIC,iBAAQF,mBA0BlCF,iBAAkB,oBAzBH,oCACRtH,iBAAiB2H,OAAOH,kBACvBI,OAAS/G,KAAKG,WAAWH,KAAKlC,UAAUK,GAAIuI,UAC7CK,cAGWC,kBAASC,aACvB,oBACA,SACAC,gBAAOC,gBACP,CACE5G,GAAImG,KACJU,SAAUF,gBAAOG,SACjBC,iCAAItH,KAAKL,SAAST,qEAAiB,IAIpCqI,MAAK,CAACC,KAAMC,yBACDC,YAAYX,OAAQS,KAAMC,SAC/B3H,iBACL8G,cAAce,aAGfC,UAE8B,UAC9BzI,iBAAiB0I,IAAIlB,WAAYF,iBAC/BA,gBAYTqB,0BAAetI,QAAEA,qBACToH,cAAgB,IAAIC,8DACcrH,QAAQe,KAE1CwH,YAAc/H,KAAKG,WAAWH,KAAKlC,UAAUC,QAASyB,QAAQe,OAChEwH,YAAa,4BACCf,kBAASC,aACvB,oBACA,UACAC,gBAAOC,gBACP,CACE5G,GAAIf,QAAQe,GACZ6G,SAAUF,gBAAOG,SACjBC,kCAAItH,KAAKL,SAAST,uEAAiB,IAIpCqI,MAAK,CAACC,KAAMC,yBACDC,YAAYK,YAAaP,KAAMC,SACpC3H,iBACL8G,cAAce,aAGfC,SAcPjC,cAAcqC,UAAWjE,YACjBkE,QAAUxI,SAASyI,cAAclI,KAAKlC,UAAUU,oBACtDyJ,QAAQ5E,QAAQ8E,IAAM,SACtBF,QAAQ5E,QAAQ9C,GAAKwD,KAErBkE,QAAQ1H,oBAAewD,MACvBkE,QAAQhH,UAAUC,IAAIlB,KAAKtB,QAAQE,UACnCoJ,UAAUI,OAAOH,cACZrF,UAAU,CACbpD,QAASQ,KAAKL,SAAS4C,IAAI,KAAMwB,QAE5BkE,QAaTlC,mBAAmBiC,UAAWlD,iBACtBjD,QAAU7B,KAAKL,SAAS4C,IAAI,UAAWuC,WACvCmD,QAAUxI,SAASyI,cAAclI,KAAKlC,UAAUW,mBACtDwJ,QAAQ5E,QAAQ8E,IAAM,UACtBF,QAAQ5E,QAAQ9C,GAAKuE,UACrBmD,QAAQ5E,QAAQwB,OAAShD,QAAQgD,OAEjCoD,QAAQ1H,qBAAgBuE,WACxBmD,QAAQhH,UAAUC,IAAIlB,KAAKtB,QAAQX,SACnCiK,UAAUI,OAAOH,cACZH,eAAe,CAClBtI,QAASqC,UAEJoG,wBAaPD,UACAK,SACAlC,SACAmC,kBACAC,sBAEkBzE,IAAdkE,qBAKCK,SAASG,cACZR,UAAU/G,UAAUC,IAAI,eACxB8G,UAAUS,UAAY,QA6BpBC,oBAxBJV,UAAU/G,UAAU2C,OAAO,UAG3ByE,SAAS1E,SAAQ,CAACgF,OAAQlE,wCACpBD,4CACFxE,KAAKG,WAAWgG,SAAUwC,qDAC1BL,kBAAkBK,+BAClBJ,aAAaP,UAAWW,gBACb7E,IAATU,kBAKEoE,YAAcZ,UAAUa,SAASpE,YACnBX,IAAhB8E,YAIAA,cAAgBpE,MAClBwD,UAAUc,aAAatE,KAAMoE,aAJ7BZ,UAAUI,OAAO5D,SAYdwD,UAAUa,SAASL,OAASH,SAASG,QAAQ,gCAC5CO,UAAYf,UAAUgB,0DACxBD,8CAAAA,UAAW9H,2CAAXgI,qBAAsBjH,SAAS,qBACjC0G,gBAAkBK,eAElBT,gDAAkBS,4CAAAA,UAAW1F,6CAAX6F,mBAAoB3I,0DAAM,GAAKwI,UAEnDf,UAAUmB,YAAYJ,WAGpBL,iBACFV,UAAUI,OAAOM"}