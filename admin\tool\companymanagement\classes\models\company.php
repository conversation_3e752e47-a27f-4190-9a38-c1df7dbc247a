<?php

namespace tool_companymanagement\models;

use local_ssystem\constants\uf;
use local_ssystem\util\data_formatter_trait;
use tool_lfxp\models\persistence\entity;
use lang_string;
use tool_companymanagement\util\cnpj_helper;

class company extends entity
{
    use data_formatter_trait;

    const TABLE = 'tool_companymanagement';

    protected static function track_dirty_fields(): bool
    {
        return false;
    }

    /**
     * Defines the properties of the class.
     *
     * @return array An array containing the properties of the class.
     */
    protected static function define_properties()
    {
        return [
            'name' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'cnpj' => [
                'type' => PARAM_TEXT,
            ],
            'state' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'deleted' => [
                'type' => PARAM_BOOL,
                'default' => false,
            ],
        ];
    }

    /**
     * Returns the state of the company.
     *
     * @return string The state of the company.
     */
    protected function get_state()
    {
        if (in_array($this->raw_get('state'), uf::get_ufs())) {
            return uf::UFS[$this->raw_get('state')];
        }

        return $this->raw_get('state');
    }

    protected function set_cnpj($value){
        $this->raw_set('cnpj', self::unformat_CNPJ($value));
    }

    /**
     * Returns the formatted CNPJ.
     *
     * @return string|null The formatted CNPJ or null if no CNPJ is provided.
     */
    protected function get_cnpj()
    {
        $cnpj = $this->raw_get('cnpj');

        if ($cnpj) {
            return $this->format_CNPJ($cnpj);
        }

        return $cnpj;
    }

    protected function get_name()
    {
        if($name = $this->raw_get('name')){
            return $name;
        }

        return $this->get_cnpj();
    }

    /**
     * Returns the UF of the company.
     *
     * @return string The UF of the company.
     */
    public function get_uf()
    {
        return $this->raw_get('state');
    }

    /**
     * Returns the unformatted CNPJ of the company.
     *
     * @return string The unformatted CNPJ of the company.
     */
    public function get_raw_cnpj()
    {
        return $this->raw_get('cnpj');
    }

    protected function validate_cnpj($cnpj) {
        if(cnpj_helper::validate($cnpj)){
            return true;
        }
        return new lang_string('cnpjinvalid', 'tool_companymanagement');
    }

    /**
     * Returns whether the company has been deleted.
     *
     * @return bool
     */
    public function is_deleted(): bool
    {
        return (bool)$this->get('deleted');
    }
}
