<?php namespace tool_migration\models;

defined('MOODLE_INTERNAL')||die();

use Exception;


class usuario_segmento extends abstract_migratable_entity{
    const TABLE = 'eadtech_usuarios_segmento';

    public static function define_identifier(): string {
        throw new \coding_exception("eadtech_usuarios_segmento is a relationship table");
    }
    
    protected static function define_model_properties(): array{
        return [
            'source_codaluno' => [
                'type' => PARAM_INT,
            ],
            'source_codsegmento' => [
                'type' => PARAM_INT,
            ],
            'source_tipo' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_principal' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
        ];
    }

    public function get_idnumber(): string {
        return md5($this->get('source_codaluno').$this->get('source_codsegmento'));
    }

    public function to_audience_member() : object {
        $audience_member = (object)[
            'audienceid' => segmento::get_instanceid_from_identifier($this->get('source_codsegmento')),
            'userid' => estudante::get_instanceid_from_identifier($this->get('source_codaluno')),
            'manual' => 1,
        ];

        if($id = $this->get('instanceid')){
            $audience_member->id = $id;
        }

        if(empty($audience_member->audienceid)){
            throw new Exception("Público-alvo não encontrado!");
        }

        if(empty($audience_member->userid)){
            throw new Exception("Usuário não encontrado!");
        }

        return $audience_member;
    }


    public static function upsert_from_csv(array $data) : bool {
        global $DB;

        $hash = static::generate_hash($data);
        $data = static::format_csv_data($data);

        unset($data['id']);

        foreach ($data as $key => $value) {
            if($value == 'NULL'){
                unset($data[$key]);
            }
        }

        $existing = $DB->get_record(static::TABLE, [
            'source_codaluno' => $data['source_codaluno'],
            'source_codsegmento' => $data['source_codsegmento'],
        ], 'id, hash');

        if(!$existing){
            $data['hash'] = $hash;
            $data['instanceid'] = 0;
            $DB->insert_record(static::TABLE, $data);
            return true;
        }

        if($hash != $existing->hash){
            $data['id'] = $existing->id;
            $data['hash'] = $hash;
            $data['needs_update'] = 1;
            $DB->update_record(static::TABLE, $data);
            return true;
        }

        return false;
    }
}
