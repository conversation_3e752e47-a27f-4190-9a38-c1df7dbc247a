{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/content/section

    Displays a course section.

    Note: This template is a wrapper around the section/content template to allow course formats and theme designers to
    modify parts of the wrapper without having to copy/paste the entire template.

    Example context (json):
    {
        "num": 3,
        "id": 35,
        "controlmenu": "[tools menu]",
        "header": {
            "name": "Section title",
            "title": "<a href=\"http://moodle/course/view.php?id=5#section-0\">Section title</a>",
            "url": "#",
            "ishidden": true
        },
        "cmlist": {
            "cms": [
                {
                    "cmitem": {
                        "cmformat": {
                            "cmname": "<a class=\"aalink\" href=\"#\"><span class=\"instancename\">Forum example</span></a>",
                            "hasname": "true"
                        },
                        "id": 3,
                        "module": "forum",
                        "anchor": "activity-3",
                        "extraclasses": "newmessages"
                    }
                },
                {
                    "cmitem": {
                        "cmformat": {
                            "cmname": "<a class=\"aalink\" href=\"#\"><span class=\"instancename\">Assign example</span></a>",
                            "hasname": "true"
                        },
                        "id": 4,
                        "anchor": "activity-4",
                        "module": "assign",
                        "extraclasses": ""
                    }
                }
            ],
            "hascms": true
        },
        "ishidden": false,
        "iscurrent": true,
        "currentlink": "<span class=\"accesshide\">This topic</span>",
        "availability": {
            "info": "<span class=\"badge badge-info\">Hidden from students</span>",
            "hasavailability": true
        },
        "summary": {
            "summarytext": "Summary text!"
        },
        "controlmenu": {
            "menu": "<a href=\"#\" class=\"d-inline-block dropdown-toggle icon-no-margin\">Edit<b class=\"caret\"></b></a>",
            "hasmenu": true
        },
        "cmcontrols": "[Add an activity or resource]",
        "iscoursedisplaymultipage": true,
        "sectionreturnid": 0,
        "contentcollapsed": false,
        "insertafter": true,
        "numsections": 42,
        "sitehome": false,
        "highlightedlabel" : "Highlighted"
    }
}}
<li id="section-{{num}}"
    class="section course-section main {{#onlysummary}} section-summary {{/onlysummary}} clearfix
            {{#ishidden}} hidden {{/ishidden}} {{#iscurrent}} current {{/iscurrent}}
            {{#isstealth}} orphaned {{/isstealth}} border-bottom py-3"
    data-sectionid="{{num}}"
    data-sectionreturnid="{{sectionreturnid}}"
    data-for="section"
    data-id="{{id}}"
    data-number="{{num}}"
>
    <div class="course-section-header d-flex"
        data-for="section_title"
        data-id="{{id}}"
        data-number="{{num}}"
    >
        {{$ core_courseformat/local/content/section/content }}
            {{> format_trails/local/content/section/content }}
        {{/ core_courseformat/local/content/section/content }}
    </div>
</li>
