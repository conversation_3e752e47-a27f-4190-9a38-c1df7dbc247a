<?php namespace tool_migration;

use advanced_testcase;
use tool_migration\models\estudante;
use tool_migration\importers\estudante_importer;
use tool_migration\util\ingestion_helper;
use local_ssystem\constants\custom_profile_fields;

global $CFG;
require_once($CFG->dirroot . '/user/profile/lib.php');
require_once($CFG->dirroot . '/user/lib.php');

use core_date;

final class estudante_importer_test extends advanced_testcase {

    protected string $previousTimezone;

    protected function setUp(): void {
        global $CFG;

        parent::setUp();

        $CFG->timezone = 'America/Sao_Paulo';
        date_default_timezone_set('America/Sao_Paulo');
        core_date::set_default_server_timezone();
    }

    /**
     * @group !current
     *
     * @return void
     */
    public function test_import(): void {
        global $DB;
        $this->resetAfterTest(true);

        $row = [
            'source_codaluno'           => 21,
            'source_nome'               => '<PERSON>',
            'source_email'              => '<EMAIL>',
            'source_estado'             => 'DF',
            'source_sexo'               => 'M',
            'source_nascimento'         => '1975-07-05 00:00:00.000',
            'source_escolaridade'       => 'Superior',
            'source_estadocivil'        => 'Solteiro',
            'source_renda'              => '3000-4000',
            'source_datacadastro'       => '2022-05-26 15:29:30.193',
            'source_cnpj_lista'         => '00330845000579',
            'source_cargo'              => 'Analista',
            'source_nivelocupacional'   => 'Analista',
            'source_cpf'                => '77263693100',
            'source_emailsecundario'    => '<EMAIL>',
            'source_celular'            => '6192714354',
            'source_campolivre3'        => '',
            'source_perfil'             => 'COLABORADOR',
            'source_status'             => 'A',
            'source_filial'             => 'SEBRAE/NA',
            'source_dataadmissao'       => '2011-04-11 00:00:00.000',
            'source_dataexpiracao'      => '',
            'source_dataalteracao'      => '2023-11-28 16:00:32.000',
            'source_nomesocial'         => 'Fulano',
            'source_pais'               => '',
            'source_funcao'             => '',
            'source_degreed_usuarioativo' => '1',
            'source_ufsebrae'           => 'SEBRAE/NA',
            'source_datamodificacao'    => '2023-11-28 16:00:32.000',
            'hash' => '123456',
        ];

        $estudante = new estudante(0, (object)$row);
        $estudante->save();

        $importer = new estudante_importer(new \text_progress_trace());
        $importer->import_pending();

        $user = \core_user::get_user_by_username('77263693100');

        $this->assertNotFalse($user);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('Elias', $user->firstname);
        $this->assertEquals('Alexandre Oliveira Dos Santos', $user->lastname);
        $this->assertEquals('amei', $user->auth);
        $this->assertEquals('BR', $user->country);

        $profile = profile_user_record($user->id);
        $this->assertEquals('DF', $profile->{custom_profile_fields::STATE_FIELD});
        $this->assertEquals(custom_profile_fields::GENDER_MALE, $profile->{custom_profile_fields::GENDER_FIELD});
        $this->assertEquals(ingestion_helper::str_to_timestamp('1975-07-05 00:00:00.000'), (int) $profile->{custom_profile_fields::BIRTHDATE_FIELD});
        $this->assertEquals('<EMAIL>', $profile->{custom_profile_fields::SECONDARY_EMAIL_FIELD});
    }

    /**
     * @group !current
     */
    public function test_import_and_update_pending(): void {
        global $DB;

        $this->resetAfterTest(true);
        $cpf = '77263693100';
        $original = [
            'source_codaluno'        => 21,
            'source_nome'            => 'Elias Alexandre Oliveira dos Santos',
            'source_email'           => '<EMAIL>',
            'source_estado'          => 'DF',
            'source_sexo'            => 'M',
            'source_nascimento'      => '1975-07-05 00:00:00.000',
            'source_datacadastro'    => '2022-05-26 15:29:30.193',
            'source_cpf'             => $cpf,
            'source_emailsecundario' => '<EMAIL>',
            'hash'                   => 'hash-inicial',
        ];
        $est1 = new estudante(0, (object)$original);
        $est1->save();

        $importer = new estudante_importer(new \text_progress_trace());
        $importer->import_pending();

        $user = \core_user::get_user_by_username($cpf);
        $this->assertNotFalse($user);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('Elias', $user->firstname);
        $this->assertEquals('Alexandre Oliveira Dos Santos', $user->lastname);

        // Updating
        $mig = $DB->get_record(estudante::TABLE, ['source_codaluno' => 21], '*', MUST_EXIST);
        $mig->source_nome  = 'Elias Atualizado Santos';
        $mig->source_email = '<EMAIL>';
        $mig->source_estado = 'SP';
        $mig->source_sexo  = 'F';
        $mig->needs_update = 1;
        $DB->update_record(estudante::TABLE, $mig);

        $importer->update_pending();

        $count = $DB->count_records('user', ['username' => $cpf]);
        $this->assertEquals(1, $count);

        $user = \core_user::get_user_by_username($cpf);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('Elias', $user->firstname);
        $this->assertEquals('Atualizado Santos', $user->lastname);

        $profile = profile_user_record($user->id);
        $this->assertEquals('SP', $profile->{custom_profile_fields::STATE_FIELD});
        $this->assertEquals(custom_profile_fields::GENDER_FEMALE, $profile->{custom_profile_fields::GENDER_FIELD});
    }
}
