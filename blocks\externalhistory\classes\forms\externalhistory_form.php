<?php

namespace block_externalhistory\forms;

require_once($CFG->libdir . '/formslib.php');

use local_ssystem\constants\custom_course_fields;

/**
 * Class externalhistory_form
 * @package block_externalhistory\forms
 */
class externalhistory_form extends \moodleform
{
    /**
     * externalhistory_form constructor.
     * 
     * @param null $customdata
     */
    public function __construct($customdata = null)
    {
        $url = new \moodle_url('/blocks/externalhistory/edit.php');
        parent::__construct($url, $customdata);
    }

    /**
     * @throws \coding_exception
     */
    public function definition()
    {
        global $USER;

        $mform = $this->_form;

        // Hidden fields
        $mform->addElement('hidden', 'id');
        $mform->setType('id', PARAM_INT);

        $mform->addElement('hidden', 'userid');
        $mform->setType('userid', PARAM_INT);
        $mform->setConstant('userid', $USER->id);        

        // Solution name
        $mform->addElement('text', 'solutionname', get_string('solutionname', 'block_externalhistory'), [
            'class' => 'formexternalhistory', 
            'maxlength' => '254', 
            'size' => '50'
        ]);
        $mform->setType('solutionname', PARAM_TEXT);
        $mform->addRule('solutionname', get_string('required_solutionname', 'block_externalhistory'), 'required', null, 'client');

        // Format  
        $options = $this->get_solution_format_options();
        
        $mform->addElement('select', 'format', get_string('format', 'block_externalhistory'), $options);
        $mform->addRule('format', get_string('required_format', 'block_externalhistory'), 'required', null, 'client');

        // Description
        $mform->addElement('textarea', 'description', get_string("description", "block_externalhistory"), 'wrap="virtual" rows="10" cols="50"');
        $mform->addRule('description', get_string('required_description', 'block_externalhistory'), 'required', null, 'client');


        // Hours completed
        $mform->addElement('duration', 'hourscompleted', get_string('hourscompleted', 'block_externalhistory'));
        $mform->addRule('hourscompleted', get_string('required_hourscompleted', 'block_externalhistory'), 'required', null, 'client');

        // Dates
        $mform->addElement('html', '<hr><h6>' . get_string('execution_date', 'block_externalhistory') . '</h6>');
        $mform->addElement('date_selector', 'startdate', get_string('start_date', 'block_externalhistory'));
        $mform->addElement('date_selector', 'enddate', get_string('end_date', 'block_externalhistory'));
        $mform->addElement('html', '<hr>');       

        // Institution
        $mform->addElement('text', 'institution', get_string('institution', 'block_externalhistory'), [
            'class' => 'formexternalhistory', 
            'maxlength' => '254', 
            'size' => '50'
        ]);
        $mform->setType('institution', PARAM_TEXT);
        $mform->addRule('institution', get_string('required_institution', 'block_externalhistory'), 'required', null, 'client');

        // Certificate file
        $mform->addElement('filepicker', 'certfile',
            get_string('certfile', 'block_externalhistory'),
            null,
            ['maxbytes' => 10000000, 'subdirs' => false, 'maxfiles' => 1]
        );
        $mform->setType('certfile', PARAM_INT);
        $mform->addRule('certfile', get_string('required_certfile', 'block_externalhistory'), 'required', null, 'client');

        $mform->addElement('html', '<hr>');               
        $this->add_action_buttons();
    }

    /**
     * @param array $data
     * @param array $files
     * @return array
     * @throws \coding_exception
     */
    public function validation($data, $files)
    {
        $errors = parent::validation($data, $files);

        if (empty($data['solutionname'])) {
            $errors['solutionname'] = get_string('required_solutionname', 'block_externalhistory');
        }

        if (empty($data['hourscompleted']) || ($data['hourscompleted'] < 1)) {
            $errors['hourscompleted'] = get_string('required_hourscompleted', 'block_externalhistory');
        }

        if (empty($data['format'])) {
            $errors['format'] = get_string('required_format', 'block_externalhistory');
        }

        if ($data['startdate'] > $data['enddate']) {
            $errors['startdate'] = get_string('invalid_dates', 'block_externalhistory');
        }

        return $errors;
    }

    protected function get_solution_format_options() : array {
        $options = ['' => get_string('select_format', 'block_externalhistory')];
        return $options + array_combine(custom_course_fields::get_solution_format_options(),custom_course_fields::get_solution_format_options());
    }
}
