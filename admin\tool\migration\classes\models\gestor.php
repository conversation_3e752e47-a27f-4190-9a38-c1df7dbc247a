<?php namespace tool_migration\models;

defined('MOODLE_INTERNAL') || die();

use tool_migration\util\ingestion_helper;
use \local_ssystem\constants\custom_profile_fields;

class gestor extends abstract_migratable_entity {
    const TABLE = 'eadtech_gestores';

    const STATUS_INACTIVE = 'I';
    const STATUS_DELETED = 'E';
    const STATUS_ACTIVE = 'A';

    public static function define_identifier(): string {
        return 'source_codusuariogestor';
    }

    protected static function define_model_properties(): array {
        return [
            'source_codusuariogestor' => [
                'type' => PARAM_INT,
            ],
            'source_nomecompleto' => [
                'type' => PARAM_TEXT,
            ],
            'source_login' => [
                'type' => PARAM_RAW,
            ],
            'source_email' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_datadecriacao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_datamodificacao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_status' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_trocarsenha' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_acessarmensagens' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_webaula' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_ehelpdesk' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_acessobiometrico' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_tran_userid' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_corpodocente' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
        ];
    }

    public function get_username() : string {
        return mb_strtolower($this->get('source_login'));
    }

    public function get_idnumber(): string {
        return 'LEGADO-' . $this->get('source_codusuariogestor');
    }

    protected function get_email() : string {
        $email = ingestion_helper::format_email($this->get('source_email'));

        if(empty($email)){
            $email = ingestion_helper::generate_fake_email($this->get('source_codusuariogestor'));
        }

        return $email;
    }


    /**
     * To an user object
     *
     * @return object
     */
    public function to_user() : object {
        global $CFG;

        $user = ingestion_helper::split_fullname($this->get('source_nomecompleto'));
        $user->idnumber = $this->get_idnumber();
        $user->username = $this->get_username();
        $user->email = $this->get_email();
        $user->confirmed = 1;
        $user->suspended = (int) $this->is_suspended();
        $user->country = 'BR';
        $user->lang = get_string_manager()->translation_exists('pt_br') ? 'pt_br' : 'en';
        $user->auth = 'amei';
        $user->mnethostid = $CFG->mnet_localhost_id;

        foreach ($this->get_custom_fields() as $key => $value) {
            $key = "profile_field_$key";
            $user->$key = $value;
        }

        if($id = $this->get('instanceid')){
            $user->id = $id;
        }

        return $user;
    }

    protected function get_custom_fields() : array {
        $fields = [];

        if($status = $this->get('source_status')){
            $key = custom_profile_fields::STATUS_TYPE_FIELD;
            if($status = self::format_status($status)){
                $fields[$key] = $status;
            }
        }

        return $fields;
    }

    public function is_suspended() : bool {
        return $this->get('source_status') != self::STATUS_ACTIVE;
    }

    public function is_deleted() : bool {
        return $this->get('source_status') == self::STATUS_DELETED || str_contains($this->get_username(), 'EXCLUIDO:');
    }

    public static function format_status(string $status) : string {
        return match ($status) {
            self::STATUS_INACTIVE => custom_profile_fields::STATUS_TYPE_INACTIVE,
            self::STATUS_ACTIVE => custom_profile_fields::STATUS_TYPE_ACTIVE,
            default => '',
        };
    }
}
