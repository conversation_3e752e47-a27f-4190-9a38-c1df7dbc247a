{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/content/section/cmitem

    Displays the course module list inside a course section.

    Example context (json):
    {
        "cmformat": {
            "cmname": "<a class=\"aalink\" href=\"#\"><span class=\"instancename\">Activity example</span></a>",
            "hasname": "true",
            "moveicon": "<i>[Move]</i>",
            "indent": 2,
            "afterlink": "<span class=\"badge badge-primary\">30 unread messages</span>",
            "hasextras": true,
            "extras": ["<span class=\"badge badge-secondary\">[extras]</span>"],
            "completion": "<span class=\"badge badge-success\">Completed!</span>"
        },
        "id": 3,
        "anchor": "module-3",
        "module": "forum",
        "extraclasses": "newmessages",
        "indent": 0
    }
}}
<li
    class="activity activity-wrapper {{module}} modtype_{{module}} {{extraclasses}} {{#hasinfo}}hasinfo{{/hasinfo}} {{#indent}}indented{{/indent}}"
    id="{{anchor}}"
    data-for="cmitem"
    data-id="{{id}}"
>
	{{#cmformat}}
		{{$ core_courseformat/local/content/cm}}
			{{> format_trails/local/content/cm}}
		{{/ core_courseformat/local/content/cm}}
	{{/cmformat}}
</li>
