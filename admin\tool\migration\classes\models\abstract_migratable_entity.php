<?php namespace tool_migration\models;

use core\persistent;
use Generator;
use coding_exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Abstract base class for entities that can be migrated from an external system into Moodle.
 * 
 */
abstract class abstract_migratable_entity extends persistent {

    /**
     * @throws coding_exception If a property key from define_model_properties does not start with 'source_'.
     * @return array
     */
    protected static function define_properties(): array {
        $definitions = static::define_model_properties();

        foreach (array_keys($definitions) as $key) {
            if (!str_starts_with($key, 'source_')) {
                throw new coding_exception("Property '{$key}' must start with 'source_' in " . static::class);
            }
        }

        return array_merge($definitions, [
            'custom_data' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'instanceid' => [
                'type' => PARAM_INT,
                'default' => 0,
            ],
            'legacyid' => [
                'type' => PARAM_INT,
                'default' => 0,
            ],
            'needs_update' => [
                'type' => PARAM_BOOL,
                'default' => false,
            ],
            'hash' => [
                'type' => PARAM_RAW,
                'default' => '',
            ],
        ]);
    }

    public static function generate_hash(array|object $data) : string {
        $data = (array) $data;
        ksort($data);
        return hash('md5', json_encode($data));
    }

    /**
     * Sets the custom structured data to be saved as JSON.
     *
     * @param array|object|null $data Structured data to store.
     * @return void
     */
    protected function set_custom_data(array|object|null $data): void {
        if ($data === null) {
            $this->raw_set('custom_data', null);
        } else {
            $this->raw_set('custom_data', json_encode($data));
        }
    }

    /**
     * Gets the custom structured data, decoded from JSON.
     *
     * @return array|null Returns the decoded array, or null if empty or invalid.
     */
    protected function get_custom_data(): ?array {
        $json = $this->get('custom_data');

        if ($json === null) {
            return null;
        }

        $decoded = json_decode($json, true);
        return json_last_error() === JSON_ERROR_NONE ? $decoded : null;
    }

    /**
     * Defines the entity-specific properties that should be merged into the persistent definition.
     *
     * This method must be implemented by child classes to specify custom fields.
     * 
     * All properties must be prefixed by "source_".
     *
     * @return array Array of entity-specific property definitions.
     */
    abstract protected static function define_model_properties(): array;

    /**
     * Returns the Idnumber of the entity in the source system.
     *
     * @return string
     */
    abstract public function get_idnumber(): string;

    abstract public static function define_identifier(): string;

    /**
     * Returns an entity based on its main identifier
     *
     * @param mixed $value
     * @return static|null
     */
    public static function get_by_identifier($value) : ?static {
        $identifier = static::define_identifier();
        return static::get_record([$identifier => $value]) ?: null;
    }

    public static function get_instanceid_from_identifier($value) : ?int {
        global $DB;
        $identifier = static::define_identifier();
        return $DB->get_field(static::TABLE, 'instanceid', [$identifier => $value]) ?: null;
    }

    /**
     * Returns a generator yielding entities that have not yet been imported.
     * 
     * @param int $limit Optional limit on the number of records to retrieve.
     * @return Generator
     */
    public static function get_pending_imports(int $limit = 0): Generator {
        global $DB;

        $recordset = $DB->get_recordset_select(
            static::TABLE,
            'instanceid = 0',
            null,
            'id',
            '*',
            0,
            $limit
        );

        foreach ($recordset as $record) {
            yield new static(0, $record);
        }
        $recordset->close();
    }

    /**
     * Returns a generator yielding entities that have already been imported but require updates.
     *
     * @param int $limit Optional limit on the number of records to retrieve.
     * @return Generator
     */
    public static function get_pending_updates(int $limit = 0): Generator {
        global $DB;

        $recordset = $DB->get_recordset_select(
            static::TABLE,
            'instanceid > 0 AND needs_update = 1',
            null,
            'id',
            '*',
            0,
            $limit
        );
        foreach ($recordset as $record) {
            yield new static(0, $record);
        }
        $recordset->close();
    }

    public function mark_as_imported(int $instanceid) : static {
        $this->set('instanceid', $instanceid);
        $this->save();
        return $this;
    }

    public function mark_as_updated() : static {
        $this->set('needs_update', false);
        $this->save();
        return $this;
    }

    protected static function format_csv_data(array $data) : array {
        return $data;
    }

    public static function upsert_from_csv(array $data) : bool {
        global $DB;

        $identifier_column = static::define_identifier();
        if(empty($data[$identifier_column])){
            return false;
        }

        $hash = static::generate_hash($data);
        $data = static::format_csv_data($data);

        unset($data['id']);

        foreach ($data as $key => $value) {
            if(strtoupper(trim($value)) == "NULL"){
                unset($data[$key]);
            }
        }

        $existing = $DB->get_record(static::TABLE, [
            $identifier_column => $data[$identifier_column],
        ], 'id, hash');

        if(!$existing){
            $data['hash'] = $hash;
            $data['instanceid'] = 0;
            $DB->insert_record(static::TABLE, $data);
            return true;
        }

        if($hash != $existing->hash){
            $data['id'] = $existing->id;
            $data['hash'] = $hash;
            $data['needs_update'] = 1;
            $DB->update_record(static::TABLE, $data);
            return true;
        }

        return false;
    }




    public static function get_pending_legacy_upserts(int $limit = 0): Generator {
        global $DB;

        $recordset = $DB->get_recordset_select(
            static::TABLE,
            'instanceid > 0 AND (legacyid = 0 OR needs_update = 1)',
            null,
            'id',
            '*',
            0,
            $limit
        );

        foreach ($recordset as $record) {
            yield new static(0, $record);
        }
        $recordset->close();
    }
}
