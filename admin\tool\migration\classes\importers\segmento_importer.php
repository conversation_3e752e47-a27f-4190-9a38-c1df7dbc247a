<?php namespace tool_migration\importers;

use tool_migration\models\segmento;

require_once($CFG->dirroot . '/user/profile/lib.php');
require_once($CFG->dirroot . '/user/lib.php');

class segmento_importer extends abstract_importer {

    const AUDIENCE_TABLE = 'local_audience_audiences';

    public function import_pending(int $limit = 0) : void {
        foreach (segmento::get_pending_imports($limit) as $segmento) {
            try {
                $this->upsert_audience($segmento);
            } catch (\Throwable $th) {
                $code = $segmento->get('source_codsegmento');
                $this->output("Erro ao importar segmento \"$code\": " .$th->getMessage(), 1);
            }
        }
    }

    public function update_pending(int $limit = 0) : void {
        foreach (segmento::get_pending_updates($limit) as $segmento) {
            try {
                $this->upsert_audience($segmento);
            } catch (\Throwable $th) {
                $code = $segmento->get('source_codsegmento');
                $this->output("Erro ao atualizar segmento \"$code\": " .$th->getMessage(), 1);
            }
        }
    }

    protected function upsert_audience(segmento $segmento){
        global $DB;
        $audience = $segmento->to_audience();

        if(empty($audience->id)){
            $audience->id = $DB->insert_record(self::AUDIENCE_TABLE, $audience);
            $segmento->mark_as_imported($audience->id);
            $this->output("Publico-alvo $audience->name adicionado", 1);
            return;
        }

        $DB->update_record(self::AUDIENCE_TABLE, $audience);
        $segmento->mark_as_updated();
        $this->output("Publico-alvo $audience->name atualizado", 1);
    }
}
