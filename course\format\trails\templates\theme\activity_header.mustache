{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core/activity_header

    Activity header template.

    Context variables required for this template:
    * title - The title of the activity module
    * description - The intro for the module
    * completion - The completion info if available for the the module as acquired via the activity_information method
    * additional_items - Any additional URL select navigation that needs to show up in the header

    Example context (json):
    {
        "title": "Assignment 1",
        "description": "The assignment does something",
        "completion": "<div class='activitycompletion'>Some activity completion criteria</div>",
        "additional_items": {
            "id": "url_select_test",
            "action": "https://example.com/post",
            "formid": "url_select_form",
            "sesskey": "sesskey",
            "label": "core/url_select",
            "helpicon": {
                "title": "Help with something",
                "text": "Help with something",
                "url": "http://example.org/help",
                "linktext": "",
                "icon":{
                    "extraclasses": "iconhelp",
                    "attributes": [
                        {"name": "src", "value": "../../../pix/help.svg"},
                        {"name": "alt", "value": "Help icon"}
                    ]
                }
            },
            "showbutton": "Go",
            "options": [{
                "name": "Group 1", "isgroup": true, "options":
                [
                    {"name": "Item 1", "isgroup": false, "value": "1"},
                    {"name": "Item 2", "isgroup": false, "value": "2"}
                ]},
                {"name": "Group 2", "isgroup": true, "options":
                [
                    {"name": "Item 3", "isgroup": false, "value": "3"},
                    {"name": "Item 4", "isgroup": false, "value": "4"}
                ]}],
            "disabled": false,
            "title": "Some cool title"
        }
    }
}}
<span id="maincontent"></span>
{{#title}}
    <h2>{{{title}}}</h2>
{{/title}}
<div class="activity-header pb-3" data-for="page-activity-header">{{!
    }}{{#completion}}
        <span class="sr-only">{{#str}} overallaggregation, completion {{/str}}</span>
        {{{completion}}}
    {{/completion}}
    {{#description}}
        <div class="activity-description mb-3" id="intro">
			<h5 class='mb-3'>{{#str}}intro, format_trails{{/str}}</h5>
            {{{description}}}
        </div>
    {{/description}}{{!
}}</div>
{{#additional_items}}
    <nav aria-label="{{#str}} additionalcustomnav, core {{/str}}">
        {{> core/url_select}}
    </nav>
{{/additional_items}}
