{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/content/section/controlmenu

    Displays a section control menu (dropdown menu).

    Example context (json):
    {
        "menu": "<a href=\"#\" class=\"d-inline-block dropdown-toggle icon-no-margin\">Edit<b class=\"caret\"></b></a>",
        "hasmenu": true
    }
}}
{{#hasmenu}}
<div class="section_action_menu bulk-hidden ml-auto" data-sectionid="{{id}}">
    {{{menu}}}
</div>
{{/hasmenu}}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1 && node.matches('a.dropdown-item.editing_delete.menu-action')) {
                    node.addEventListener('click', function(e) {
                        const originalEvent = e;
                        setTimeout(function() {
                            window.location.reload();
                        }, 800);
                    });
                }
            });
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});
</script>
