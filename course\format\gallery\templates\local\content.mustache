{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/content

    Displays the complete course format.

    Example context (json):
    {
        "initialsection": {
                "num": 0,
                "id": 34,
                "cmlist": {
                    "cms": [
                        {
                            "cmitem": {
                                "cmformat": {
                                    "cmname": "<a class=\"aalink\" href=\"#\"><span class=\"instancename\">Forum example</span></a>",
                                    "hasname": "true"
                                },
                                "id": 3,
                                "cmid": 3,
                                "module": "forum",
                                "extraclasses": "newmessages",
                                "anchor": "module-3"
                            }
                        }
                    ],
                    "hascms": true
                },
                "iscurrent": true,
                "summary": {
                    "summarytext": "Summary text!"
                }
            },
        "sections": [
            {
                "num": 1,
                "id": 35,
                "header": {
                    "name": "Section title",
                    "url": "#"
                },
                "cmlist": {
                    "cms": [
                        {
                            "cmitem": {
                                "cmformat": {
                                    "cmname": "<a class=\"aalink\" href=\"#\"><span class=\"instancename\">Another forum</span></a>",
                                    "hasname": "true"
                                },
                                "id": 4,
                                "cmid": 4,
                                "module": "forum",
                                "extraclasses": "newmessages",
                                "anchor": "module-4"
                            }
                        }
                    ],
                    "hascms": true
                },
                "iscurrent": true,
                "summary": {
                    "summarytext": "Summary text!"
                }
            },
            {
                "num": 4,
                "id": 36,
                "header": {
                    "name": "Section 2 title",
                    "url": "#"
                },
                "cmlist": {
                    "cms": [
                        {
                            "cmitem": {
                                "cmformat": {
                                    "cmname": "<a class=\"aalink\" href=\"#\"><span class=\"instancename\">Forum example</span></a>",
                                    "hasname": "true"
                                },
                                "id": 5,
                                "cmid": 5,
                                "module": "forum",
                                "extraclasses": "newmessages",
                                "anchor": "module-5"
                            }
                        }
                    ],
                    "hascms": true
                },
                "iscurrent": true,
                "summary": {
                    "summarytext": "Summary text!"
                }
            }
        ],
        "format": "topics",
        "title": "Course title example",
            "hasnavigation": true,
            "sectionnavigation": {
            "hasprevious": true,
            "previousurl": "#",
            "larrow": "&#x25C4;",
            "previousname": "Section 3",
            "hasnext": true,
            "rarrow": "&#x25BA;",
            "nexturl": "#",
            "nextname": "Section 5"
        },
        "sectionselector": {
            "hasprevious": true,
            "previousurl": "#",
            "larrow": "&#x25C4;",
            "previousname": "Section 3",
            "hasnext": true,
            "rarrow": "&#x25BA;",
            "nexturl": "#",
            "nextname": "Section 5",
            "selector": "<select><option>Section 4</option></select>"
        },
        "sectionreturn": 1,
        "singlesection": {
            "num": 5,
            "id": 37,
            "header": {
                "name": "Single Section Example",
                "url": "#"
            },
            "cmlist": {
                "cms": [
                    {
                        "cmitem": {
                            "cmformat": {
                                "cmname": "<a class=\"aalink\" href=\"#\"><span class=\"instancename\">Assign example</span></a>",
                                "hasname": "true"
                            },
                            "id": 6,
                            "cmid": 6,
                            "module": "assign",
                            "extraclasses": "",
                            "anchor": "module-6"
                        }
                    }
                ],
                "hascms": true
            },
            "iscurrent": true,
            "summary": {
                "summarytext": "Summary text!"
            }
        }
    }
}}

<div id="{{uniqid}}-course-format">
	{{#courseinfo}}
		{{> format_gallery/local/course_info}}
	{{/courseinfo}}
	
    <h2 class="accesshide">{{{title}}}</h2>
    {{{completionhelp}}}
	
    <ul class="{{format}} p-0 {{^sections}}d-none{{/sections}}" data-for="course_sectionlist">
        {{^hideinitialsection}}
			{{#initialsection}}
				{{$ core_courseformat/local/content/section }}
					{{> format_gallery/local/content/section }}
				{{/ core_courseformat/local/content/section }}
			{{/initialsection}}
		{{/hideinitialsection}}
		
        {{#sections}}
            {{$ core_courseformat/local/content/section }}
                {{> format_gallery/local/content/section }}
            {{/ core_courseformat/local/content/section }}
        {{/sections}}
    </ul>

    {{#hasnavigation}}
    <div class="single-section m-0">
        {{#sectionnavigation}}
            {{$ core_courseformat/local/content/sectionnavigation }}
                {{> core_courseformat/local/content/sectionnavigation }}
            {{/ core_courseformat/local/content/sectionnavigation }}
        {{/sectionnavigation}}
        <ul class="{{format}} p-0">
        {{#singlesection}}
            {{$ core_courseformat/local/content/section }}
                {{> format_gallery/local/content/section }}
            {{/ core_courseformat/local/content/section }}
        {{/singlesection}}
        </ul>
        {{#sectionselector}}
            {{$ core_courseformat/local/content/sectionselector }}
                {{> core_courseformat/local/content/sectionselector }}
            {{/ core_courseformat/local/content/sectionselector }}
        {{/sectionselector}}
    </div>
    {{/hasnavigation}}
    {{#numsections}}
        {{$ core_courseformat/local/content/addsection}}
            {{> core_courseformat/local/content/addsection}}
        {{/ core_courseformat/local/content/addsection}}
    {{/numsections}}
    {{#bulkedittools}}
        {{$ core_courseformat/local/content/bulkedittools}}
            {{> core_courseformat/local/content/bulkedittools}}
        {{/ core_courseformat/local/content/bulkedittools}}
    {{/bulkedittools}}

    <div class="section-pagination d-flex justify-content-center align-items-center mt-5 mb-4">
        <nav aria-label="{{#str}}pagination, core{{/str}}" class="pagination pagination-centered justify-content-center">
            <ul class="mt-1 pagination">
                <li class="page-item page-item-previous" data-page-number="">
                    <a href="#" class="page-link prev-page">
                        <span aria-hidden="true"><i class="icon fa fa-angle-left m-0 d-flex justify-content-center align-items-center"></i></span>
                        <span class="sr-only">{{#str}}previouspage, moodle{{/str}}</span>
                    </a>
                </li>
                <li class="page-numbers d-flex"></li>
                <li class="page-item page-item-next" data-page-number="">
                    <a href="#" class="page-link next-page">
                        <span aria-hidden="true"><i class="icon fa fa-angle-right m-0 d-flex justify-content-center align-items-center"></i></span>
                        <span class="sr-only">{{#str}}nextpage, moodle{{/str}}</span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
</div>

{{#js}}
require(['jquery', 'format_gallery/local/content', 'local_courseblockapi/api', 'core/notification', 'format_gallery/availability_more'], function($, component, API, Notification, AvailabilityMore) {
    component.init('{{uniqid}}-course-format', {}, {{sectionreturn}}, '{{sectionname}}', '{{pluralsectionname}}');
	
    AvailabilityMore.init();
	
	$(document).on("click", ".btn-show-availability-info", function(e){
		e.preventDefault();
		$(".activity-item .modal-availability-info").removeClass("d-flex").addClass("d-none");
		$(this).closest(".activity-item").find(".modal-availability-info").removeClass("d-none").addClass("d-flex");
	})
	.on("click", ".btn-hide-availability-info", function(e){
		e.preventDefault();
		$(this).closest(".activity-item").find(".modal-availability-info").removeClass("d-flex").addClass("d-none");
	});

	$("#page").on("click", ".toggle-favourite", function(e){
		e.preventDefault();
		let $icon = $(this).find(".theicon");
		let status = !$(this).data("status");
		
		let promise = API.set_favourite({
			courses: [
				{
					component: null, //null means it favourites in system context
					id: {{courseinfo.id}},
					favourite: status,
				},
			],
		});

		promise
			.then((result) => {
                let $this = $(this);
				$(this).data("status", status);
				
				if(status){
					$icon.removeClass("fa-regular").addClass("fa");
					$(this).find(".favtext").addClass("d-none");
					$(this).find(".unfavtext").removeClass("d-none");
                    $(this).find("[aria-live]").text("Favoritado");
				}else{
					$icon.removeClass("fa").addClass("fa-regular");
					$(this).find(".favtext").removeClass("d-none");
					$(this).find(".unfavtext").addClass("d-none");
                    $(this).find("[aria-live]").text("Desfavoritado");
				}

                setTimeout(function(){
                    $this.find("[aria-live]").text("");
                }, 500)
			})
			.fail(Notification.exception);
	});

    function initPagination() {
        const $modules = $('.activity');
        const itemsPerPage = 8;
        const totalPages = Math.ceil($modules.length / itemsPerPage);
        let currentPage = 1;
        let currentSection = null;
        const isEditing = $('body').hasClass('editing');
        const $navigationButtons = $('.page-item-previous, .page-item-next');

        function showPage(page) {
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;

            $modules.hide();
            $modules.slice(start, end).show();

            updatePaginationButtons(page);
            currentPage = page;
        }

        function handleTodosTab() {
            if (currentSection === '' && isEditing) {
                // Na aba todos e em modo de edição, mostra todos os módulos sem paginação
                $modules.show();
                $('.section-pagination').hide();
                $navigationButtons.hide(); // Esconde os botões de navegação
            } else {
                // Em outras abas ou aba todos sem edição, mantém a paginação
                $('.section-pagination').show();
                $navigationButtons.show(); // Mostra os botões de navegação
                showPage(1);
            }
        }

        function updatePaginationButtons(currentPage) {
            const $pageNumbers = $('.page-numbers');
            const $paginationSection = $('.section-pagination');
            $pageNumbers.empty();

            const isMobile = window.innerWidth <= 600;
            const totalItems = $modules.length;

            function createPageItem(page, isActive = false) {
                return $(`<li class="page-item ${isActive ? 'active' : ''}" data-page-number="${page}">
                    <a href="#" class="page-link" ${isActive ? 'aria-current="page"' : ''}>
                        <span aria-hidden="true">${page}</span>
                        <span class="sr-only">Página ${page}</span>
                    </a>
                </li>`).click(function(e) {
                    e.preventDefault();
                    showPage(page);
                });
            }

            function createEllipsisItem() {
                return $(`<li class="page-item disabled">
                    <span class="page-link"><i class="icon fa fa-ellipsis-h m-0"></i></span>
                </li>`);
            }

            if (totalItems <= itemsPerPage) {
                $paginationSection.hide();
                $('.prev-page, .next-page').closest('.page-item').addClass('d-none');
                return;
            }

            $('.prev-page, .next-page').closest('.page-item').removeClass('d-none');

            let pages = [];

            if (isMobile) {
                // MOBILE: Sempre no máximo 5 elementos (sem contar setas)
                if (totalPages <= 5) {
                    for (let i = 1; i <= totalPages; i++) {
                        pages.push(i);
                    }
                } else {
                    pages.push(1); // primeira página

                    if (currentPage <= 3) {
                        // início
                        pages.push(2, 3);
                        pages.push('...');
                        pages.push(totalPages);
                    } else if (currentPage >= totalPages - 2) {
                        // fim
                        pages.push('...');
                        pages.push(totalPages - 2, totalPages - 1);
                        pages.push(totalPages);
                    } else {
                        // meio
                        pages.push('...');
                        pages.push(currentPage);
                        pages.push('...');
                        pages.push(totalPages);
                    }
                }
            } else {
                // DESKTOP: lógica anterior
                if (totalPages <= 7) {
                    for (let i = 1; i <= totalPages; i++) {
                        pages.push(i);
                    }
                } else {
                    const startRange = Math.max(2, currentPage - 3);
                    const endRange = Math.min(totalPages - 1, currentPage + 3);

                    pages.push(1);
                    if (startRange > 2) pages.push('...');
                    for (let i = startRange; i <= endRange; i++) {
                        pages.push(i);
                    }
                    if (endRange < totalPages - 1) pages.push('...');
                    pages.push(totalPages);
                }
            }

            pages.forEach(p => {
                if (p === '...') {
                    $pageNumbers.append(createEllipsisItem());
                } else {
                    $pageNumbers.append(createPageItem(p, p === currentPage));
                }
            });

            $('.prev-page').closest('.page-item').toggleClass('disabled', currentPage === 1);
            $('.next-page').closest('.page-item').toggleClass('disabled', currentPage === totalPages);
            $paginationSection.show();
        }

        $('.prev-page').click(function(e) {
            e.preventDefault();
            if (currentPage > 1) {
                showPage(currentPage - 1);
            }
        });

        $('.next-page').click(function(e) {
            e.preventDefault();
            if (currentPage < totalPages) {
                showPage(currentPage + 1);
            }
        });

        // Adiciona evento de clique nas abas
        $('.nav-tabs .nav-link').click(function(e) {
            e.preventDefault();
            const section = $(this).data('section');
            
            // Remove active de todas as abas
            $('.nav-tabs .nav-link').removeClass('active');
            // Adiciona active na aba clicada
            $(this).addClass('active');
            
            currentSection = section;
            handleTodosTab();
        });

        // Inicializa o estado correto para a aba atual
        currentSection = $('.nav-tabs .nav-link.active').data('section') || '';
        handleTodosTab();
    }

    $(document).ready(function() {
        initPagination();
    });
});
{{/js}}
