<?php namespace tool_migration\importers;

use tool_migration\models\curso;

require_once($CFG->dirroot . '/user/profile/lib.php');
require_once($CFG->dirroot . '/user/lib.php');
require_once("$CFG->dirroot/course/lib.php");

class curso_importer extends abstract_importer {

    protected int $categoryid = 0;

    public function import_pending(int $limit = 0) : void {
        foreach (curso::get_pending_imports($limit) as $curso) {
            try {
                $this->upsert_course($curso);
            } catch (\Throwable $th) {
                $idcurso = $curso->get('source_idcurso');
                $this->output("Erro ao importar curso \"$idcurso\": " .$th->getMessage(), 1);
            }
        }
        $this->output("Fim da importação");
    }

    public function update_pending(int $limit = 0) : void {
        foreach (curso::get_pending_updates($limit) as $curso) {
            try {
                $this->upsert_course($curso);
            } catch (\Throwable $th) {
                $idcurso = $curso->get('source_idcurso');
                $this->output("Erro ao atualizar curso \"$idcurso\": " .$th->getMessage(), 1);
            }
        }
        $this->output("Fim da atualização");
    }

    public function set_category(int $categoryid) : static {
        $this->categoryid = $categoryid;
        return $this;
    }

    public function upsert_course(curso $curso) : object {
        $course = $curso->to_course($this->categoryid);

        if(empty($course->id)){
            $course = create_course($course);
            $curso->mark_as_imported($course->id);
            $this->output("curso $course->idnumber adicionado", 1);
            return $course;
        }

        update_course($course);
        $curso->mark_as_updated();
        $this->output("curso $course->idnumber atualizado", 1);
        return $course;
    }
}
