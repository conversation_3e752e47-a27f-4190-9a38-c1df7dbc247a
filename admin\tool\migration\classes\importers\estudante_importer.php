<?php namespace tool_migration\importers;

use local_ssystem\constants\custom_profile_fields;
use tool_migration\models\estudante;

require_once($CFG->dirroot . '/user/profile/lib.php');
require_once($CFG->dirroot . '/user/lib.php');

class estudante_importer extends abstract_importer {

    public function import_pending(int $limit = 0) : void {
        foreach (estudante::get_pending_imports($limit) as $estudante) {
            try {
                $this->upsert_user($estudante);
            } catch (\Throwable $th) {
                $cpf = $estudante->get('source_cpf');
                $this->output("Erro ao importar usuário \"$cpf\": " .$th->getMessage(), 1);
            }
        }
    }

    public function update_pending(int $limit = 0) : void {
        foreach (estudante::get_pending_updates($limit) as $estudante) {
            try {
                $this->upsert_user($estudante);
            } catch (\Throwable $th) {
                $cpf = $estudante->get('source_cpf');
                $this->output("Erro ao atualizar usuário \"$cpf\": " .$th->getMessage(), 1);
            }
        }
    }

    protected function upsert_user(estudante $estudante){
        $user = $estudante->to_user();

        if(empty($user->id)){
            $user->id = user_create_user($user, false, false);
            $user->password = null;
            profile_save_data($user);
            $estudante->mark_as_imported($user->id);
            $this->output("Usuário $user->username adicionado", 1);
            return;
        }

        profile_save_data($user);
        user_update_user($user, false, true);
        $estudante->mark_as_updated();

        $this->output("Usuário $user->username atualizado", 1);
    }


    /**
     * Undocumented function
     *
     * @param integer $limit
     * @return void
     */
    public function update_pending_empresas(int $limit = 0) : void {
        global $DB;

        $table = estudante::TABLE;

        // Build the SQL to fetch only those 'estudante' rows whose related-company field is empty.
        $sql = "SELECT
                e.instanceid       AS userid,
                e.source_cpf       AS source_cpf,
                e.source_cnpj_lista AS source_cnpj_lista
            FROM {{$table}} e
                JOIN {user_info_field} f ON (f.shortname = :shortname)
                LEFT JOIN {user_info_data} d ON (d.userid = e.instanceid AND d.fieldid = f.id)
            WHERE e.instanceid > 0
                AND e.source_cnpj_lista IS NOT NULL
                AND (d.data IS NULL OR d.data = '')";

        $params = ['shortname' => custom_profile_fields::RELATED_COMPANY_FIELD];
        $estudantes = $DB->get_recordset_sql($sql, $params, 0, $limit);

        $key = 'profile_field_' . custom_profile_fields::RELATED_COMPANY_FIELD;
        foreach ($estudantes as $estudante) {
            try {
                $user = (object)[
                    'id' => $estudante->instanceid,
                    $key => $estudante->source_cnpj_lista
                ];   
                profile_save_data($user);
                $this->output("CNPJs do usuário $estudante->source_cpf adicionados", 1);

            } catch (\Throwable $th) {
                $this->output("Erro ao importar cnpjs do usuário \"$estudante->source_cpf\": " .$th->getMessage(), 1);
            }
        }
        $estudantes->close();
    }
}


/*
-- Teste
SELECT
    e.instanceid       AS userid,
    e.source_cpf       AS source_cpf,
    e.source_cnpj_lista AS source_cnpj_lista,
    d.data
FROM mdl_eadtech_estudantes e
    JOIN mdl_user_info_field f ON (f.shortname = 'pessoa_juridica_relacionada')
    LEFT JOIN mdl_user_info_data d ON (d.userid = e.instanceid AND d.fieldid = f.id)
WHERE e.instanceid > 0
    AND e.source_cnpj_lista IS NOT NULL
    AND (d.data IS NULL OR d.data = '')
*/
