<?php

/**
 * Checks if a given user is a client admin, editor de cursos or coordenador
 *
 * @param integer $userid Defaults to $USER
 * @return bool
 */
function tool_lfxp_is_client(int $userid = 0) : bool {
	global $USER;
    $userid = $userid ?: $USER->id;
	$roles = get_user_roles(\context_system::instance(), $userid, false);
	$shortnames = array_column($roles, 'shortname');
	return array_search('client', $shortnames) !== false ||
	       array_search('editorcursos', $shortnames) !== false ||
	       array_search('coordenador', $shortnames) !== false;
}
