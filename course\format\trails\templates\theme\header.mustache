<header class="header-incourse d-flex justify-content-between align-items-center px-4 py-3 bg-light border-bottom shadow">
	{{#activityinfo}}
		<div class="d-flex align-items-center">
			<div class="activityiconcontainer {{purpose}} courseicon mr-3">
				<img src="{{{icon}}}" class="activityicon {{iconclass}}" alt="{{{modname}}} icon">
			</div>
			<div>
				<h3 class="mb-0">{{activityname}}</h3>
				<p class="mb-0 small text-muted">{{{pluginname}}} {{#workload}}• {{workload}}{{/workload}}</p>
			</div>
		</div>
	{{/activityinfo}}
	
	<div class="d-flex align-items-center">
		<a href="{{courseurl}}" class="btn btn-dark default">
			<span class="d-md-inline d-none"><i class="fa-solid fa-door-open pr-1"></i> {{#str}}exit, contentbank{{/str}}</span>
			<span class="d-md-none"><i class="fa-solid fa-arrow-right-from-bracket fa-rotate-180 mb-2" title="{{#str}}exit, contentbank{{/str}}"></i></span>
		</a>

		{{#activityinfo}}
			<a href="#" class="btn btn-dark default toggle-favourite ml-1" data-status="{{isfavourite}}">
				<i class="fa{{^isfavourite}}-regular{{/isfavourite}} fa-heart pr-1 theicon"></i> 
				<span class="favtext {{#isfavourite}}d-none{{/isfavourite}}">Favoritar</span>
				<span class="unfavtext {{^isfavourite}}d-none{{/isfavourite}}">Desfavoritar</span>
				<span class="sr-only" aria-live="polite"></span>
			</a>
		{{/activityinfo}}
		
		{{#activitynavigation}}
		<a href="{{previous_activity_url}}" 
			class="btn btn-dark default ml-1 {{^previous_activity_url}}disabled{{/previous_activity_url}} {{#previous_activity_has_availability}}has_availability{{/previous_activity_has_availability}}"
			title="Atividade anterior: {{previous_activity_name}}"
			data-id="{{previous_activity_id}}"
			{{#previous_onclick}}onclick="{{{previous_onclick}}}"{{/previous_onclick}}
		>
			<i class="fa-solid fa-angle-left"></i>
		</a>
		
		<a href="{{next_activity_url}}" 
			class="btn btn-dark default {{^next_activity_url}}disabled{{/next_activity_url}} {{#next_activity_has_availability}}has_availability{{/next_activity_has_availability}}"
			title="Próxima Atividade: {{next_activity_name}}"
			data-id="{{next_activity_id}}"
			{{#next_onclick}}onclick="{{{next_onclick}}}"{{/next_onclick}}
		>
			<i class="fa-solid fa-angle-right"></i>
		</a>
		{{/activitynavigation}}
		
		{{#secondarymoremenu}}
			<div class="ml-1">
				{{>theme_smart/course_menu}}
			</div>
		{{/secondarymoremenu}}
	</div>
</header>

{{#js}}
	require(['jquery', 'local_courseblockapi/api'], function($, API){
		$(".header-incourse").on("click", ".has_availability", function(e){
			e.preventDefault();
			let id = $(this).data("id");
			let $modal = $(".courseindex-item[data-id="+id+"] .modal-availability-info").clone(true, true);
			
			$(this).next(".modal-availability-info").remove();

			$modal
				.removeClass("d-none")
				.addClass("d-flex")
				.css("z-index", 100001)
				.insertAfter($(this));
		})
		.on("click", ".btn-hide-availability-info", function(){
			$(this).closest(".modal-availability-info").remove();
		})
		.on("click", ".toggle-favourite", function(e){
			e.preventDefault();
			let $icon = $(this).find(".theicon");
			let status = !$(this).data("status");
			
			let promise = API.set_activity_favourite({
				activities: [
					{
						component: 'mod_{{#activityinfo}}{{modname}}{{/activityinfo}}',
						id: {{#activityinfo}}{{id}}{{/activityinfo}},
						favourite: status,
					},
				],
			});
	
			promise
				.then((result) => {
					let $this = $(this);
					$(this).data("status", status);
					
					if(status){
						$icon.removeClass("fa-regular").addClass("fa");
						$(this).find(".favtext").addClass("d-none");
						$(this).find(".unfavtext").removeClass("d-none");
						$(this).find("[aria-live]").text("Favoritado");
					}else{
						$icon.removeClass("fa").addClass("fa-regular");
						$(this).find(".favtext").removeClass("d-none");
						$(this).find(".unfavtext").addClass("d-none");
						$(this).find("[aria-live]").text("Desfavoritado");
					}
	
					setTimeout(function(){
						$this.find("[aria-live]").text("");
					}, 500)
				})
				.fail(Notification.exception);
		});
	})
{{/js}}