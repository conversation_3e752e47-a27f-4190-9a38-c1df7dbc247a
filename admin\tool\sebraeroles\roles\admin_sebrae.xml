<?xml version="1.0" encoding="UTF-8"?>
<role>
    <shortname>admin_sebrae</shortname>
    <name>Administrador SEBRAE</name>
    <description/>
    <archetype>manager</archetype>
    <contextlevels>
        <level>system</level>
        <level>coursecat</level>
        <level>course</level>
    </contextlevels>
    <allowassign>
        <shortname>manager</shortname>
        <shortname>coursecreator</shortname>
        <shortname>editingteacher</shortname>
        <shortname>teacher</shortname>
        <shortname>student</shortname>
    </allowassign>
    <allowoverride>
        <shortname>manager</shortname>
        <shortname>coursecreator</shortname>
        <shortname>editingteacher</shortname>
        <shortname>teacher</shortname>
        <shortname>student</shortname>
        <shortname>guest</shortname>
        <shortname>user</shortname>
        <shortname>frontpage</shortname>
    </allowoverride>
    <allowswitch>
        <shortname>editingteacher</shortname>
        <shortname>teacher</shortname>
        <shortname>student</shortname>
        <shortname>guest</shortname>
    </allowswitch>
    <allowview>
        <shortname>manager</shortname>
        <shortname>coursecreator</shortname>
        <shortname>editingteacher</shortname>
        <shortname>teacher</shortname>
        <shortname>student</shortname>
        <shortname>guest</shortname>
        <shortname>user</shortname>
        <shortname>frontpage</shortname>
    </allowview>
    <permissions>
        <allow>atto/h5p:addembed</allow>
        <allow>atto/recordrtc:recordaudio</allow>
        <allow>atto/recordrtc:recordvideo</allow>
        <allow>auth/oauth2:managelinkedlogins</allow>
        <allow>auth/oidc:manageconnection</allow>
        <allow>auth/oidc:manageconnectionconnect</allow>
        <allow>auth/oidc:manageconnectiondisconnect</allow>
        <allow>block/accessreview:addinstance</allow>
        <allow>block/accessreview:view</allow>
        <allow>block/activity_modules:addinstance</allow>
        <allow>block/activity_results:addinstance</allow>
        <allow>block/admin_bookmarks:addinstance</allow>
        <allow>block/admin_bookmarks:myaddinstance</allow>
        <allow>block/badges:addinstance</allow>
        <allow>block/badges:myaddinstance</allow>
        <allow>block/blog_menu:addinstance</allow>
        <allow>block/blog_recent:addinstance</allow>
        <allow>block/blog_tags:addinstance</allow>
        <allow>block/calendar_month:addinstance</allow>
        <allow>block/calendar_month:myaddinstance</allow>
        <allow>block/calendar_upcoming:addinstance</allow>
        <allow>block/calendar_upcoming:myaddinstance</allow>
        <allow>block/comments:addinstance</allow>
        <allow>block/comments:myaddinstance</allow>
        <allow>block/completionstatus:addinstance</allow>
        <allow>block/course_list:addinstance</allow>
        <allow>block/course_list:myaddinstance</allow>
        <allow>block/course_summary:addinstance</allow>
        <allow>block/externalhistory:addinstance</allow>
        <allow>block/externalhistory:myaddinstance</allow>
        <allow>block/feedback:addinstance</allow>
        <allow>block/freecourses:addinstance</allow>
        <allow>block/freecourses:myaddinstance</allow>
        <allow>block/freecourses:view</allow>
        <allow>block/globalsearch:addinstance</allow>
        <allow>block/globalsearch:myaddinstance</allow>
        <allow>block/glossary_random:addinstance</allow>
        <allow>block/glossary_random:myaddinstance</allow>
        <allow>block/html:addinstance</allow>
        <allow>block/html:myaddinstance</allow>
        <allow>block/judy:addinstance</allow>
        <allow>block/judy:myaddinstance</allow>
        <allow>block/login:addinstance</allow>
        <allow>block/lp:addinstance</allow>
        <allow>block/lp:myaddinstance</allow>
        <allow>block/mentees:addinstance</allow>
        <allow>block/mentees:myaddinstance</allow>
        <allow>block/mnet_hosts:addinstance</allow>
        <allow>block/mnet_hosts:myaddinstance</allow>
        <allow>block/mycourses:addinstance</allow>
        <allow>block/mycourses:myaddinstance</allow>
        <allow>block/myoverview:myaddinstance</allow>
        <allow>block/myprofile:addinstance</allow>
        <allow>block/myprofile:myaddinstance</allow>
        <allow>block/mytrails:addinstance</allow>
        <allow>block/mytrails:config</allow>
        <allow>block/mytrails:myaddinstance</allow>
        <allow>block/navigation:addinstance</allow>
        <allow>block/navigation:myaddinstance</allow>
        <allow>block/news_items:addinstance</allow>
        <allow>block/news_items:myaddinstance</allow>
        <allow>block/online_users:addinstance</allow>
        <allow>block/online_users:myaddinstance</allow>
        <allow>block/online_users:viewlist</allow>
        <allow>block/popularcourses:addinstance</allow>
        <allow>block/popularcourses:myaddinstance</allow>
        <allow>block/private_files:addinstance</allow>
        <allow>block/private_files:myaddinstance</allow>
        <allow>block/recentlyaccessedcourses:myaddinstance</allow>
        <allow>block/recentlyaccesseditems:myaddinstance</allow>
        <allow>block/recent_activity:addinstance</allow>
        <allow>block/recent_activity:viewaddupdatemodule</allow>
        <allow>block/recent_activity:viewdeletemodule</allow>
        <allow>block/recommendedcourses:addinstance</allow>
        <allow>block/recommendedcourses:myaddinstance</allow>
        <allow>block/rss_client:addinstance</allow>
        <allow>block/rss_client:manageanyfeeds</allow>
        <allow>block/rss_client:manageownfeeds</allow>
        <allow>block/rss_client:myaddinstance</allow>
        <allow>block/search_forums:addinstance</allow>
        <allow>block/section_links:addinstance</allow>
        <allow>block/selfcompletion:addinstance</allow>
        <allow>block/settings:addinstance</allow>
        <allow>block/settings:myaddinstance</allow>
        <allow>block/site_main_menu:addinstance</allow>
        <allow>block/social_activities:addinstance</allow>
        <allow>block/starredcourses:myaddinstance</allow>
        <allow>block/tags:addinstance</allow>
        <allow>block/tags:myaddinstance</allow>
        <allow>block/tag_flickr:addinstance</allow>
        <allow>block/tag_youtube:addinstance</allow>
        <allow>block/timeline:myaddinstance</allow>
        <allow>block/trails:addinstance</allow>
        <allow>block/trails:myaddinstance</allow>
        <allow>block/xp:addinstance</allow>
        <allow>block/xp:earnxp</allow>
        <allow>block/xp:manage</allow>
        <allow>block/xp:myaddinstance</allow>
        <allow>block/xp:view</allow>
        <allow>block/xp:viewlogs</allow>
        <allow>block/xp:viewreport</allow>
        <allow>booktool/exportimscp:export</allow>
        <allow>booktool/importhtml:import</allow>
        <allow>booktool/print:print</allow>
        <allow>contenttype/h5p:access</allow>
        <allow>contenttype/h5p:upload</allow>
        <allow>contenttype/h5p:useeditor</allow>
        <allow>enrol/attributes:config</allow>
        <allow>enrol/category:config</allow>
        <allow>enrol/category:synchronised</allow>
        <allow>enrol/cohort:config</allow>
        <allow>enrol/cohort:unenrol</allow>
        <allow>enrol/database:config</allow>
        <allow>enrol/database:unenrol</allow>
        <allow>enrol/fee:config</allow>
        <allow>enrol/fee:manage</allow>
        <allow>enrol/fee:unenrol</allow>
        <allow>enrol/fee:unenrolself</allow>
        <allow>enrol/flatfile:manage</allow>
        <allow>enrol/flatfile:unenrol</allow>
        <allow>enrol/guest:config</allow>
        <allow>enrol/imsenterprise:config</allow>
        <allow>enrol/joomdle:config</allow>
        <allow>enrol/joomdle:manage</allow>
        <allow>enrol/joomdle:unenrol</allow>
        <allow>enrol/ldap:manage</allow>
        <allow>enrol/lti:config</allow>
        <allow>enrol/lti:unenrol</allow>
        <allow>enrol/manual:config</allow>
        <allow>enrol/manual:enrol</allow>
        <allow>enrol/manual:manage</allow>
        <allow>enrol/manual:unenrol</allow>
        <allow>enrol/manual:unenrolself</allow>
        <allow>enrol/meta:config</allow>
        <allow>enrol/meta:selectaslinked</allow>
        <allow>enrol/meta:unenrol</allow>
        <allow>enrol/mnet:config</allow>
        <allow>enrol/offer_automatic:enrol</allow>
        <allow>enrol/offer_automatic:manage</allow>
        <allow>enrol/offer_automatic:unenrol</allow>
        <allow>enrol/offer_automatic:unenrolself</allow>
        <allow>enrol/offer_manual:enrol</allow>
        <allow>enrol/offer_manual:manage</allow>
        <allow>enrol/offer_manual:unenrol</allow>
        <allow>enrol/offer_manual:unenrolself</allow>
        <allow>enrol/offer_self:enrol</allow>
        <allow>enrol/offer_self:enrolself</allow>
        <allow>enrol/offer_self:manage</allow>
        <allow>enrol/offer_self:unenrol</allow>
        <allow>enrol/offer_self:unenrolself</allow>
        <allow>enrol/paypal:config</allow>
        <allow>enrol/paypal:manage</allow>
        <allow>enrol/paypal:unenrol</allow>
        <allow>enrol/paypal:unenrolself</allow>
        <allow>enrol/self:config</allow>
        <allow>enrol/self:enrolself</allow>
        <allow>enrol/self:holdkey</allow>
        <allow>enrol/self:manage</allow>
        <allow>enrol/self:unenrol</allow>
        <allow>enrol/self:unenrolself</allow>
        <allow>enrol/selfattributes:config</allow>
        <allow>enrol/selfattributes:unenrol</allow>
        <allow>enrol/trail:config</allow>
        <allow>enrol/trail:enrol</allow>
        <allow>enrol/trail:manage</allow>
        <allow>enrol/trail:unenrol</allow>
        <allow>enrol/trail:unenrolself</allow>
        <allow>forumreport/summary:view</allow>
        <allow>forumreport/summary:viewall</allow>
        <allow>gradeexport/ods:publish</allow>
        <allow>gradeexport/ods:view</allow>
        <allow>gradeexport/txt:publish</allow>
        <allow>gradeexport/txt:view</allow>
        <allow>gradeexport/xls:publish</allow>
        <allow>gradeexport/xls:view</allow>
        <allow>gradeexport/xml:publish</allow>
        <allow>gradeexport/xml:view</allow>
        <allow>gradeimport/csv:view</allow>
        <allow>gradeimport/direct:view</allow>
        <allow>gradeimport/xml:publish</allow>
        <allow>gradeimport/xml:view</allow>
        <allow>gradereport/grader:view</allow>
        <allow>gradereport/history:view</allow>
        <allow>gradereport/outcomes:view</allow>
        <allow>gradereport/overview:view</allow>
        <allow>gradereport/singleview:view</allow>
        <allow>gradereport/summary:view</allow>
        <allow>gradereport/user:view</allow>
        <allow>local/audience:manage</allow>
        <allow>local/courseconsentterm:can_ignore_term</allow>
        <allow>local/csp:seenotifications</allow>
        <allow>local/customfields:view</allow>
        <allow>local/gamification:managegoals</allow>
        <allow>local/gamification:manageorders</allow>
        <allow>local/gamification:manageproducts</allow>
        <allow>local/gamification:managesettings</allow>
        <allow>local/hierarchy:manage</allow>
        <allow>local/intelliboard:attendanceadmin</allow>
        <allow>local/intelliboard:browseallcohorts</allow>
        <allow>local/intelliboard:competency</allow>
        <allow>local/intelliboard:instructors</allow>
        <allow>local/intelliboard:manage</allow>
        <allow>local/intelliboard:students</allow>
        <allow>local/intelliboard:view</allow>
        <allow>local/judy:config</allow>
        <allow>local/lfapi:config</allow>
        <allow>local/multithemes:manage</allow>
        <allow>local/notifications:manage</allow>
        <allow>local/o365:manageconnectionlink</allow>
        <allow>local/o365:manageconnectionunlink</allow>
        <allow>local/o365:managegroups</allow>
        <allow>local/o365:teammember</allow>
        <allow>local/o365:teamowner</allow>
        <allow>local/o365:viewgroups</allow>
        <allow>local/offermanager:manage</allow>
        <allow>local/offermanager:manageparticipants</allow>
        <allow>local/offermanager:viewparticipants</allow>
        <allow>local/recertification:manage</allow>
        <allow>local/recertification:resetmycompletion</allow>
        <allow>local/tickets:manage</allow>
        <allow>local/trails:config</allow>
        <allow>local/trails:delete</allow>
        <allow>local/trails:edit</allow>
        <allow>local/trails:enrol</allow>
        <allow>local/trails:view</allow>
        <allow>local/trails:viewhidden</allow>
        <allow>local/user:config</allow>
        <allow>message/airnotifier:managedevice</allow>
        <allow>mod/assign:addinstance</allow>
        <allow>mod/assign:editothersubmission</allow>
        <allow>mod/assign:exportownsubmission</allow>
        <allow>mod/assign:grade</allow>
        <allow>mod/assign:grantextension</allow>
        <allow>mod/assign:manageallocations</allow>
        <allow>mod/assign:managegrades</allow>
        <allow>mod/assign:manageoverrides</allow>
        <allow>mod/assign:receivegradernotifications</allow>
        <allow>mod/assign:releasegrades</allow>
        <allow>mod/assign:revealidentities</allow>
        <allow>mod/assign:reviewgrades</allow>
        <allow>mod/assign:showhiddengrader</allow>
        <allow>mod/assign:submit</allow>
        <allow>mod/assign:view</allow>
        <allow>mod/assign:viewblinddetails</allow>
        <allow>mod/assign:viewgrades</allow>
        <allow>mod/assign:viewownsubmissionsummary</allow>
        <allow>mod/attendance:addinstance</allow>
        <allow>mod/attendance:canbelisted</allow>
        <allow>mod/attendance:changeattendances</allow>
        <allow>mod/attendance:changepreferences</allow>
        <allow>mod/attendance:export</allow>
        <allow>mod/attendance:import</allow>
        <allow>mod/attendance:manageattendances</allow>
        <allow>mod/attendance:managetemporaryusers</allow>
        <allow>mod/attendance:manualautomark</allow>
        <allow>mod/attendance:takeattendances</allow>
        <allow>mod/attendance:view</allow>
        <allow>mod/attendance:viewreports</allow>
        <allow>mod/attendance:viewsummaryreports</allow>
        <allow>mod/attendance:warningemails</allow>
        <allow>mod/bigbluebuttonbn:addinstance</allow>
        <allow>mod/bigbluebuttonbn:addinstancewithmeeting</allow>
        <allow>mod/bigbluebuttonbn:addinstancewithrecording</allow>
        <allow>mod/bigbluebuttonbn:deleterecordings</allow>
        <allow>mod/bigbluebuttonbn:importrecordings</allow>
        <allow>mod/bigbluebuttonbn:join</allow>
        <allow>mod/bigbluebuttonbn:managerecordings</allow>
        <allow>mod/bigbluebuttonbn:protectrecordings</allow>
        <allow>mod/bigbluebuttonbn:publishrecordings</allow>
        <allow>mod/bigbluebuttonbn:unprotectrecordings</allow>
        <allow>mod/bigbluebuttonbn:unpublishrecordings</allow>
        <allow>mod/bigbluebuttonbn:view</allow>
        <allow>mod/bigbluebuttonbn:viewallrecordingformats</allow>
        <allow>mod/book:addinstance</allow>
        <allow>mod/book:edit</allow>
        <allow>mod/book:read</allow>
        <allow>mod/book:viewhiddenchapters</allow>
        <allow>mod/chat:addinstance</allow>
        <allow>mod/chat:chat</allow>
        <allow>mod/chat:deletelog</allow>
        <allow>mod/chat:exportparticipatedsession</allow>
        <allow>mod/chat:exportsession</allow>
        <allow>mod/chat:readlog</allow>
        <allow>mod/chat:view</allow>
        <allow>mod/choice:addinstance</allow>
        <allow>mod/choice:choose</allow>
        <allow>mod/choice:deleteresponses</allow>
        <allow>mod/choice:downloadresponses</allow>
        <allow>mod/choice:readresponses</allow>
        <allow>mod/choice:view</allow>
        <allow>mod/data:addinstance</allow>
        <allow>mod/data:approve</allow>
        <allow>mod/data:comment</allow>
        <allow>mod/data:exportallentries</allow>
        <allow>mod/data:exportentry</allow>
        <allow>mod/data:exportownentry</allow>
        <allow>mod/data:exportuserinfo</allow>
        <allow>mod/data:managecomments</allow>
        <allow>mod/data:manageentries</allow>
        <allow>mod/data:managetemplates</allow>
        <allow>mod/data:manageuserpresets</allow>
        <allow>mod/data:rate</allow>
        <allow>mod/data:view</allow>
        <allow>mod/data:viewallratings</allow>
        <allow>mod/data:viewalluserpresets</allow>
        <allow>mod/data:viewanyrating</allow>
        <allow>mod/data:viewentry</allow>
        <allow>mod/data:viewrating</allow>
        <allow>mod/data:writeentry</allow>
        <allow>mod/feedback:addinstance</allow>
        <allow>mod/feedback:complete</allow>
        <allow>mod/feedback:createprivatetemplate</allow>
        <allow>mod/feedback:createpublictemplate</allow>
        <allow>mod/feedback:deletesubmissions</allow>
        <allow>mod/feedback:deletetemplate</allow>
        <allow>mod/feedback:edititems</allow>
        <allow>mod/feedback:mapcourse</allow>
        <allow>mod/feedback:receivemail</allow>
        <allow>mod/feedback:view</allow>
        <allow>mod/feedback:viewanalysepage</allow>
        <allow>mod/feedback:viewreports</allow>
        <allow>mod/folder:addinstance</allow>
        <allow>mod/folder:managefiles</allow>
        <allow>mod/folder:view</allow>
        <allow>mod/forum:addinstance</allow>
        <allow>mod/forum:addnews</allow>
        <allow>mod/forum:addquestion</allow>
        <allow>mod/forum:allowforcesubscribe</allow>
        <allow>mod/forum:canoverridecutoff</allow>
        <allow>mod/forum:canoverridediscussionlock</allow>
        <allow>mod/forum:canposttomygroups</allow>
        <allow>mod/forum:cantogglefavourite</allow>
        <allow>mod/forum:createattachment</allow>
        <allow>mod/forum:deleteanypost</allow>
        <allow>mod/forum:deleteownpost</allow>
        <allow>mod/forum:editanypost</allow>
        <allow>mod/forum:exportdiscussion</allow>
        <allow>mod/forum:exportforum</allow>
        <allow>mod/forum:exportownpost</allow>
        <allow>mod/forum:exportpost</allow>
        <allow>mod/forum:grade</allow>
        <allow>mod/forum:managesubscriptions</allow>
        <allow>mod/forum:movediscussions</allow>
        <allow>mod/forum:pindiscussions</allow>
        <allow>mod/forum:postprivatereply</allow>
        <allow>mod/forum:postwithoutthrottling</allow>
        <allow>mod/forum:rate</allow>
        <allow>mod/forum:readprivatereplies</allow>
        <allow>mod/forum:replynews</allow>
        <allow>mod/forum:replypost</allow>
        <allow>mod/forum:splitdiscussions</allow>
        <allow>mod/forum:startdiscussion</allow>
        <allow>mod/forum:viewallratings</allow>
        <allow>mod/forum:viewanyrating</allow>
        <allow>mod/forum:viewdiscussion</allow>
        <allow>mod/forum:viewhiddentimedposts</allow>
        <allow>mod/forum:viewqandawithoutposting</allow>
        <allow>mod/forum:viewrating</allow>
        <allow>mod/forum:viewsubscribers</allow>
        <allow>mod/game:addinstance</allow>
        <allow>mod/game:attempt</allow>
        <allow>mod/game:deleteattempts</allow>
        <allow>mod/game:manage</allow>
        <allow>mod/game:manageoverrides</allow>
        <allow>mod/game:reviewmyattempts</allow>
        <allow>mod/game:view</allow>
        <allow>mod/game:viewreports</allow>
        <allow>mod/glossary:addinstance</allow>
        <allow>mod/glossary:approve</allow>
        <allow>mod/glossary:comment</allow>
        <allow>mod/glossary:export</allow>
        <allow>mod/glossary:exportentry</allow>
        <allow>mod/glossary:exportownentry</allow>
        <allow>mod/glossary:import</allow>
        <allow>mod/glossary:managecategories</allow>
        <allow>mod/glossary:managecomments</allow>
        <allow>mod/glossary:manageentries</allow>
        <allow>mod/glossary:rate</allow>
        <allow>mod/glossary:view</allow>
        <allow>mod/glossary:viewallratings</allow>
        <allow>mod/glossary:viewanyrating</allow>
        <allow>mod/glossary:viewrating</allow>
        <allow>mod/glossary:write</allow>
        <allow>mod/googlemeet:addinstance</allow>
        <allow>mod/googlemeet:editrecording</allow>
        <allow>mod/googlemeet:removerecording</allow>
        <allow>mod/googlemeet:syncgoogledrive</allow>
        <allow>mod/googlemeet:view</allow>
        <allow>mod/h5pactivity:addinstance</allow>
        <allow>mod/h5pactivity:reviewattempts</allow>
        <allow>mod/h5pactivity:submit</allow>
        <allow>mod/h5pactivity:view</allow>
        <allow>mod/hvp:addinstance</allow>
        <allow>mod/hvp:contenthubregistration</allow>
        <allow>mod/hvp:emailconfirmsubmission</allow>
        <allow>mod/hvp:emailnotifysubmission</allow>
        <allow>mod/hvp:getcachedassets</allow>
        <allow>mod/hvp:getembedcode</allow>
        <allow>mod/hvp:getexport</allow>
        <allow>mod/hvp:installrecommendedh5plibraries</allow>
        <allow>mod/hvp:manage</allow>
        <allow>mod/hvp:restrictlibraries</allow>
        <allow>mod/hvp:savecontentuserdata</allow>
        <allow>mod/hvp:saveresults</allow>
        <allow>mod/hvp:share</allow>
        <allow>mod/hvp:updatelibraries</allow>
        <allow>mod/hvp:userestrictedlibraries</allow>
        <allow>mod/hvp:view</allow>
        <allow>mod/hvp:viewallresults</allow>
        <allow>mod/hvp:viewresults</allow>
        <allow>mod/imscp:addinstance</allow>
        <allow>mod/imscp:view</allow>
        <allow>mod/label:addinstance</allow>
        <allow>mod/label:view</allow>
        <allow>mod/lesson:addinstance</allow>
        <allow>mod/lesson:edit</allow>
        <allow>mod/lesson:grade</allow>
        <allow>mod/lesson:manage</allow>
        <allow>mod/lesson:manageoverrides</allow>
        <allow>mod/lesson:view</allow>
        <allow>mod/lesson:viewreports</allow>
        <allow>mod/linkedin:addinstance</allow>
        <allow>mod/linkedin:manage</allow>
        <allow>mod/linkedin:view</allow>
        <allow>mod/lti:addcoursetool</allow>
        <allow>mod/lti:addinstance</allow>
        <allow>mod/lti:addmanualinstance</allow>
        <allow>mod/lti:addpreconfiguredinstance</allow>
        <allow>mod/lti:admin</allow>
        <allow>mod/lti:manage</allow>
        <allow>mod/lti:requesttooladd</allow>
        <allow>mod/lti:view</allow>
        <allow>mod/msteams:addinstance</allow>
        <allow>mod/msteams:view</allow>
        <allow>mod/nps:addinstance</allow>
        <allow>mod/nps:complete</allow>
        <allow>mod/nps:createprivatetemplate</allow>
        <allow>mod/nps:createpublictemplate</allow>
        <allow>mod/nps:deletesubmissions</allow>
        <allow>mod/nps:deletetemplate</allow>
        <allow>mod/nps:edititems</allow>
        <allow>mod/nps:mapcourse</allow>
        <allow>mod/nps:receivemail</allow>
        <allow>mod/nps:view</allow>
        <allow>mod/nps:viewanalysepage</allow>
        <allow>mod/nps:viewreports</allow>
        <allow>mod/page:addinstance</allow>
        <allow>mod/page:view</allow>
        <allow>mod/quiz:addinstance</allow>
        <allow>mod/quiz:attempt</allow>
        <allow>mod/quiz:deleteattempts</allow>
        <allow>mod/quiz:emailconfirmsubmission</allow>
        <allow>mod/quiz:emailnotifyattemptgraded</allow>
        <allow>mod/quiz:emailnotifysubmission</allow>
        <allow>mod/quiz:emailwarnoverdue</allow>
        <allow>mod/quiz:grade</allow>
        <allow>mod/quiz:ignoretimelimits</allow>
        <allow>mod/quiz:manage</allow>
        <allow>mod/quiz:manageoverrides</allow>
        <allow>mod/quiz:preview</allow>
        <allow>mod/quiz:regrade</allow>
        <allow>mod/quiz:reopenattempts</allow>
        <allow>mod/quiz:reviewmyattempts</allow>
        <allow>mod/quiz:view</allow>
        <allow>mod/quiz:viewoverrides</allow>
        <allow>mod/quiz:viewreports</allow>
        <allow>mod/resource:addinstance</allow>
        <allow>mod/resource:view</allow>
        <allow>mod/scorm:addinstance</allow>
        <allow>mod/scorm:deleteownresponses</allow>
        <allow>mod/scorm:deleteresponses</allow>
        <allow>mod/scorm:savetrack</allow>
        <allow>mod/scorm:skipview</allow>
        <allow>mod/scorm:viewreport</allow>
        <allow>mod/scorm:viewscores</allow>
        <allow>mod/simplecertificate:addinstance</allow>
        <allow>mod/simplecertificate:manage</allow>
        <allow>mod/simplecertificate:view</allow>
        <allow>mod/smtpdfprotect:addinstance</allow>
        <allow>mod/smtpdfprotect:view</allow>
        <allow>mod/subcourse:addinstance</allow>
        <allow>mod/subcourse:begraded</allow>
        <allow>mod/subcourse:fetchgrades</allow>
        <allow>mod/subcourse:view</allow>
        <allow>mod/supervideo:addinstance</allow>
        <allow>mod/supervideo:view</allow>
        <allow>mod/supervideo:view_report</allow>
        <allow>mod/survey:addinstance</allow>
        <allow>mod/survey:download</allow>
        <allow>mod/survey:participate</allow>
        <allow>mod/survey:readresponses</allow>
        <allow>mod/url:addinstance</allow>
        <allow>mod/url:view</allow>
        <allow>mod/videos:addinstance</allow>
        <allow>mod/videos:submit</allow>
        <allow>mod/videos:view</allow>
        <allow>mod/wiki:addinstance</allow>
        <allow>mod/wiki:createpage</allow>
        <allow>mod/wiki:editcomment</allow>
        <allow>mod/wiki:editpage</allow>
        <allow>mod/wiki:managecomment</allow>
        <allow>mod/wiki:managefiles</allow>
        <allow>mod/wiki:managewiki</allow>
        <allow>mod/wiki:overridelock</allow>
        <allow>mod/wiki:viewcomment</allow>
        <allow>mod/wiki:viewpage</allow>
        <allow>mod/workshop:addinstance</allow>
        <allow>mod/workshop:allocate</allow>
        <allow>mod/workshop:deletesubmissions</allow>
        <allow>mod/workshop:editdimensions</allow>
        <allow>mod/workshop:exportsubmissions</allow>
        <allow>mod/workshop:ignoredeadlines</allow>
        <allow>mod/workshop:manageexamples</allow>
        <allow>mod/workshop:overridegrades</allow>
        <allow>mod/workshop:peerassess</allow>
        <allow>mod/workshop:publishsubmissions</allow>
        <allow>mod/workshop:submit</allow>
        <allow>mod/workshop:switchphase</allow>
        <allow>mod/workshop:view</allow>
        <allow>mod/workshop:viewallassessments</allow>
        <allow>mod/workshop:viewallsubmissions</allow>
        <allow>mod/workshop:viewauthornames</allow>
        <allow>mod/workshop:viewauthorpublished</allow>
        <allow>mod/workshop:viewpublishedsubmissions</allow>
        <allow>mod/workshop:viewreviewernames</allow>
        <allow>mod/zoom:addinstance</allow>
        <allow>mod/zoom:eligiblealternativehost</allow>
        <allow>mod/zoom:refreshsessions</allow>
        <allow>mod/zoom:view</allow>
        <allow>mod/zoom:viewdialin</allow>
        <allow>mod/zoom:viewjoinurl</allow>
        <allow>moodle/analytics:listinsights</allow>
        <allow>moodle/analytics:listowninsights</allow>
        <allow>moodle/analytics:managemodels</allow>
        <allow>moodle/backup:anonymise</allow>
        <allow>moodle/backup:backupactivity</allow>
        <allow>moodle/backup:backupcourse</allow>
        <allow>moodle/backup:backupsection</allow>
        <allow>moodle/backup:backuptargetimport</allow>
        <allow>moodle/backup:configure</allow>
        <allow>moodle/backup:downloadfile</allow>
        <allow>moodle/backup:userinfo</allow>
        <allow>moodle/badges:awardbadge</allow>
        <allow>moodle/badges:configurecriteria</allow>
        <allow>moodle/badges:configuredetails</allow>
        <allow>moodle/badges:configuremessages</allow>
        <allow>moodle/badges:createbadge</allow>
        <allow>moodle/badges:deletebadge</allow>
        <allow>moodle/badges:earnbadge</allow>
        <allow>moodle/badges:manageglobalsettings</allow>
        <allow>moodle/badges:manageownbadges</allow>
        <allow>moodle/badges:revokebadge</allow>
        <allow>moodle/badges:viewawarded</allow>
        <allow>moodle/badges:viewbadges</allow>
        <allow>moodle/badges:viewotherbadges</allow>
        <allow>moodle/block:edit</allow>
        <allow>moodle/block:view</allow>
        <allow>moodle/blog:create</allow>
        <allow>moodle/blog:manageentries</allow>
        <allow>moodle/blog:manageexternal</allow>
        <allow>moodle/blog:search</allow>
        <allow>moodle/blog:view</allow>
        <allow>moodle/blog:viewdrafts</allow>
        <allow>moodle/calendar:manageentries</allow>
        <allow>moodle/calendar:managegroupentries</allow>
        <allow>moodle/calendar:manageownentries</allow>
        <allow>moodle/category:manage</allow>
        <allow>moodle/category:viewcourselist</allow>
        <allow>moodle/category:viewhiddencategories</allow>
        <allow>moodle/cohort:assign</allow>
        <allow>moodle/cohort:configurecustomfields</allow>
        <allow>moodle/cohort:manage</allow>
        <allow>moodle/cohort:view</allow>
        <allow>moodle/comment:delete</allow>
        <allow>moodle/comment:post</allow>
        <allow>moodle/comment:view</allow>
        <allow>moodle/competency:competencygrade</allow>
        <allow>moodle/competency:competencymanage</allow>
        <allow>moodle/competency:competencyview</allow>
        <allow>moodle/competency:coursecompetencyconfigure</allow>
        <allow>moodle/competency:coursecompetencygradable</allow>
        <allow>moodle/competency:coursecompetencymanage</allow>
        <allow>moodle/competency:coursecompetencyview</allow>
        <allow>moodle/competency:evidencedelete</allow>
        <allow>moodle/competency:plancomment</allow>
        <allow>moodle/competency:plancommentown</allow>
        <allow>moodle/competency:planmanage</allow>
        <allow>moodle/competency:planmanagedraft</allow>
        <allow>moodle/competency:planmanageown</allow>
        <allow>moodle/competency:planmanageowndraft</allow>
        <allow>moodle/competency:planrequestreview</allow>
        <allow>moodle/competency:planrequestreviewown</allow>
        <allow>moodle/competency:planreview</allow>
        <allow>moodle/competency:planview</allow>
        <allow>moodle/competency:planviewdraft</allow>
        <allow>moodle/competency:planviewown</allow>
        <allow>moodle/competency:planviewowndraft</allow>
        <allow>moodle/competency:templatemanage</allow>
        <allow>moodle/competency:templateview</allow>
        <allow>moodle/competency:usercompetencycomment</allow>
        <allow>moodle/competency:usercompetencycommentown</allow>
        <allow>moodle/competency:usercompetencyrequestreview</allow>
        <allow>moodle/competency:usercompetencyrequestreviewown</allow>
        <allow>moodle/competency:usercompetencyreview</allow>
        <allow>moodle/competency:usercompetencyview</allow>
        <allow>moodle/competency:userevidencemanage</allow>
        <allow>moodle/competency:userevidencemanageown</allow>
        <allow>moodle/competency:userevidenceview</allow>
        <allow>moodle/contentbank:access</allow>
        <allow>moodle/contentbank:deleteanycontent</allow>
        <allow>moodle/contentbank:deleteowncontent</allow>
        <allow>moodle/contentbank:downloadcontent</allow>
        <allow>moodle/contentbank:manageanycontent</allow>
        <allow>moodle/contentbank:manageowncontent</allow>
        <allow>moodle/contentbank:upload</allow>
        <allow>moodle/contentbank:useeditor</allow>
        <allow>moodle/contentbank:viewunlistedcontent</allow>
        <allow>moodle/course:activityvisibility</allow>
        <allow>moodle/course:bulkmessaging</allow>
        <allow>moodle/course:changecategory</allow>
        <allow>moodle/course:changefullname</allow>
        <allow>moodle/course:changeidnumber</allow>
        <allow>moodle/course:changelockedcustomfields</allow>
        <allow>moodle/course:changeshortname</allow>
        <allow>moodle/course:changesummary</allow>
        <allow>moodle/course:configurecustomfields</allow>
        <allow>moodle/course:configuredownloadcontent</allow>
        <allow>moodle/course:create</allow>
        <allow>moodle/course:creategroupconversations</allow>
        <allow>moodle/course:delete</allow>
        <allow>moodle/course:downloadcoursecontent</allow>
        <allow>moodle/course:enrolconfig</allow>
        <allow>moodle/course:enrolreview</allow>
        <allow>moodle/course:ignoreavailabilityrestrictions</allow>
        <allow>moodle/course:ignorefilesizelimits</allow>
        <allow>moodle/course:isincompletionreports</allow>
        <allow>moodle/course:manageactivities</allow>
        <allow>moodle/course:managefiles</allow>
        <allow>moodle/course:managegroups</allow>
        <allow>moodle/course:managescales</allow>
        <allow>moodle/course:markcomplete</allow>
        <allow>moodle/course:movesections</allow>
        <allow>moodle/course:overridecompletion</allow>
        <allow>moodle/course:recommendactivity</allow>
        <allow>moodle/course:renameroles</allow>
        <allow>moodle/course:request</allow>
        <allow>moodle/course:reset</allow>
        <allow>moodle/course:reviewotherusers</allow>
        <allow>moodle/course:sectionvisibility</allow>
        <allow>moodle/course:setcurrentsection</allow>
        <allow>moodle/course:setforcedlanguage</allow>
        <allow>moodle/course:tag</allow>
        <allow>moodle/course:togglecompletion</allow>
        <allow>moodle/course:update</allow>
        <allow>moodle/course:useremail</allow>
        <allow>moodle/course:view</allow>
        <allow>moodle/course:viewhiddenactivities</allow>
        <allow>moodle/course:viewhiddencourses</allow>
        <allow>moodle/course:viewhiddengroups</allow>
        <allow>moodle/course:viewhiddensections</allow>
        <allow>moodle/course:viewhiddenuserfields</allow>
        <allow>moodle/course:viewparticipants</allow>
        <allow>moodle/course:viewscales</allow>
        <allow>moodle/course:viewsuspendedusers</allow>
        <allow>moodle/course:visibility</allow>
        <allow>moodle/filter:manage</allow>
        <allow>moodle/grade:edit</allow>
        <allow>moodle/grade:export</allow>
        <allow>moodle/grade:hide</allow>
        <allow>moodle/grade:import</allow>
        <allow>moodle/grade:lock</allow>
        <allow>moodle/grade:manage</allow>
        <allow>moodle/grade:managegradingforms</allow>
        <allow>moodle/grade:manageletters</allow>
        <allow>moodle/grade:manageoutcomes</allow>
        <allow>moodle/grade:managesharedforms</allow>
        <allow>moodle/grade:sharegradingforms</allow>
        <allow>moodle/grade:unlock</allow>
        <allow>moodle/grade:view</allow>
        <allow>moodle/grade:viewall</allow>
        <allow>moodle/grade:viewhidden</allow>
        <allow>moodle/h5p:deploy</allow>
        <allow>moodle/h5p:setdisplayoptions</allow>
        <allow>moodle/h5p:updatelibraries</allow>
        <allow>moodle/moodlenet:shareactivity</allow>
        <allow>moodle/my:configsyspages</allow>
        <allow>moodle/my:manageblocks</allow>
        <allow>moodle/notes:manage</allow>
        <allow>moodle/notes:view</allow>
        <allow>moodle/payment:manageaccounts</allow>
        <allow>moodle/payment:viewpayments</allow>
        <allow>moodle/portfolio:export</allow>
        <allow>moodle/question:add</allow>
        <allow>moodle/question:commentall</allow>
        <allow>moodle/question:commentmine</allow>
        <allow>moodle/question:config</allow>
        <allow>moodle/question:editall</allow>
        <allow>moodle/question:editmine</allow>
        <allow>moodle/question:flag</allow>
        <allow>moodle/question:managecategory</allow>
        <allow>moodle/question:moveall</allow>
        <allow>moodle/question:movemine</allow>
        <allow>moodle/question:tagall</allow>
        <allow>moodle/question:tagmine</allow>
        <allow>moodle/question:useall</allow>
        <allow>moodle/question:usemine</allow>
        <allow>moodle/question:viewall</allow>
        <allow>moodle/question:viewmine</allow>
        <allow>moodle/rating:rate</allow>
        <allow>moodle/rating:view</allow>
        <allow>moodle/rating:viewall</allow>
        <allow>moodle/rating:viewany</allow>
        <allow>moodle/reportbuilder:edit</allow>
        <allow>moodle/reportbuilder:editall</allow>
        <allow>moodle/reportbuilder:scheduleviewas</allow>
        <allow>moodle/reportbuilder:view</allow>
        <allow>moodle/restore:configure</allow>
        <allow>moodle/restore:createuser</allow>
        <allow>moodle/restore:restoreactivity</allow>
        <allow>moodle/restore:restorecourse</allow>
        <allow>moodle/restore:restoresection</allow>
        <allow>moodle/restore:restoretargetimport</allow>
        <allow>moodle/restore:rolldates</allow>
        <allow>moodle/restore:uploadfile</allow>
        <allow>moodle/restore:userinfo</allow>
        <allow>moodle/restore:viewautomatedfilearea</allow>
        <allow>moodle/role:assign</allow>
        <allow>moodle/role:manage</allow>
        <allow>moodle/role:override</allow>
        <allow>moodle/role:review</allow>
        <allow>moodle/role:safeoverride</allow>
        <allow>moodle/role:switchroles</allow>
        <allow>moodle/search:query</allow>
        <allow>moodle/site:accessallgroups</allow>
        <allow>moodle/site:approvecourse</allow>
        <allow>moodle/site:config</allow>
        <allow>moodle/site:configview</allow>
        <allow>moodle/site:deleteanymessage</allow>
        <allow>moodle/site:deleteownmessage</allow>
        <allow>moodle/site:doclinks</allow>
        <allow>moodle/site:forcelanguage</allow>
        <allow>moodle/site:maintenanceaccess</allow>
        <allow>moodle/site:manageallmessaging</allow>
        <allow>moodle/site:manageblocks</allow>
        <allow>moodle/site:managecontextlocks</allow>
        <allow>moodle/site:messageanyuser</allow>
        <allow>moodle/site:mnetlogintoremote</allow>
        <allow>moodle/site:readallmessages</allow>
        <allow>moodle/site:senderrormessage</allow>
        <allow>moodle/site:sendmessage</allow>
        <allow>moodle/site:trustcontent</allow>
        <allow>moodle/site:uploadusers</allow>
        <allow>moodle/site:viewanonymousevents</allow>
        <allow>moodle/site:viewfullnames</allow>
        <allow>moodle/site:viewparticipants</allow>
        <allow>moodle/site:viewreports</allow>
        <allow>moodle/site:viewuseridentity</allow>
        <allow>moodle/tag:edit</allow>
        <allow>moodle/tag:editblocks</allow>
        <allow>moodle/tag:flag</allow>
        <allow>moodle/tag:manage</allow>
        <allow>moodle/user:changeownpassword</allow>
        <allow>moodle/user:create</allow>
        <allow>moodle/user:delete</allow>
        <allow>moodle/user:editmessageprofile</allow>
        <allow>moodle/user:editownmessageprofile</allow>
        <allow>moodle/user:editownprofile</allow>
        <allow>moodle/user:editprofile</allow>
        <allow>moodle/user:ignoreuserquota</allow>
        <allow>moodle/user:loginas</allow>
        <allow>moodle/user:manageblocks</allow>
        <allow>moodle/user:manageownblocks</allow>
        <allow>moodle/user:manageownfiles</allow>
        <allow>moodle/user:managesyspages</allow>
        <allow>moodle/user:readuserblogs</allow>
        <allow>moodle/user:readuserposts</allow>
        <allow>moodle/user:update</allow>
        <allow>moodle/user:viewalldetails</allow>
        <allow>moodle/user:viewdetails</allow>
        <allow>moodle/user:viewhiddendetails</allow>
        <allow>moodle/user:viewlastip</allow>
        <allow>moodle/user:viewuseractivitiesreport</allow>
        <allow>moodle/webservice:createmobiletoken</allow>
        <allow>moodle/webservice:createtoken</allow>
        <allow>moodle/webservice:managealltokens</allow>
        <allow>qbank/customfields:changelockedcustomfields</allow>
        <allow>qbank/customfields:configurecustomfields</allow>
        <allow>qbank/customfields:viewhiddencustomfields</allow>
        <allow>quiz/grading:viewidnumber</allow>
        <allow>quiz/grading:viewstudentnames</allow>
        <allow>quiz/statistics:view</allow>
        <allow>quizaccess/seb:bypassseb</allow>
        <allow>quizaccess/seb:managetemplates</allow>
        <allow>quizaccess/seb:manage_filemanager_sebconfigfile</allow>
        <allow>quizaccess/seb:manage_seb_activateurlfiltering</allow>
        <allow>quizaccess/seb:manage_seb_allowedbrowserexamkeys</allow>
        <allow>quizaccess/seb:manage_seb_allowreloadinexam</allow>
        <allow>quizaccess/seb:manage_seb_allowspellchecking</allow>
        <allow>quizaccess/seb:manage_seb_allowuserquitseb</allow>
        <allow>quizaccess/seb:manage_seb_enableaudiocontrol</allow>
        <allow>quizaccess/seb:manage_seb_expressionsallowed</allow>
        <allow>quizaccess/seb:manage_seb_expressionsblocked</allow>
        <allow>quizaccess/seb:manage_seb_filterembeddedcontent</allow>
        <allow>quizaccess/seb:manage_seb_linkquitseb</allow>
        <allow>quizaccess/seb:manage_seb_muteonstartup</allow>
        <allow>quizaccess/seb:manage_seb_quitpassword</allow>
        <allow>quizaccess/seb:manage_seb_regexallowed</allow>
        <allow>quizaccess/seb:manage_seb_regexblocked</allow>
        <allow>quizaccess/seb:manage_seb_requiresafeexambrowser</allow>
        <allow>quizaccess/seb:manage_seb_showkeyboardlayout</allow>
        <allow>quizaccess/seb:manage_seb_showreloadbutton</allow>
        <allow>quizaccess/seb:manage_seb_showsebdownloadlink</allow>
        <allow>quizaccess/seb:manage_seb_showsebtaskbar</allow>
        <allow>quizaccess/seb:manage_seb_showtime</allow>
        <allow>quizaccess/seb:manage_seb_showwificontrol</allow>
        <allow>quizaccess/seb:manage_seb_templateid</allow>
        <allow>quizaccess/seb:manage_seb_userconfirmquit</allow>
        <allow>report/completion:view</allow>
        <allow>report/courseoverview:view</allow>
        <allow>report/dashboard:view</allow>
        <allow>report/gptreport:view</allow>
        <allow>report/log:view</allow>
        <allow>report/log:viewtoday</allow>
        <allow>report/loglive:view</allow>
        <allow>report/outline:view</allow>
        <allow>report/outline:viewuserreport</allow>
        <allow>report/participants:export</allow>
        <allow>report/participants:view</allow>
        <allow>report/participation:view</allow>
        <allow>report/performance:view</allow>
        <allow>report/policyagreement:view</allow>
        <allow>report/progress:view</allow>
        <allow>report/questioninstances:view</allow>
        <allow>report/security:view</allow>
        <allow>report/stats:view</allow>
        <allow>report/status:view</allow>
        <allow>report/usersessions:manageownsessions</allow>
        <allow>repository/areafiles:view</allow>
        <allow>repository/contentbank:accesscoursecategorycontent</allow>
        <allow>repository/contentbank:accesscoursecontent</allow>
        <allow>repository/contentbank:accessgeneralcontent</allow>
        <allow>repository/contentbank:view</allow>
        <allow>repository/coursefiles:view</allow>
        <allow>repository/dropbox:view</allow>
        <allow>repository/equella:view</allow>
        <allow>repository/filesystem:view</allow>
        <allow>repository/flickr:view</allow>
        <allow>repository/flickr_public:view</allow>
        <allow>repository/googledocs:view</allow>
        <allow>repository/local:view</allow>
        <allow>repository/merlot:view</allow>
        <allow>repository/nextcloud:view</allow>
        <allow>repository/onedrive:view</allow>
        <allow>repository/recent:view</allow>
        <allow>repository/s3:view</allow>
        <allow>repository/upload:view</allow>
        <allow>repository/url:view</allow>
        <allow>repository/user:view</allow>
        <allow>repository/webdav:view</allow>
        <allow>repository/wikimedia:view</allow>
        <allow>repository/youtube:view</allow>
        <allow>tiny/h5p:addembed</allow>
        <allow>tiny/recordrtc:recordaudio</allow>
        <allow>tiny/recordrtc:recordvideo</allow>
        <allow>tool/brickfield:viewcoursetools</allow>
        <allow>tool/brickfield:viewsystemtools</allow>
        <allow>tool/changetheme:change</allow>
        <allow>tool/companymanagement:create</allow>
        <allow>tool/companymanagement:delete</allow>
        <allow>tool/companymanagement:update</allow>
        <allow>tool/companymanagement:view</allow>
        <allow>tool/coursemanagement:edit</allow>
        <allow>tool/coursemanagement:view</allow>
        <allow>tool/customlang:edit</allow>
        <allow>tool/customlang:export</allow>
        <allow>tool/customlang:view</allow>
        <allow>tool/custom_forgotpassword:manage</allow>
        <allow>tool/dataprivacy:downloadallrequests</allow>
        <allow>tool/dataprivacy:downloadownrequest</allow>
        <allow>tool/dataprivacy:makedatadeletionrequestsforchildren</allow>
        <allow>tool/dataprivacy:makedatarequestsforchildren</allow>
        <allow>tool/dataprivacy:managedataregistry</allow>
        <allow>tool/dataprivacy:managedatarequests</allow>
        <allow>tool/dataprivacy:requestdelete</allow>
        <allow>tool/dataprivacy:requestdeleteforotheruser</allow>
        <allow>tool/interestareas:manage</allow>
        <allow>tool/lfxp:config</allow>
        <allow>tool/lpmigrate:frameworksmigrate</allow>
        <allow>tool/monitor:managerules</allow>
        <allow>tool/monitor:managetool</allow>
        <allow>tool/monitor:subscribe</allow>
        <allow>tool/policy:accept</allow>
        <allow>tool/policy:acceptbehalf</allow>
        <allow>tool/policy:managedocs</allow>
        <allow>tool/policy:viewacceptances</allow>
        <allow>tool/recyclebin:deleteitems</allow>
        <allow>tool/recyclebin:restoreitems</allow>
        <allow>tool/recyclebin:viewitems</allow>
        <allow>tool/trailmate:use</allow>
        <allow>tool/uploaduser:uploaduserpictures</allow>
        <allow>tool/usercoursestatus:view</allow>
        <allow>tool/usermanagement:edit</allow>
        <allow>tool/usermanagement:view</allow>
        <allow>tool/usertours:managetours</allow>
        <allow>webservice/rest:use</allow>
        <allow>webservice/restful:use</allow>
        <allow>webservice/soap:use</allow>
    </permissions>
</role>
