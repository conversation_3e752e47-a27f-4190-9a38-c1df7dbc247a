<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Contains the default content output class.
 *
 * @package   format_learningflix
 * @copyright 2020 Ferran Recio <<EMAIL>>
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace format_learningflix\output\courseformat;

use context_course;
use core_courseformat\output\local\content as content_base;
use local_courseblockapi\traits\course_trait;
use local_gamification\repositories\reward_repository;
use local_gamification\repositories\setting_repository;
use local_offermanager\persistent\offer_user_enrol_model;
use local_ssystem\constants\custom_course_fields;
use renderer_base;
use stdClass;

/**
 * Base class to render a course content.
 *
 * @package   format_learningflix
 * @copyright 2020 Ferran Recio <<EMAIL>>
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class content extends content_base
{
    use course_trait;

    /**
     * @var bool Season format has add section after each season.
     *
     * The responsible for the buttons is core_courseformat\output\local\content\section.
     */
    protected $hasaddsection = false;

    /**
     * Export this data so it can be used as the context for a mustache template (core/inplace_editable).
     *
     * @param renderer_base $output typically, the renderer that's calling this function
     * @return stdClass data context for a mustache template
     */
    public function export_for_template(renderer_base $output)
    {
        global $PAGE;

        $PAGE->requires->js_call_amd('format_learningflix/mutations', 'init');
        $PAGE->requires->js_call_amd('format_learningflix/section', 'init');

        $data = parent::export_for_template($output);
        $data->hideinitialsection = true;
        $data->courseinfo = $this->course_info($PAGE->course, $data);

        $courseformat = course_get_format($PAGE->course);
        $data->sectionname = mb_strtolower($courseformat->get_custom_section_name());
        $data->pluralsectionname = mb_strtolower($courseformat->get_custom_section_name(true));
        $data->initialsectionid = $this->get_initial_section_id();

        return $data;
    }

    public function course_info($course, $data)
    {
        global $CFG, $OUTPUT, $USER, $DB;

        require_once($CFG->dirroot . '/local/courseblockapi/locallib.php');

        $courseformat = course_get_format($course);
        $courseimages = $this->get_course_image($course, "object");
        $courselogo = $courseimages->logo;
        $courseimage = $courseimages->image;
        $coverposition = $courseformat->get_cover_image_position();

        $progress = offer_user_enrol_model::calculate_course_progress($USER->id, $course);

        $hasprogress = false;

        if ($progress === 0 || $progress > 0) {
            $hasprogress = true;
        }

        $progress = floor($progress ?? 0);
        $coursecategory = \core_course_category::get($course->category, MUST_EXIST, true);

        $startmonth = (new \DateTime("@$course->startdate"))->format('F'); // strftime is deprecated
        $startyear = date('Y', $course->startdate);
        $startdate = ucfirst($startmonth) . " " . $startyear;

        $teachers = $this->get_course_teachers($course->id);
        $customfields = $this->get_customfields_data($course->id);

        $totalsections = count($data->sections);

        $config = (new setting_repository)->get_instance();
        $reward = (new reward_repository)->get_course_reward($course->id);

        if ($config?->get('enable') && $reward) {
            $gamification = [
                'points' => $reward->get_points_named(),
                'coins' => $reward->get('coins')
            ];
        }

        $aboutcourse_data = self::get_aboutcourse_data($course->id);

        $info = (object)[
            'id' => $course->id,
            'fullnamedisplay' => get_course_display_name_for_list($course),
            'viewurl' => (new \moodle_url('/course/view.php', array('id' => $course->id)))->out(false),
            'haslogo' => $courselogo ? true : false,
            'logo' => $courselogo,
            'image' => $courseimage,
            'coverposition' => $coverposition,
            'summary' => $course->summary,
            'startdate' => "", //$startdate,
            'progress' => $progress,
            'hasprogress' => $hasprogress,
            'isfavourite' => local_courseblockapi_is_course_favourite($course->id, 'core_course', $USER->id),
            'hidden' => boolval(get_user_preferences('block_myoverview_hidden_course_' . $course->id, 0)),
            'showshortname' => $CFG->courselistshortnames ? true : false,
            'coursecategory' => $coursecategory->name,
            'totalsections' => $totalsections,
            'sectionname' => $this->format->get_custom_section_name($totalsections > 1),
            'hasteacher' => !empty($teachers) || false,
            'hasmanyteachers' => count($teachers) > 1 || false,
            'teachers' => $teachers,
            'customfields' => $customfields,
            'showcourseprogress' => isset($data->showcourseprogress) ? $data->showcourseprogress : true,
            'gamification' => $gamification ?? null,
            'aboutcourse_data' => $aboutcourse_data,
            'aboutcourse_data_is_empty' => count($aboutcourse_data) === 0
        ];

        return $info;
    }

    /**
     * Return the id of the initial section (section 0) for a course.
     *
     * @return int The id of the initial section.
     */
    protected function get_initial_section_id(): int
    {
        global $DB, $PAGE;

        return $DB->get_field('course_sections', 'id', ['course' => $PAGE->course->id, 'section' => 0]);
    }
}
