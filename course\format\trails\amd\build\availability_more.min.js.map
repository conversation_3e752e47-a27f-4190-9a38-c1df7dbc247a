{"version": 3, "file": "availability_more.min.js", "sources": ["../src/availability_more.js"], "sourcesContent": ["/**\n * Show more action for availablity information.\n *\n * @module     format_trails/availability_more\n * @copyright  2023\n */\n\n/**\n * Availability info selectors.\n */\nconst Selectors = {\n  availabilityinfo:\n    \"#page-course-view-trails #region-main .notifications .alert.alert-danger .availabilityinfo-error ul\",\n  availabilityinfoshowmore:\n    \"#region-main .notifications .alert.alert-danger .availabilityinfo-error ul li.showmore\",\n  dataregion: \"availability-multiple\",\n  dataaction: \"showmore\",\n};\n/**\n * Initialise the eventlister for the showmore action on availability information.\n *\n * @method  init\n */\nexport const init = () => {\n  window.onload = function () {\n    document\n      .querySelectorAll(Selectors.availabilityinfo)\n      .forEach((e) => e.setAttribute(\"data-region\", Selectors.dataregion));\n\n    document\n      .querySelectorAll(Selectors.availabilityinfoshowmore)\n      .forEach((e) => e.setAttribute(\"data-action\", Selectors.dataaction));\n  };\n};\n"], "names": ["Selectors", "window", "onload", "document", "querySelectorAll", "for<PERSON>ach", "e", "setAttribute"], "mappings": "kKAUMA,2BAEF,4GAFEA,mCAIF,yFAJEA,qBAKQ,wBALRA,qBAMQ,yBAOM,KAClBC,OAAOC,OAAS,WACdC,SACGC,iBAAiBJ,4BACjBK,SAASC,GAAMA,EAAEC,aAAa,cAAeP,wBAEhDG,SACGC,iBAAiBJ,oCACjBK,SAASC,GAAMA,EAAEC,aAAa,cAAeP,wBAPlD"}