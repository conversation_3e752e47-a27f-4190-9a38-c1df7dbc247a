<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace format_trails\output\courseformat\state;

use core_courseformat\output\local\state\cm as cm_base;
use core_courseformat\base as course_format;
use completion_info;
use renderer_base;
use section_info;
use cm_info;
use stdClass;
use core_availability\info_module;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/completionlib.php');

/**
 * Contains the ajax update course module structure.
 *
 * @package   core_course
 * @copyright 2021 <PERSON><PERSON><PERSON> <<EMAIL>>
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class cm extends cm_base {
    /**
     * Export this data so it can be used as state object in the course editor.
     *
     * @param renderer_base $output typically, the renderer that's calling this function
     * @return stdClass data context for a mustache template
     */
    public function export_for_template(renderer_base $output): stdClass {
        global $CFG, $USER, $OUTPUT;

        $format = $this->format;
        $section = $this->section;
        $cm = $this->cm;
        $course = $format->get_course();

		$icon = 'monologo';
		// Quick check for monologo icons.
		// Plugins that don't have monologo icons will be displayed as is and CSS filter will not be applied.
		$hasmonologoicons = \core_component::has_monologo_icon('mod', $cm->modname);
		$iconclass = '';
		if (!$hasmonologoicons) {
			$iconclass = 'nofilter';
		}

        $has_restrictions = $this->get_has_restrictions();

        $data = (object)[
            'id' => $cm->id,
            'anchor' => "module-{$cm->id}",
            'name' => \core_external\util::format_string($cm->name, $cm->context, true),
            'visible' => !empty($cm->visible),
            'stealth' => $cm->is_stealth(),
            'sectionid' => $section->id,
            'sectionnumber' => $section->section,
            'uservisible' => $cm->uservisible,
            'hascmrestrictions' => $has_restrictions,
            'modname' => get_string('pluginname', 'mod_' . $cm->modname),
            'indent' => ($format->uses_indentation()) ? $cm->indent : 0,
            'module' => $cm->modname,
            'plugin' => 'mod_' . $cm->modname,
            'purpose' => plugin_supports('mod', $cm->modname, FEATURE_MOD_PURPOSE, MOD_PURPOSE_OTHER),
            'icon' => $OUTPUT->pix_icon($icon, '', $cm->modname, ['class' => "activityicon $iconclass"]),
            'grouping' => $cm->get_grouping_label($this->displayoptions['textclasses']),
            'activityname' => $cm->get_formatted_name(),
            'textclasses' => $this->displayoptions['textclasses'],
            'classlist' => [],
            'cmid' => $cm->id,
            'isadmin' => (tool_lfxp_is_client() || is_siteadmin()) || false,
            'onclick' => htmlspecialchars_decode($cm->onclick, ENT_QUOTES),
        ];

        // Check the user access type to this cm.
        $info = new info_module($cm);
        $data->accessvisible = ($data->visible && $info->is_available_for_all());

        // Add url if the activity is compatible.
        $url = $cm->url;
        if ($url) {
            $data->url = $url->out();
            $data->url_check = $has_restrictions ? "#" : $url->out();
        }

        if ($this->exportcontent) {
            $data->content = $output->course_section_updated_cm_item($format, $section, $cm);
        }

        // Completion status.
        $completioninfo = new completion_info($course);
        $data->istrackeduser = $completioninfo->is_tracked_user($USER->id);
        if ($data->istrackeduser && $completioninfo->is_enabled($cm)) {
            $completiondata = $completioninfo->get_data($cm);
            $data->completionstate = $completiondata->completionstate;
        }

        $data->allowstealth = !empty($CFG->allowstealth) && $format->allow_stealth_module_visibility($cm, $section);

        $data->btn_show_availability_info_class = $has_restrictions ? "btn-show-availability-info" : "";

        // Add partial data segments.
        $haspartials = [];
        $haspartials['availability'] = $this->add_availability_data($data, $output);
        $this->add_format_data($data, $haspartials, $output);

        return $data;
    }

    protected function add_availability_data(stdClass &$data, renderer_base $output): bool {
        $this->availabilityclass = $this->format->get_output_classname('content\\cm\\availability');
        $this->mod = $this->cm;
        
        if (!$this->mod->visible) {
            $data->modavailability = null;
            return false;
        }
        // Mod availability output class.
        $availability = new $this->availabilityclass(
            $this->format,
            $this->section,
            $this->mod,
            []
        );
        $modavailability = $availability->export_for_template($output);
        $data->modavailability = $modavailability;
        return $availability->has_availability($output);
    }

    /**
     * Add activity information to the data structure.
     *
     * @param stdClass $data the current cm data reference
     * @param bool[] $haspartials the result of loading partial data elements
     * @param renderer_base $output typically, the renderer that's calling this function
     * @return bool if the cm has format data
     */
    protected function add_format_data(stdClass &$data, array $haspartials, renderer_base $output): bool {
        $this->mod = $this->cm;
        $result = false;
        // Legacy indentation.
        if (!empty($this->mod->indent) && $this->format->uses_indentation()) {
            $data->indent = $this->mod->indent;
            if ($this->mod->indent > 15) {
                $data->hugeindent = true;
                $result = true;
            }
        }
        // Stealth and hidden from student.
        if (!$this->mod->visible) {
            // This module is hidden but current user has capability to see it.
            $data->modhiddenfromstudents = true;
            $result = true;
        } else if ($this->mod->is_stealth()) {
            // This module is available but is normally not displayed on the course page
            // (this user can see it because they can manage it).
            $data->modstealth = true;
            $result = true;
        }
        // Special inline activity format.
        if (
            $this->mod->has_custom_cmlist_item() &&
            !$haspartials['availability'] &&
            !$haspartials['completion'] &&
            !isset($data->modhiddenfromstudents) &&
            !isset($data->modstealth) &&
            !$this->format->show_editor()
        ) {
            $data->modinline = true;
            $result = true;
        }
        return $result;
    }
}
