<?php

use tool_migration\importers\progress_curso_importer;

define("CLI_SCRIPT", true);
require_once(__DIR__ . '/../../../../../config.php');

raise_memory_limit(MEMORY_HUGE);

$importer = new progress_curso_importer(new text_progress_trace());
$importer->prune_obsolete_completions();
$importer->import_pending_completion();
$importer->update_pending_completion();

/* 

-- Testing completions progresso em curso
SELECT
	epc.id,
	epc.user_enrolment_id = mue.id AS "Enrolment OK?",
	epc.course_completion_id = cc.id AS "Completion OK?"
FROM mdl_eadtech_progresso_cursos epc
	LEFT JOIN mdl_eadtech_cursos mec ON (
		mec.source_codcurso = epc.source_codcurso 
	)
	LEFT JOIN mdl_eadtech_estudantes mee ON (
		mee.source_codaluno = epc.source_codaluno 
	)
	LEFT JOIN mdl_course_completions cc ON (
		cc.course = mec.instanceid 
		AND cc.userid = mee.instanceid 
	)
	LEFT JOIN mdl_enrol me ON (
		mec.instanceid = me.courseid 
		AND me.enrol = 'manual'
		AND me.name = 'LEGADO'
	)
	LEFT JOIN mdl_user_enrolments mue ON (
		mue.enrolid = me.id
		AND mue.userid = mee.instanceid 
	)
WHERE epc.course_completion_id > 0;


-- Garantindo que todo registro aponta para um único enrolment
SELECT
    course_completion_id,
    COUNT(course_completion_id) AS TOTAL
FROM mdl_eadtech_progresso_cursos
WHERE course_completion_id > 0
GROUP BY course_completion_id
HAVING TOTAL > 1


*/