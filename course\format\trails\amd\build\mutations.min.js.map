{"version": 3, "file": "mutations.min.js", "sources": ["../src/mutations.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Format trails mutations.\n *\n * An instance of this class will be used to add custom mutations to the course editor.\n * To make sure the addMutations method find the proper functions, all functions must\n * be declared as class attributes, not a simple methods. The reason is because many\n * plugins can add extra mutations to the course editor.\n *\n * @module     format_trails/mutations\n * @copyright  2022 Fe<PERSON><PERSON> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {getCurrentCourseEditor} from 'core_courseformat/courseeditor';\nimport DefaultMutations from 'core_courseformat/local/courseeditor/mutations';\nimport CourseActions from 'core_courseformat/local/content/actions';\n\nclass TrailsMutations extends DefaultMutations {\n\n    /**\n     * Highlight sections.\n     *\n     * It is important to note this mutation method is declared as a class attribute,\n     * See the class jsdoc for more details on why.\n     *\n     * @param {StateManager} stateManager the current state manager\n     * @param {array} sectionIds the list of section ids\n     */\n    sectionHighlight = async function(stateManager, sectionIds) {\n        const course = stateManager.get('course');\n        this.sectionLock(stateManager, sectionIds, true);\n        const updates = await this._callEditWebservice('section_highlight', course.id, sectionIds);\n        stateManager.processUpdates(updates);\n        this.sectionLock(stateManager, sectionIds, false);\n    };\n\n    /**\n     * Unhighlight sections.\n     *\n     * It is important to note this mutation method is declared as a class attribute,\n     * See the class jsdoc for more details on why.\n     *\n     * @param {StateManager} stateManager the current state manager\n     * @param {array} sectionIds the list of section ids\n     */\n    sectionUnhighlight = async function(stateManager, sectionIds) {\n        const course = stateManager.get('course');\n        this.sectionLock(stateManager, sectionIds, true);\n        const updates = await this._callEditWebservice('section_unhighlight', course.id, sectionIds);\n        stateManager.processUpdates(updates);\n        this.sectionLock(stateManager, sectionIds, false);\n    };\n}\n\nexport const init = () => {\n    const courseEditor = getCurrentCourseEditor();\n    // Some plugin (activity or block) may have their own mutations already registered.\n    // This is why we use addMutations instead of setMutations here.\n    courseEditor.addMutations(new TrailsMutations());\n    // Add direct mutation content actions.\n    CourseActions.addActions({\n        sectionHighlight: 'sectionHighlight',\n        sectionUnhighlight: 'sectionUnhighlight',\n    });\n};\n"], "names": ["TrailsMutations", "DefaultMutations", "async", "stateManager", "sectionIds", "course", "get", "sectionLock", "updates", "this", "_callEditWebservice", "id", "processUpdates", "addMutations", "addActions", "sectionHighlight", "sectionUnhighlight"], "mappings": "ioBAgCMA,yBAAyBC,8FAWRC,eAAeC,aAAcC,kBACtCC,OAASF,aAAaG,IAAI,eAC3BC,YAAYJ,aAAcC,YAAY,SACrCI,cAAgBC,KAAKC,oBAAoB,oBAAqBL,OAAOM,GAAIP,YAC/ED,aAAaS,eAAeJ,cACvBD,YAAYJ,aAAcC,YAAY,iDAY1BF,eAAeC,aAAcC,kBACxCC,OAASF,aAAaG,IAAI,eAC3BC,YAAYJ,aAAcC,YAAY,SACrCI,cAAgBC,KAAKC,oBAAoB,sBAAuBL,OAAOM,GAAIP,YACjFD,aAAaS,eAAeJ,cACvBD,YAAYJ,aAAcC,YAAY,qBAI/B,MACK,0CAGRS,aAAa,IAAIb,mCAEhBc,WAAW,CACrBC,iBAAkB,mBAClBC,mBAAoB"}