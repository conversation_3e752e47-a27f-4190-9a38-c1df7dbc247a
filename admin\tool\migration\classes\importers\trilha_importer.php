<?php namespace tool_migration\importers;

use tool_migration\models\trilha;

require_once($CFG->dirroot . '/user/profile/lib.php');
require_once($CFG->dirroot . '/user/lib.php');
require_once("$CFG->dirroot/course/lib.php");

class trilha_importer extends abstract_importer {

    protected int $categoryid = 0;

    public function import_pending(int $limit = 0) : void {
        foreach (trilha::get_pending_imports($limit) as $trilha) {
            try {
                $this->upsert_course($trilha);
            } catch (\Throwable $th) {
                var_dump($th);
                $idtrilha = $trilha->get('source_id');
                $this->output("Erro ao importar trilha \"$idtrilha\": " .$th->getMessage(), 1);
            }
        }
    }

    public function update_pending(int $limit = 0) : void {
        foreach (trilha::get_pending_updates($limit) as $trilha) {
            try {
                $this->upsert_course($trilha);
            } catch (\Throwable $th) {
                $idtrilha = $trilha->get('source_id');
                $this->output("Erro ao atualizar trilha \"$idtrilha\": " .$th->getMessage(), 1);
            }
        }
    }

    public function set_category(int $categoryid) : static {
        $this->categoryid = $categoryid;
        return $this;
    }

    public function upsert_course(trilha $trilha) : object {
        $course = $trilha->to_course($this->categoryid);

        if(empty($course->id)){
            $course = create_course($course);
            $trilha->mark_as_imported($course->id);
            $this->output("trilha $course->idnumber adicionado", 1);
            return $course;
        }

        update_course($course);
        $trilha->mark_as_updated();
        $this->output("trilha $course->idnumber atualizado", 1);
        return $course;
    }
}
