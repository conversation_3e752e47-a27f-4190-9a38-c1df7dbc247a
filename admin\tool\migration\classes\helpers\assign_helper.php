<?php namespace tool_migration\helpers;

require_once($CFG->dirroot . '/mod/assign/locallib.php');

use assign;
use cm_info;
use context_module;
use context_user;
use tool_migration\helpers\session_helper;

class assign_helper {

    public static function get_assign_from_id(int $id) : ?assign {
        try {
            [$course, $cm] = get_course_and_cm_from_instance($id, 'assign');
            return static::get_assign($cm);
        } catch (\Throwable $th) {
            // debugging($th->getMessage(), DEBUG_DEVELOPER);
            return null;
        }
    }

    public static function get_assign(cm_info $cm) : ?assign {
        $context = context_module::instance($cm->id);
        return new assign($context, $cm, $cm->get_course());
    }

    /**
     * @param assign $assign
     * @param object $user
     * @param array $data
     * @return object|null submission object
     */
    public static function create_submission(assign $assign, object $user, array $data) : ?object {
        global $DB;

        try {
            session_helper::set_user($user);

            $submission_data = [
                'status' => ASSIGN_SUBMISSION_STATUS_SUBMITTED,
                'files_filemanager' => null,
                'onlinetext_editor' => null,
            ];

            if(!empty($data['text'])){
                $submission_data['onlinetext_editor'] = [
                    'itemid' => file_get_unused_draft_itemid(),
                    'text' => $data['text'],
                    'format' => FORMAT_MOODLE,
                ];
            }

            if(!empty($data['status'])){
                $submission_data['status'] = $data['status'];
            }

            if(!empty($data['filepath'])){
                $draft_file = self::create_draft_file_from_path($user->id, $data['filepath']);
                $submission_data['files_filemanager'] = $draft_file->itemid;
            }

            $notices = [];
            $assign->save_submission((object) $submission_data, $notices);

            session_helper::set_original_user();

            // Atualizando datas:
            $submission = $assign->get_user_submission($user->id, false);

            if(!empty($data['timecreated'])){
                $submission->timecreated = intval($data['timecreated']);
            }

            if(!empty($data['timemodified'])){
                $submission->timemodified = intval($data['timemodified']);
            }

            if(!empty($data['timestarted'])){
                $submission->timestarted = intval($data['timestarted']);
            }

            $DB->update_record('assign_submission', $submission);

            return $submission;

        } catch (\Throwable $th) {
            session_helper::set_original_user();
            // debugging($th->getMessage(), DEBUG_DEVELOPER);
            return null;
        }       
    }

    public static function grade_submission(assign $assign, object $teacher, object $student, array $data) : ?object {
        global $DB;

        try {
            session_helper::set_user($teacher);

            $grade_data = [
                'grade' => 0,
                'attemptnumber' => -1,
                'grader' => $teacher->id,
            ];

            if(!empty($data['text'])){
                $grade_data['assignfeedbackcomments_editor'] = [
                    'itemid' => file_get_unused_draft_itemid(),
                    'text' => $data['text'],
                    'format' => FORMAT_MOODLE,
                ];
            }

            if(!empty($data['grade'])){
                $grade_data['grade'] = $data['grade'];
            }

            if(!empty($data['attemptnumber'])){
                $grade_data['attemptnumber'] = $data['attemptnumber'];
            }

            $assign->save_grade($student->id, (object) $grade_data);

            session_helper::set_original_user();

            // Alterando datas
            $grade = $assign->get_user_grade($student->id, false);           

            if(!empty($data['timecreated'])){
                $grade->timecreated = intval($data['timecreated']);
            }

            if(!empty($data['timemodified'])){
                $grade->timemodified = intval($data['timemodified']);
            }

            $grade->grader = $teacher->id;
            $DB->update_record('assign_grades', $grade);

            return $grade;

        } catch (\Throwable $th) {
            session_helper::set_original_user();
            // debugging($th->getMessage(), DEBUG_DEVELOPER);
            return null;
        }    
    }


    public static function create_draft_file_from_path(int $userid, string $filepath) : object {
        $draftid = file_get_unused_draft_itemid();
        $user_context = context_user::instance($userid);

        $file = (object) [
            'contextid' => $user_context->id,
            'component' => 'user',
            'filearea'  => 'draft',
            'itemid'    => $draftid,
            'filepath'  => '/',
            'filename'  => basename($filepath),
        ];

        $fs = get_file_storage();
        $fs->create_file_from_pathname($file, $filepath);
        return $file;
    }
}
