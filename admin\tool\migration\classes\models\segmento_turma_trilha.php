<?php namespace tool_migration\models;

defined('MOODLE_INTERNAL') || die();

class segmento_turma_trilha extends abstract_migratable_entity {
    const TABLE = 'eadtech_seg_turmas_trilha';

    public static function define_identifier(): string {
        throw new \coding_exception("eadtech_seg_turmas_trilha is a relationship table");
    }

    public function get_idnumber(): string {
        return md5('LEGADO-' . $this->get('source_codturma') . '-' . $this->get('source_codsegmento'));
    }

    protected static function define_model_properties(): array {
        return [
            'source_codturma' => [
                'type' => PARAM_INT,
            ],
            'source_codsegmento' => [
                'type' => PARAM_INT,
            ],
            'source_codtrilha' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
        ];
    }

    public function to_legacy(): object {
        $record = (object)[
            'codturma' => $this->get('source_codturma'),
            'codsegmento' => $this->get('source_codsegmento'),
            'codtrilha' => $this->get('source_codtrilha'),
        ];

        if ($id = $this->get('instanceid')) {
            $record->id = $id;
        }

        return $record;
    }
}
