<?php namespace tool_migration\models;

defined('MOODLE_INTERNAL')||die();

require_once($CFG->libdir.'/weblib.php');

use tool_migration\util\ingestion_helper;
use local_ssystem\constants\custom_course_fields;
use local_degreed_integration\degreed\entities\skill;

class trilha extends curso {
    const TABLE = 'eadtech_trilhas';

    public static function define_identifier(): string {
        return 'source_id';
    }
    
    protected static function define_model_properties(): array{
        return [
            'source_id' => ['type' => PARAM_INT],
            'source_disponivel' => ['type' => PARAM_TEXT, 'null' => NULL_ALLOWED, 'default' => null],
            'source_nome' => ['type' => PARAM_TEXT, 'null' => NULL_ALLOWED, 'default' => null],
            'source_status' => ['type' => PARAM_TEXT, 'null' => NULL_ALLOWED, 'default' => null],
            'source_prazo' => ['type' => PARAM_TEXT, 'null' => NULL_ALLOWED, 'default' => null],
            'source_data_de_criacao' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_data_de_modificacao' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_frequencia' => ['type' => PARAM_TEXT, 'null' => NULL_ALLOWED, 'default' => null],
            'source_media' => ['type' => PARAM_TEXT, 'null' => NULL_ALLOWED, 'default' => null],
            'source_permite_rematricula' => ['type' => PARAM_TEXT, 'null' => NULL_ALLOWED, 'default' => null],
            'source_keywords' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_carga_horaria_minima' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_descricao' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_tipo_solucao' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_apresentacao' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_objetivos' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_conteudo_programatico' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_nivel_de_complexidade' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_termo_de_aceite' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_ficha_tecnica' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_requisitos' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_criterios_de_avaliacao' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_publico' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
            'source_area_subarea' => ['type' => PARAM_RAW, 'null' => NULL_ALLOWED, 'default' => null],
        ];
    }

    public function get_idnumber(): string {
        return 'LEGADO-' .  $this->get('source_id');
    }

    protected function get_fullname() : string {
        return $this->get('source_nome') . ' [LEGADO]';
    }

    protected function get_shortname() : string {
        return $this->get_fullname();
    }
    
    public function to_course(int $categoryid): object {
       $course = (object) [
            'category' => $categoryid,
            'sortorder' => 0,
            'fullname' => $this->get_fullname(),
            'shortname' => $this->get_shortname(),
            'idnumber' => $this->get_idnumber(),
            'summary' => $this->get('source_descricao'),
            'summaryformat' => FORMAT_MOODLE,
            'format' => 'trails',
            'showgrades' => 0,
            'newsitems' => 0,
            'startdate' => ingestion_helper::str_to_timestamp($this->get('source_data_de_criacao')),
            'enddate' => 0,
            'relativedatesmode' => 0,
            'marker' => 0,
            'maxbytes' => 0,
            'legacyfiles' => 0,
            'showreports' => 0,
            'visible' => 0,
            'visibleold' => 0,
            'downloadcontent' => null,
            'groupmode' => 0,
            'groupmodeforce' => 0,
            'defaultgroupingid' => 0,
            'lang' => '',
            'calendartype' => '',
            'theme' => '',
            'requested' => 0,
            'enablecompletion' => 1,
            'completionnotify' => 0,
            'originalcourseid' => null,
            'showactivitydates' => 0,
            'showcompletionconditions' => null,
            'pdfexportfont' => null,
            'timecreated' => ingestion_helper::str_to_timestamp($this->get('source_data_de_criacao')),
            'timemodified' => ingestion_helper::str_to_timestamp($this->get('source_data_de_modificacao')),
        ];

        foreach ($this->get_custom_fields() as $key => $value) {
            $key = "customfield_$key";
            $course->$key = $value;
        }

        if($id = $this->get('instanceid')){
            $course->id = $id;
        }

        return $course;
    }


    protected function get_custom_fields() : array {
        $fields = [];

        if($keywords = $this->get('source_keywords')){
            $tags = array_map(function($tag){
                return trim($tag);
            }, explode(',', $keywords));

            $key = skill::CUSTOM_FIELD_SHORTNAME;
            $fields[$key] = $tags;
        }

        $complexidade = ingestion_helper::parse_complexity_level($this->get('source_nivel_de_complexidade'));
        if(!empty($complexidade)){
            $key = custom_course_fields::COMPLEXITY_LEVEL;
            $fields[$key] = static::translate_customfield_select_option($key, $complexidade);
        }

        $formato = ingestion_helper::parse_solution_format($this->get('source_tipo_solucao'));
        if(!empty($formato)){
            $key = custom_course_fields::SOLUTION_FORMAT;
            $fields[$key] = static::translate_customfield_select_option($key, $formato);
        }

        $carga_horaria = ingestion_helper::parse_workload($this->get('source_carga_horaria_minima'));
        if(!empty($carga_horaria)){
            $key = custom_course_fields::WORKLOAD;
            $fields[$key] = $carga_horaria;
        }

        $apresentacao = $this->get('source_apresentacao');
        if(!empty($apresentacao)){
            $key = static::make_customfield_textarea_form_element_name(custom_course_fields::INTRODUCTION);
            $fields[$key] = static::format_customfield_textarea_value($apresentacao);
        }

        $objetivos = $this->get('source_objetivos');
        if(!empty($objetivos)){
            $key = static::make_customfield_textarea_form_element_name(custom_course_fields::OBJECTIVES);
            $fields[$key] = static::format_customfield_textarea_value($objetivos);
        }

        $conteudo_programatico = $this->get('source_conteudo_programatico');
        if(!empty($conteudo_programatico)){
            $key = static::make_customfield_textarea_form_element_name(custom_course_fields::CURRICULUM);
            $fields[$key] = static::format_customfield_textarea_value($conteudo_programatico);
        }

        $termo_de_aceite = $this->get('source_termo_de_aceite');
        if(!empty($termo_de_aceite)){
            $key = static::make_customfield_textarea_form_element_name(custom_course_fields::CONSENT_TERM);
            $fields[$key] = static::format_customfield_textarea_value($termo_de_aceite);
        }

        $ficha_tecnica = $this->get('source_ficha_tecnica');
        if(!empty($ficha_tecnica)){
            $key = static::make_customfield_textarea_form_element_name(custom_course_fields::TECHNICAL_SHEET);
            $fields[$key] = static::format_customfield_textarea_value($ficha_tecnica);
        }

        $requisitos = $this->get('source_requisitos');
        if(!empty($requisitos)){
            $key = static::make_customfield_textarea_form_element_name(custom_course_fields::REQUIREMENTS);
            $fields[$key] = static::format_customfield_textarea_value($requisitos);
        }

        $criterios_de_avaliacao = $this->get('source_criterios_de_avaliacao');
        if(!empty($criterios_de_avaliacao)){
            $key = static::make_customfield_textarea_form_element_name(custom_course_fields::EVALUATION_CRITERIA);
            $fields[$key] = static::format_customfield_textarea_value($criterios_de_avaliacao);
        }

        $publico = $this->get('source_publico');
        if(!empty($publico)){
            $key = static::make_customfield_textarea_form_element_name(custom_course_fields::TARGET_AUDIENCE);
            $fields[$key] = static::format_customfield_textarea_value($publico);
        }

        $area_subarea = $this->get('source_area_subarea');
        if(!empty($area_subarea)){
            $key = static::make_customfield_textarea_form_element_name(custom_course_fields::AREA);
            $fields[$key] = static::format_customfield_textarea_value($area_subarea);
        }

        return $fields;
    }


    public static function upsert_from_csv(array $data) : bool {
        if(!empty($data['source_keywords']) && strlen($data['source_keywords']) > 250){
            unset($data['source_keywords']);
        }

        if(!empty($data['source_data_de_criacao']) && strlen($data['source_data_de_criacao']) > 28){
            $data['source_data_de_criacao'] = substr($data['source_data_de_criacao'], 0, 28);
        }

        if(!empty($data['source_data_de_modificacao']) && strlen($data['source_data_de_modificacao']) > 28){
            $data['source_data_de_modificacao'] = substr($data['source_data_de_modificacao'], 0, 28);
        }

        return parent::upsert_from_csv($data);
    }
}
