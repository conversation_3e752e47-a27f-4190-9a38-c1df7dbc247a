SELECT
    CodUsuarioGestor,
    NomeCompleto,
    UU.EMAIL,
    Login,
    Criado AS DATADECRIACAO,
    Modificado AS DATAMODIFICACAO,
    STATUS,
    TROCARSENHA,
    ACESSARMENSAGENS,
    WEBAULA,
    EHELPDESK,
    ACESSOBIOMETRICO,
    TRAN_UserId,
    CORPODOCENTE
FROM
    USUARIOGESTOR (NOLOCK) AS UG
LEFT JOIN
    USUARIOUNIFICADO (NOLOCK) AS UU
    ON UU.CODESTRANGEIRO = UG.CodUsuarioGestor
    AND UU.TABELACAMPOESTRANGEIRO = 'UsuarioGestor.CodUsuarioGestor';