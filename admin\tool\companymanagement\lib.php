<?php

/**
 * @package    tool
 * @subpackage companymanagement
 * @copyright  2025 Revvo
 */

use local_ssystem\constants\uf;

defined('MOODLE_INTERNAL') || die();

/**
 * Function to define the shared company form
 *
 * @param MoodleQuickForm $mform
 * @param stdClass $company
 */
function tool_companymanagement_companyedit_shared_definition(&$mform, $company)
{
    global $CFG, $USER, $DB;

    $mform->addElement('hidden', 'id');
    $mform->setType('id', PARAM_INT);

    $mform->addElement(
        'text',
        'name',
        get_string('company_name', 'tool_companymanagement'),
        ['minlength' => 2, 'maxlength' => 60]
    );
    $mform->addRule('name', get_string('required'), 'required', null, 'client');
    $mform->addRule('name', get_string('err_minlength', 'form', ['format' => 2]), 'minlength', 2, 'client');
    $mform->addRule('name', get_string('err_maxlength', 'form', ['format' => 60]), 'maxlength', 60, 'client');
    $mform->setType('name', PARAM_RAW);

    $mform->addElement('text', 'cnpj', 'CNPJ', ['maxlength' => 18]);
    $mform->addRule('cnpj', get_string('required'), 'required', null, 'client');
    $mform->setType('cnpj', PARAM_RAW);

    $states = [null => ''] + uf::UFS;
    $mform->addElement('select', 'state', get_string('uf', 'tool_companymanagement'), $states);
    // $mform->addRule('state', get_string('required'), 'required', null, 'client');
    $mform->setType('state', PARAM_RAW);
}

function tool_companymanagement_add_menubar_client_menu()
{
    global $PAGE;

    return [
        "name" => get_string('menubarname', 'tool_companymanagement'),
        "url" => new \moodle_url("/admin/tool/companymanagement/index.php"),
        "active" => strstr($PAGE->url, "admin/tool/companymanagement") ? "active" : "",
    ];
}
