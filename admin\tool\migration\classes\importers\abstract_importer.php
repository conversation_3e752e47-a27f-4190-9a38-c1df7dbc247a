<?php namespace tool_migration\importers;

defined('MOODLE_INTERNAL')||die();

use null_progress_trace;
use progress_trace;

abstract class abstract_importer implements importer_interface {

    protected progress_trace $trace;
    protected static abstract_importer $instance;

    public function __construct(?progress_trace $trace = null){
        $this->trace = $trace ?? new null_progress_trace();
    }
    
    public static function instance() : static {
        if(!isset(static::$instance)){
            static::$instance = new static();
        }

        return static::$instance;
    }

    public function set_progress_trace(progress_trace $trace) : static {
        $this->trace = $trace;
        return $this;
    }

    protected function output(string $message, int $depth = 0){
        $this->trace->output($message, $depth);
    }

    abstract public function import_pending(int $limit = 0) : void;

    abstract public function update_pending(int $limit = 0) : void;
}