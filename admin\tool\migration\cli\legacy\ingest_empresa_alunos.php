<?php

require_once(__DIR__ . '/config.php');

$filepath = "$csv_path/empresa-aluno.csv";
$reader = new tool_migration\importers\readers\csv_reader($filepath);

$counter = 0;
foreach ($reader->read('') as $row) {
    try {
        if(\local_legacy\models\empresa_aluno::upsert_from_csv($row)){
            $counter++;
        }
    } catch (\Throwable $th) {
        mtrace($th->getMessage());
    }
}

print_r("\nIMPORTADOS: $counter");