{"version": 3, "file": "actions.min.js", "sources": ["../../../src/local/content/actions.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Course state actions dispatcher.\n *\n * This module captures all data-dispatch links in the course content and dispatch the proper\n * state mutation, including any confirmation and modal required.\n *\n * @module     core_courseformat/local/content/actions\n * @class      core_courseformat/local/content/actions\n * @copyright  2021 Ferran Recio <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport { BaseComponent } from \"core/reactive\";\nimport ModalFactory from \"core/modal_factory\";\nimport ModalEvents from \"core/modal_events\";\nimport Templates from \"core/templates\";\nimport { prefetchStrings } from \"core/prefetch\";\nimport { get_string as getString } from \"core/str\";\nimport { getFirst } from \"core/normalise\";\nimport { toggleBulkSelectionAction } from \"core_courseformat/local/content/actions/bulkselection\";\nimport * as CourseEvents from \"core_course/events\";\nimport Pending from \"core/pending\";\nimport ContentTree from \"core_courseformat/local/courseeditor/contenttree\";\n// The jQuery module is only used for interacting with Boostrap 4. It can we removed when MDL-71979 is integrated.\nimport jQuery from \"jquery\";\n\n// Load global strings.\nprefetchStrings(\"core\", [\n  \"movecoursesection\",\n  \"movecoursemodule\",\n  \"confirm\",\n  \"delete\",\n]);\n\n// Mutations are dispatched by the course content actions.\n// Formats can use this module addActions static method to add custom actions.\n// Direct mutations can be simple strings (mutation) name or functions.\nconst directMutations = {\n  sectionHide: \"sectionHide\",\n  sectionShow: \"sectionShow\",\n  cmHide: \"cmHide\",\n  cmShow: \"cmShow\",\n  cmStealth: \"cmStealth\",\n  cmMoveRight: \"cmMoveRight\",\n  cmMoveLeft: \"cmMoveLeft\",\n};\n\nexport default class extends BaseComponent {\n  /**\n   * Constructor hook.\n   *\n   * @param {Object} descriptor the component descriptor\n   */\n  create(descriptor) {\n    // Optional component name for debugging.\n    this.name = \"content_actions\";\n    // Default query selectors.\n    this.selectors = {\n      ACTIONLINK: `[data-action]`,\n      // Move modal selectors.\n      SECTIONLINK: `[data-for='section']`,\n      CMLINK: `[data-for='cm']`,\n      SECTIONNODE: `[data-for='sectionnode']`,\n      MODALTOGGLER: `[data-toggle='collapse']`,\n      ADDSECTION: `[data-action='addSection']`,\n      CONTENTTREE: `#destination-selector`,\n      ACTIONMENU: `.action-menu`,\n      ACTIONMENUTOGGLER: `[data-toggle=\"dropdown\"]`,\n      // Availability modal selectors.\n      OPTIONSRADIO: `[type='radio']`,\n    };\n    // Component css classes.\n    this.classes = {\n      DISABLED: `disabled`,\n    };\n\n    this.sectionname = descriptor.sectionname;\n    this.pluralsectionname = descriptor.pluralsectionname;\n  }\n\n  /**\n   * Add extra actions to the module.\n   *\n   * @param {array} actions array of methods to execute\n   */\n  static addActions(actions) {\n    for (const [action, mutationReference] of Object.entries(actions)) {\n      if (\n        typeof mutationReference !== \"function\" &&\n        typeof mutationReference !== \"string\"\n      ) {\n        throw new Error(\n          `${action} action must be a mutation name or a function`\n        );\n      }\n      directMutations[action] = mutationReference;\n    }\n  }\n\n  /**\n   * Initial state ready method.\n   *\n   * @param {Object} state the state data.\n   *\n   */\n  stateReady(state) {\n    // Delegate dispatch clicks.\n    this.addEventListener(this.element, \"click\", this._dispatchClick);\n    // Check section limit.\n    this._checkSectionlist({ state });\n    // Add an Event listener to recalculate limits it if a section HTML is altered.\n    this.addEventListener(this.element, CourseEvents.sectionRefreshed, () =>\n      this._checkSectionlist({ state })\n    );\n  }\n\n  /**\n   * Return the component watchers.\n   *\n   * @returns {Array} of watchers\n   */\n  getWatchers() {\n    return [\n      // Check section limit.\n      { watch: `course.sectionlist:updated`, handler: this._checkSectionlist },\n    ];\n  }\n\n  _dispatchClick(event) {\n    const target = event.target.closest(this.selectors.ACTIONLINK);\n    if (!target) {\n      return;\n    }\n    if (target.classList.contains(this.classes.DISABLED)) {\n      event.preventDefault();\n      return;\n    }\n\n    // Invoke proper method.\n    const actionName = target.dataset.action;\n    const methodName = this._actionMethodName(actionName);\n\n    if (this[methodName] !== undefined) {\n      this[methodName](target, event);\n      return;\n    }\n\n    // Check direct mutations or mutations handlers.\n    if (directMutations[actionName] !== undefined) {\n      if (typeof directMutations[actionName] === \"function\") {\n        directMutations[actionName](target, event);\n        return;\n      }\n      this._requestMutationAction(target, event, directMutations[actionName]);\n      return;\n    }\n  }\n\n  _actionMethodName(name) {\n    const requestName = name.charAt(0).toUpperCase() + name.slice(1);\n    return `_request${requestName}`;\n  }\n\n  /**\n   * Check the section list and disable some options if needed.\n   *\n   * @param {Object} detail the update details.\n   * @param {Object} detail.state the state object.\n   */\n  _checkSectionlist({ state }) {\n    // Disable \"add section\" actions if the course max sections has been exceeded.\n    this._setAddSectionLocked(\n      state.course.sectionlist.length > state.course.maxsections\n    );\n  }\n\n  /**\n   * Return the ids represented by this element.\n   *\n   * Depending on the dataset attributes the action could represent a single id\n   * or a bulk actions with all the current selected ids.\n   *\n   * @param {HTMLElement} target\n   * @returns {Number[]} array of Ids\n   */\n  _getTargetIds(target) {\n    let ids = [];\n    if (target?.dataset?.id) {\n      ids.push(target.dataset.id);\n    }\n    const bulkType = target?.dataset?.bulk;\n    if (!bulkType) {\n      return ids;\n    }\n    const bulk = this.reactive.get(\"bulk\");\n    if (bulk.enabled && bulk.selectedType === bulkType) {\n      ids = [...ids, ...bulk.selection];\n    }\n    return ids;\n  }\n\n  /**\n   * Handle a move section request.\n   *\n   * @param {Element} target the dispatch action element\n   * @param {Event} event the triggered event\n   */\n  async _requestMoveSection(target, event) {\n    // Check we have an id.\n    const sectionIds = this._getTargetIds(target);\n    if (sectionIds.length == 0) {\n      return;\n    }\n\n    event.preventDefault();\n\n    const pendingModalReady = new Pending(\n      `courseformat/actions:prepareMoveSectionModal`\n    );\n\n    // The section edit menu to refocus on end.\n    const editTools = this._getClosestActionMenuToogler(target);\n\n    // Collect section information from the state.\n    const exporter = this.reactive.getExporter();\n    const data = exporter.course(this.reactive.state);\n    let titleText = null;\n\n    // Add the target section id and title.\n    let sectionInfo = null;\n    if (sectionIds.length == 1) {\n      sectionInfo = this.reactive.get(\"section\", sectionIds[0]);\n      data.sectionid = sectionInfo.id;\n      data.sectiontitle = sectionInfo.title;\n      data.information = await this.reactive.getFormatString(\n        \"sectionmove_info\",\n        data.sectiontitle\n      );\n\n      titleText = this.reactive.getFormatString(\n        \"sectionmove_title\",\n        this.sectionname\n      );\n    } else {\n      data.information = await this.reactive.getFormatString(\n        \"sectionsmove_info\",\n        {\n          count: sectionIds.length,\n          sectionname: this.pluralsectionname,\n        }\n      );\n      titleText = this.reactive.getFormatString(\n        \"sectionsmove_title\",\n        this.pluralsectionname\n      );\n    }\n\n    // Build the modal parameters from the event data.\n    const modalParams = {\n      title: titleText,\n      body: Templates.render(\n        \"core_courseformat/local/content/movesection\",\n        data\n      ),\n    };\n\n    // Create the modal.\n    const modal = await this._modalBodyRenderedPromise(modalParams);\n\n    const modalBody = getFirst(modal.getBody());\n\n    // Disable current selected section ids.\n    sectionIds.forEach((sectionId) => {\n      const currentElement = modalBody.querySelector(\n        `${this.selectors.SECTIONLINK}[data-id='${sectionId}']`\n      );\n      this._disableLink(currentElement);\n    });\n\n    // Setup keyboard navigation.\n    new ContentTree(\n      modalBody.querySelector(this.selectors.CONTENTTREE),\n      {\n        SECTION: this.selectors.SECTIONNODE,\n        TOGGLER: this.selectors.MODALTOGGLER,\n        COLLAPSE: this.selectors.MODALTOGGLER,\n      },\n      true\n    );\n\n    // Capture click.\n    modalBody.addEventListener(\"click\", (event) => {\n      const target = event.target;\n      if (\n        !target.matches(\"a\") ||\n        target.dataset.for != \"section\" ||\n        target.dataset.id === undefined\n      ) {\n        return;\n      }\n      if (target.getAttribute(\"aria-disabled\")) {\n        return;\n      }\n      event.preventDefault();\n      this.reactive.dispatch(\"sectionMoveAfter\", sectionIds, target.dataset.id);\n      this._destroyModal(modal, editTools);\n    });\n\n    pendingModalReady.resolve();\n  }\n\n  /**\n   * Handle a move cm request.\n   *\n   * @param {Element} target the dispatch action element\n   * @param {Event} event the triggered event\n   */\n  async _requestMoveCm(target, event) {\n    // Check we have an id.\n    const cmIds = this._getTargetIds(target);\n    if (cmIds.length == 0) {\n      return;\n    }\n\n    event.preventDefault();\n\n    const pendingModalReady = new Pending(\n      `courseformat/actions:prepareMoveCmModal`\n    );\n\n    // The section edit menu to refocus on end.\n    const editTools = this._getClosestActionMenuToogler(target);\n\n    // Collect information from the state.\n    const exporter = this.reactive.getExporter();\n    const data = exporter.course(this.reactive.state);\n\n    let titleText = null;\n    if (cmIds.length == 1) {\n      const cmInfo = this.reactive.get(\"cm\", cmIds[0]);\n      data.cmid = cmInfo.id;\n      data.cmname = cmInfo.name;\n      data.information = await this.reactive.getFormatString(\n        \"cmmove_info\",\n        data.cmname\n      );\n      titleText = this.reactive.getFormatString(\"cmmove_title\");\n    } else {\n      data.information = await this.reactive.getFormatString(\n        \"cmsmove_info\",\n        cmIds.length\n      );\n      titleText = this.reactive.getFormatString(\"cmsmove_title\");\n    }\n\n    // Build the modal parameters from the event data.\n    const modalParams = {\n      title: titleText,\n      body: Templates.render(\"core_courseformat/local/content/movecm\", data),\n    };\n\n    // Create the modal.\n    const modal = await this._modalBodyRenderedPromise(modalParams);\n\n    const modalBody = getFirst(modal.getBody());\n\n    // Disable current selected section ids.\n    cmIds.forEach((cmId) => {\n      const currentElement = modalBody.querySelector(\n        `${this.selectors.CMLINK}[data-id='${cmId}']`\n      );\n      this._disableLink(currentElement);\n    });\n\n    // Setup keyboard navigation.\n    new ContentTree(modalBody.querySelector(this.selectors.CONTENTTREE), {\n      SECTION: this.selectors.SECTIONNODE,\n      TOGGLER: this.selectors.MODALTOGGLER,\n      COLLAPSE: this.selectors.MODALTOGGLER,\n      ENTER: this.selectors.SECTIONLINK,\n    });\n\n    // Open the cm section node if possible (Bootstrap 4 uses jQuery to interact with collapsibles).\n    // All jQuery in this code can be replaced when MDL-71979 is integrated.\n    cmIds.forEach((cmId) => {\n      const currentElement = modalBody.querySelector(\n        `${this.selectors.CMLINK}[data-id='${cmId}']`\n      );\n      const sectionnode = currentElement.closest(this.selectors.SECTIONNODE);\n      const toggler = jQuery(sectionnode).find(this.selectors.MODALTOGGLER);\n      let collapsibleId = toggler.data(\"target\") ?? toggler.attr(\"href\");\n      if (collapsibleId) {\n        // We cannot be sure we have # in the id element name.\n        collapsibleId = collapsibleId.replace(\"#\", \"\");\n        const expandNode = modalBody.querySelector(`#${collapsibleId}`);\n        jQuery(expandNode).collapse(\"show\");\n      }\n    });\n\n    modalBody.addEventListener(\"click\", (event) => {\n      const target = event.target;\n      if (\n        !target.matches(\"a\") ||\n        target.dataset.for === undefined ||\n        target.dataset.id === undefined\n      ) {\n        return;\n      }\n      if (target.getAttribute(\"aria-disabled\")) {\n        return;\n      }\n      event.preventDefault();\n\n      let targetSectionId;\n      let targetCmId;\n      if (target.dataset.for == \"cm\") {\n        const dropData = exporter.cmDraggableData(\n          this.reactive.state,\n          target.dataset.id\n        );\n        targetSectionId = dropData.sectionid;\n        targetCmId = dropData.nextcmid;\n      } else {\n        const section = this.reactive.get(\"section\", target.dataset.id);\n        targetSectionId = target.dataset.id;\n        targetCmId = section?.cmlist[0];\n      }\n      this.reactive.dispatch(\"cmMove\", cmIds, targetSectionId, targetCmId);\n      this._destroyModal(modal, editTools);\n    });\n\n    pendingModalReady.resolve();\n  }\n\n  /**\n   * Handle a create section request.\n   *\n   * @param {Element} target the dispatch action element\n   * @param {Event} event the triggered event\n   */\n  async _requestAddSection(target, event) {\n    event.preventDefault();\n    this.reactive.dispatch(\"addSection\", target.dataset.id ?? 0);\n  }\n\n  /**\n   * Handle a delete section request.\n   *\n   * @param {Element} target the dispatch action element\n   * @param {Event} event the triggered event\n   */\n  async _requestDeleteSection(target, event) {\n    const sectionIds = this._getTargetIds(target);\n    if (sectionIds.length == 0) {\n      return;\n    }\n\n    event.preventDefault();\n\n    // We don't need confirmation to delete empty sections.\n    let needsConfirmation = sectionIds.some((sectionId) => {\n      const sectionInfo = this.reactive.get(\"section\", sectionId);\n      const cmList = sectionInfo.cmlist ?? [];\n      return cmList.length || sectionInfo.hassummary || sectionInfo.rawtitle;\n    });\n    if (!needsConfirmation) {\n      this.reactive.dispatch(\"sectionDelete\", sectionIds);\n      return;\n    }\n\n    let bodyText = null;\n    let titleText = null;\n    if (sectionIds.length == 1) {\n      titleText = this.reactive.getFormatString(\n        \"sectiondelete_title\",\n        this.sectionname\n      );\n      const sectionInfo = this.reactive.get(\"section\", sectionIds[0]);\n      bodyText = this.reactive.getFormatString(\"sectiondelete_info\", {\n        name: sectionInfo.title,\n        sectionname: this.sectionname,\n      });\n    } else {\n      titleText = this.reactive.getFormatString(\n        \"sectionsdelete_title\",\n        this.pluralsectionname\n      );\n      bodyText = this.reactive.getFormatString(\"sectionsdelete_info\", {\n        count: sectionIds.length,\n        sectionname: this.pluralsectionname,\n      });\n    }\n\n    const modalParams = {\n      title: titleText,\n      body: bodyText,\n      type: ModalFactory.types.DELETE_CANCEL,\n    };\n\n    const modal = await this._modalBodyRenderedPromise(modalParams);\n\n    modal.getRoot().on(ModalEvents.delete, (e) => {\n      // Stop the default save button behaviour which is to close the modal.\n      e.preventDefault();\n      modal.destroy();\n      this.reactive.dispatch(\"sectionDelete\", sectionIds);\n    });\n  }\n\n  /**\n   * Handle a toggle cm selection.\n   *\n   * @param {Element} target the dispatch action element\n   * @param {Event} event the triggered event\n   */\n  async _requestToggleSelectionCm(target, event) {\n    toggleBulkSelectionAction(this.reactive, target, event, \"cm\");\n  }\n\n  /**\n   * Handle a toggle section selection.\n   *\n   * @param {Element} target the dispatch action element\n   * @param {Event} event the triggered event\n   */\n  async _requestToggleSelectionSection(target, event) {\n    toggleBulkSelectionAction(this.reactive, target, event, \"section\");\n  }\n\n  /**\n   * Basic mutation action helper.\n   *\n   * @param {Element} target the dispatch action element\n   * @param {Event} event the triggered event\n   * @param {string} mutationName the mutation name\n   */\n  async _requestMutationAction(target, event, mutationName) {\n    if (!target.dataset.id) {\n      return;\n    }\n    event.preventDefault();\n    this.reactive.dispatch(mutationName, [target.dataset.id]);\n  }\n\n  /**\n   * Handle a course module duplicate request.\n   *\n   * @param {Element} target the dispatch action element\n   * @param {Event} event the triggered event\n   */\n  async _requestCmDuplicate(target, event) {\n    const cmIds = this._getTargetIds(target);\n    if (cmIds.length == 0) {\n      return;\n    }\n    const sectionId = target.dataset.sectionid ?? null;\n    event.preventDefault();\n    this.reactive.dispatch(\"cmDuplicate\", cmIds, sectionId);\n  }\n\n  /**\n   * Handle a delete cm request.\n   *\n   * @param {Element} target the dispatch action element\n   * @param {Event} event the triggered event\n   */\n  async _requestCmDelete(target, event) {\n    const cmIds = this._getTargetIds(target);\n    if (cmIds.length == 0) {\n      return;\n    }\n\n    event.preventDefault();\n\n    let bodyText = null;\n    let titleText = null;\n    if (cmIds.length == 1) {\n      const cmInfo = this.reactive.get(\"cm\", cmIds[0]);\n      titleText = getString(\"cmdelete_title\", \"core_courseformat\");\n      bodyText = getString(\"cmdelete_info\", \"core_courseformat\", {\n        type: cmInfo.modname,\n        name: cmInfo.name,\n      });\n    } else {\n      titleText = getString(\"cmsdelete_title\", \"core_courseformat\");\n      bodyText = getString(\"cmsdelete_info\", \"core_courseformat\", {\n        count: cmIds.length,\n      });\n    }\n\n    const modalParams = {\n      title: titleText,\n      body: bodyText,\n      type: ModalFactory.types.DELETE_CANCEL,\n    };\n\n    const modal = await this._modalBodyRenderedPromise(modalParams);\n\n    modal.getRoot().on(ModalEvents.delete, (e) => {\n      // Stop the default save button behaviour which is to close the modal.\n      e.preventDefault();\n      modal.destroy();\n      this.reactive.dispatch(\"cmDelete\", cmIds);\n    });\n  }\n\n  /**\n   * Handle a cm availability change request.\n   *\n   * @param {Element} target the dispatch action element\n   */\n  async _requestCmAvailability(target) {\n    const cmIds = this._getTargetIds(target);\n    if (cmIds.length == 0) {\n      return;\n    }\n    // Show the availability modal to decide which action to trigger.\n    const exporter = this.reactive.getExporter();\n    const data = {\n      allowstealth: exporter.canUseStealth(this.reactive.state, cmIds),\n    };\n    const modalParams = {\n      title: getString(\"availability\", \"core\"),\n      body: Templates.render(\n        \"core_courseformat/local/content/cm/availabilitymodal\",\n        data\n      ),\n      saveButtonText: getString(\"apply\", \"core\"),\n      type: ModalFactory.types.SAVE_CANCEL,\n    };\n    const modal = await this._modalBodyRenderedPromise(modalParams);\n\n    this._setupMutationRadioButtonModal(modal, cmIds);\n  }\n\n  /**\n   * Handle a section availability change request.\n   *\n   * @param {Element} target the dispatch action element\n   */\n  async _requestSectionAvailability(target) {\n    const sectionIds = this._getTargetIds(target);\n    if (sectionIds.length == 0) {\n      return;\n    }\n    const title =\n      sectionIds.length == 1\n        ? \"sectionavailability_title\"\n        : \"sectionsavailability_title\";\n\n    const sectionname =\n      sectionIds.length == 1 ? this.sectionname : this.pluralsectionname;\n\n    // Show the availability modal to decide which action to trigger.\n    const modalParams = {\n      title: this.reactive.getFormatString(title, sectionname),\n      body: Templates.render(\n        \"core_courseformat/local/content/section/availabilitymodal\",\n        []\n      ),\n      saveButtonText: getString(\"apply\", \"core\"),\n      type: ModalFactory.types.SAVE_CANCEL,\n    };\n    const modal = await this._modalBodyRenderedPromise(modalParams);\n\n    this._setupMutationRadioButtonModal(modal, sectionIds);\n  }\n\n  /**\n   * Add events to a mutation selector radio buttons modal.\n   * @param {Modal} modal\n   * @param {Number[]} ids the section or cm ids to apply the mutation\n   */\n  _setupMutationRadioButtonModal(modal, ids) {\n    // The save button is not enabled until the user selects an option.\n    modal.setButtonDisabled(\"save\", true);\n\n    const submitFunction = (radio) => {\n      const mutation = radio?.value;\n      if (!mutation) {\n        return false;\n      }\n      this.reactive.dispatch(mutation, ids);\n      return true;\n    };\n\n    const modalBody = getFirst(modal.getBody());\n    const radioOptions = modalBody.querySelectorAll(\n      this.selectors.OPTIONSRADIO\n    );\n    radioOptions.forEach((radio) => {\n      radio.addEventListener(\"change\", () => {\n        modal.setButtonDisabled(\"save\", false);\n      });\n      radio.parentNode.addEventListener(\"click\", () => {\n        radio.checked = true;\n        modal.setButtonDisabled(\"save\", false);\n      });\n      radio.parentNode.addEventListener(\"dblclick\", (dbClickEvent) => {\n        if (submitFunction(radio)) {\n          dbClickEvent.preventDefault();\n          modal.destroy();\n        }\n      });\n    });\n\n    modal.getRoot().on(ModalEvents.save, () => {\n      const radio = modalBody.querySelector(\n        `${this.selectors.OPTIONSRADIO}:checked`\n      );\n      submitFunction(radio);\n    });\n  }\n\n  /**\n   * Disable all add sections actions.\n   *\n   * @param {boolean} locked the new locked value.\n   */\n  _setAddSectionLocked(locked) {\n    const targets = this.getElements(this.selectors.ADDSECTION);\n    targets.forEach((element) => {\n      element.classList.toggle(this.classes.DISABLED, locked);\n      this.setElementLocked(element, locked);\n    });\n  }\n\n  /**\n   * Replace an element with a copy with a different tag name.\n   *\n   * @param {Element} element the original element\n   */\n  _disableLink(element) {\n    if (element) {\n      element.style.pointerEvents = \"none\";\n      element.style.userSelect = \"none\";\n      element.classList.add(this.classes.DISABLED);\n      element.setAttribute(\"aria-disabled\", true);\n      element.addEventListener(\"click\", (event) => event.preventDefault());\n    }\n  }\n\n  /**\n   * Render a modal and return a body ready promise.\n   *\n   * @param {object} modalParams the modal params\n   * @return {Promise} the modal body ready promise\n   */\n  _modalBodyRenderedPromise(modalParams) {\n    return new Promise((resolve, reject) => {\n      ModalFactory.create(modalParams)\n        .then((modal) => {\n          modal.setRemoveOnClose(true);\n          // Handle body loading event.\n          modal.getRoot().on(ModalEvents.bodyRendered, () => {\n            resolve(modal);\n          });\n          // Configure some extra modal params.\n          if (modalParams.saveButtonText !== undefined) {\n            modal.setSaveButtonText(modalParams.saveButtonText);\n          }\n          if (modalParams.deleteButtonText !== undefined) {\n            modal.setDeleteButtonText(modalParams.saveButtonText);\n          }\n          modal.show();\n          return;\n        })\n        .catch(() => {\n          reject(`Cannot load modal content`);\n        });\n    });\n  }\n\n  /**\n   * Hide and later destroy a modal.\n   *\n   * Behat will fail if we remove the modal while some boostrap collapse is executing.\n   *\n   * @param {Modal} modal\n   * @param {HTMLElement} element the dom element to focus on.\n   */\n  _destroyModal(modal, element) {\n    modal.hide();\n    const pendingDestroy = new Pending(`courseformat/actions:destroyModal`);\n    if (element) {\n      element.focus();\n    }\n    setTimeout(() => {\n      modal.destroy();\n      pendingDestroy.resolve();\n    }, 500);\n  }\n\n  /**\n   * Get the closest actions menu toggler to an action element.\n   *\n   * @param {HTMLElement} element the action link element\n   * @returns {HTMLElement|undefined}\n   */\n  _getClosestActionMenuToogler(element) {\n    const actionMenu = element.closest(this.selectors.ACTIONMENU);\n    if (!actionMenu) {\n      return undefined;\n    }\n    return actionMenu.querySelector(this.selectors.ACTIONMENUTOGGLER);\n  }\n}\n"], "names": ["directMutations", "sectionHide", "sectionShow", "cmHide", "cmShow", "cmStealth", "cmMoveRight", "cmMoveLeft", "BaseComponent", "create", "descriptor", "name", "selectors", "ACTIONLINK", "SECTIONLINK", "CMLINK", "SECTIONNODE", "MODALTOGGLER", "ADDSECTION", "CONTENTTREE", "ACTIONMENU", "ACTIONMENUTOGGLER", "OPTIONSRADIO", "classes", "DISABLED", "sectionname", "pluralsectionname", "actions", "action", "mutationReference", "Object", "entries", "Error", "stateReady", "state", "addEventListener", "this", "element", "_dispatchClick", "_checkSectionlist", "CourseEvents", "sectionRefreshed", "getWatchers", "watch", "handler", "event", "target", "closest", "classList", "contains", "preventDefault", "actionName", "dataset", "methodName", "_actionMethodName", "undefined", "_requestMutationAction", "requestName", "char<PERSON>t", "toUpperCase", "slice", "_setAddSectionLocked", "course", "sectionlist", "length", "maxsections", "_getTargetIds", "ids", "_target$dataset", "id", "push", "bulkType", "_target$dataset2", "bulk", "reactive", "get", "enabled", "selectedType", "selection", "sectionIds", "pendingModalReady", "Pending", "editTools", "_getClosestActionMenuToogler", "data", "getExporter", "titleText", "sectionInfo", "sectionid", "sectiontitle", "title", "information", "getFormatString", "count", "modalParams", "body", "Templates", "render", "modal", "_modalBodyRenderedPromise", "modalBody", "getBody", "for<PERSON>ach", "sectionId", "currentElement", "querySelector", "_disableLink", "ContentTree", "SECTION", "TOGGLER", "COLLAPSE", "matches", "for", "getAttribute", "dispatch", "_destroyModal", "resolve", "cmIds", "exporter", "cmInfo", "cmid", "cmname", "cmId", "ENTER", "sectionnode", "toggler", "find", "collapsibleId", "attr", "replace", "expandNode", "collapse", "targetSectionId", "targetCmId", "dropData", "cmDraggableData", "nextcmid", "section", "cmlist", "some", "hassummary", "rawtitle", "bodyText", "type", "ModalFactory", "types", "DELETE_CANCEL", "getRoot", "on", "ModalEvents", "delete", "e", "destroy", "mutationName", "modname", "allowstealth", "canUseStealth", "saveButtonText", "SAVE_CANCEL", "_setupMutationRadioButtonModal", "setButtonDisabled", "submitFunction", "radio", "mutation", "value", "querySelectorAll", "parentNode", "checked", "dbClickEvent", "save", "locked", "getElements", "toggle", "setElementLocked", "style", "pointerEvents", "userSelect", "add", "setAttribute", "Promise", "reject", "then", "setRemoveOnClose", "bodyRendered", "setSaveButtonText", "deleteButtonText", "setDeleteButtonText", "show", "catch", "hide", "pendingDestroy", "focus", "setTimeout", "actionMenu"], "mappings": ";;;;;;;;;;;ujCA0CgB,OAAQ,CACtB,oBACA,mBACA,UACA,iBAMIA,gBAAkB,CACtBC,YAAa,cACbC,YAAa,cACbC,OAAQ,SACRC,OAAQ,SACRC,UAAW,YACXC,YAAa,cACbC,WAAY,qCAGeC,wBAM3BC,OAAOC,iBAEAC,KAAO,uBAEPC,UAAY,CACfC,2BAEAC,mCACAC,yBACAC,uCACAC,wCACAC,wCACAC,oCACAC,0BACAC,6CAEAC,oCAGGC,QAAU,CACbC,0BAGGC,YAAcf,WAAWe,iBACzBC,kBAAoBhB,WAAWgB,oCAQpBC,aACX,MAAOC,OAAQC,qBAAsBC,OAAOC,QAAQJ,SAAU,IAElC,mBAAtBE,mBACsB,iBAAtBA,wBAED,IAAIG,gBACLJ,yDAGP5B,gBAAgB4B,QAAUC,mBAU9BI,WAAWC,YAEJC,iBAAiBC,KAAKC,QAAS,QAASD,KAAKE,qBAE7CC,kBAAkB,CAAEL,mBAEpBC,iBAAiBC,KAAKC,QAASG,aAAaC,kBAAkB,IACjEL,KAAKG,kBAAkB,CAAEL,gBAS7BQ,oBACS,CAEL,CAAEC,mCAAqCC,QAASR,KAAKG,oBAIzDD,eAAeO,aACPC,OAASD,MAAMC,OAAOC,QAAQX,KAAKxB,UAAUC,gBAC9CiC,iBAGDA,OAAOE,UAAUC,SAASb,KAAKb,QAAQC,sBACzCqB,MAAMK,uBAKFC,WAAaL,OAAOM,QAAQxB,OAC5ByB,WAAajB,KAAKkB,kBAAkBH,oBAEjBI,IAArBnB,KAAKiB,wBAM2BE,IAAhCvD,gBAAgBmD,YACyB,mBAAhCnD,gBAAgBmD,iBACzBnD,gBAAgBmD,YAAYL,OAAQD,iBAGjCW,uBAAuBV,OAAQD,MAAO7C,gBAAgBmD,yBAVtDE,YAAYP,OAAQD,OAe7BS,kBAAkB3C,YACV8C,YAAc9C,KAAK+C,OAAO,GAAGC,cAAgBhD,KAAKiD,MAAM,2BAC5CH,aASpBlB,4BAAkBL,MAAEA,iBAEb2B,qBACH3B,MAAM4B,OAAOC,YAAYC,OAAS9B,MAAM4B,OAAOG,aAanDC,cAAcpB,iDACRqB,IAAM,GACNrB,sCAAAA,OAAQM,oCAARgB,gBAAiBC,IACnBF,IAAIG,KAAKxB,OAAOM,QAAQiB,UAEpBE,SAAWzB,uCAAAA,OAAQM,2CAARoB,iBAAiBC,SAC7BF,gBACIJ,UAEHM,KAAOrC,KAAKsC,SAASC,IAAI,eAC3BF,KAAKG,SAAWH,KAAKI,eAAiBN,WACxCJ,IAAM,IAAIA,OAAQM,KAAKK,YAElBX,8BASiBrB,OAAQD,aAE1BkC,WAAa3C,KAAK8B,cAAcpB,WACb,GAArBiC,WAAWf,cAIfnB,MAAMK,uBAEA8B,kBAAoB,IAAIC,iEAKxBC,UAAY9C,KAAK+C,6BAA6BrC,QAI9CsC,KADWhD,KAAKsC,SAASW,cACTvB,OAAO1B,KAAKsC,SAASxC,WACvCoD,UAAY,KAGZC,YAAc,KACO,GAArBR,WAAWf,QACbuB,YAAcnD,KAAKsC,SAASC,IAAI,UAAWI,WAAW,IACtDK,KAAKI,UAAYD,YAAYlB,GAC7Be,KAAKK,aAAeF,YAAYG,MAChCN,KAAKO,kBAAoBvD,KAAKsC,SAASkB,gBACrC,mBACAR,KAAKK,cAGPH,UAAYlD,KAAKsC,SAASkB,gBACxB,oBACAxD,KAAKX,eAGP2D,KAAKO,kBAAoBvD,KAAKsC,SAASkB,gBACrC,oBACA,CACEC,MAAOd,WAAWf,OAClBvC,YAAaW,KAAKV,oBAGtB4D,UAAYlD,KAAKsC,SAASkB,gBACxB,qBACAxD,KAAKV,0BAKHoE,YAAc,CAClBJ,MAAOJ,UACPS,KAAMC,mBAAUC,OACd,8CACAb,OAKEc,YAAc9D,KAAK+D,0BAA0BL,aAE7CM,WAAY,uBAASF,MAAMG,WAGjCtB,WAAWuB,SAASC,kBACZC,eAAiBJ,UAAUK,wBAC5BrE,KAAKxB,UAAUE,iCAAwByF,sBAEvCG,aAAaF,uBAIhBG,qBACFP,UAAUK,cAAcrE,KAAKxB,UAAUO,aACvC,CACEyF,QAASxE,KAAKxB,UAAUI,YACxB6F,QAASzE,KAAKxB,UAAUK,aACxB6F,SAAU1E,KAAKxB,UAAUK,eAE3B,GAIFmF,UAAUjE,iBAAiB,SAAUU,cAC7BC,OAASD,MAAMC,OAElBA,OAAOiE,QAAQ,MACM,WAAtBjE,OAAOM,QAAQ4D,UACOzD,IAAtBT,OAAOM,QAAQiB,KAIbvB,OAAOmE,aAAa,mBAGxBpE,MAAMK,sBACDwB,SAASwC,SAAS,mBAAoBnC,WAAYjC,OAAOM,QAAQiB,SACjE8C,cAAcjB,MAAOhB,gBAG5BF,kBAAkBoC,+BASCtE,OAAQD,aAErBwE,MAAQjF,KAAK8B,cAAcpB,WACb,GAAhBuE,MAAMrD,cAIVnB,MAAMK,uBAEA8B,kBAAoB,IAAIC,4DAKxBC,UAAY9C,KAAK+C,6BAA6BrC,QAG9CwE,SAAWlF,KAAKsC,SAASW,cACzBD,KAAOkC,SAASxD,OAAO1B,KAAKsC,SAASxC,WAEvCoD,UAAY,QACI,GAAhB+B,MAAMrD,OAAa,OACfuD,OAASnF,KAAKsC,SAASC,IAAI,KAAM0C,MAAM,IAC7CjC,KAAKoC,KAAOD,OAAOlD,GACnBe,KAAKqC,OAASF,OAAO5G,KACrByE,KAAKO,kBAAoBvD,KAAKsC,SAASkB,gBACrC,cACAR,KAAKqC,QAEPnC,UAAYlD,KAAKsC,SAASkB,gBAAgB,qBAE1CR,KAAKO,kBAAoBvD,KAAKsC,SAASkB,gBACrC,eACAyB,MAAMrD,QAERsB,UAAYlD,KAAKsC,SAASkB,gBAAgB,uBAItCE,YAAc,CAClBJ,MAAOJ,UACPS,KAAMC,mBAAUC,OAAO,yCAA0Cb,OAI7Dc,YAAc9D,KAAK+D,0BAA0BL,aAE7CM,WAAY,uBAASF,MAAMG,WAGjCgB,MAAMf,SAASoB,aACPlB,eAAiBJ,UAAUK,wBAC5BrE,KAAKxB,UAAUG,4BAAmB2G,iBAElChB,aAAaF,uBAIhBG,qBAAYP,UAAUK,cAAcrE,KAAKxB,UAAUO,aAAc,CACnEyF,QAASxE,KAAKxB,UAAUI,YACxB6F,QAASzE,KAAKxB,UAAUK,aACxB6F,SAAU1E,KAAKxB,UAAUK,aACzB0G,MAAOvF,KAAKxB,UAAUE,cAKxBuG,MAAMf,SAASoB,+BAIPE,YAHiBxB,UAAUK,wBAC5BrE,KAAKxB,UAAUG,4BAAmB2G,YAEJ3E,QAAQX,KAAKxB,UAAUI,aACpD6G,SAAU,mBAAOD,aAAaE,KAAK1F,KAAKxB,UAAUK,kBACpD8G,oCAAgBF,QAAQzC,KAAK,iDAAayC,QAAQG,KAAK,WACvDD,cAAe,CAEjBA,cAAgBA,cAAcE,QAAQ,IAAK,UACrCC,WAAa9B,UAAUK,yBAAkBsB,oCACxCG,YAAYC,SAAS,YAIhC/B,UAAUjE,iBAAiB,SAAUU,cAC7BC,OAASD,MAAMC,WAElBA,OAAOiE,QAAQ,WACOxD,IAAvBT,OAAOM,QAAQ4D,UACOzD,IAAtBT,OAAOM,QAAQiB,aAIbvB,OAAOmE,aAAa,4BAKpBmB,gBACAC,cAHJxF,MAAMK,iBAIoB,MAAtBJ,OAAOM,QAAQ4D,IAAa,OACxBsB,SAAWhB,SAASiB,gBACxBnG,KAAKsC,SAASxC,MACdY,OAAOM,QAAQiB,IAEjB+D,gBAAkBE,SAAS9C,UAC3B6C,WAAaC,SAASE,aACjB,OACCC,QAAUrG,KAAKsC,SAASC,IAAI,UAAW7B,OAAOM,QAAQiB,IAC5D+D,gBAAkBtF,OAAOM,QAAQiB,GACjCgE,WAAaI,qBAAAA,QAASC,OAAO,QAE1BhE,SAASwC,SAAS,SAAUG,MAAOe,gBAAiBC,iBACpDlB,cAAcjB,MAAOhB,cAG5BF,kBAAkBoC,mCASKtE,OAAQD,8BAC/BA,MAAMK,sBACDwB,SAASwC,SAAS,wCAAcpE,OAAOM,QAAQiB,oDAAM,+BAShCvB,OAAQD,aAC5BkC,WAAa3C,KAAK8B,cAAcpB,WACb,GAArBiC,WAAWf,iBAIfnB,MAAMK,kBAGkB6B,WAAW4D,MAAMpC,0CACjChB,YAAcnD,KAAKsC,SAASC,IAAI,UAAW4B,8CAClChB,YAAYmD,0DAAU,IACvB1E,QAAUuB,YAAYqD,YAAcrD,YAAYsD,QAA9D,qBAGKnE,SAASwC,SAAS,gBAAiBnC,gBAItC+D,SAAW,KACXxD,UAAY,QACS,GAArBP,WAAWf,OAAa,CAC1BsB,UAAYlD,KAAKsC,SAASkB,gBACxB,sBACAxD,KAAKX,mBAED8D,YAAcnD,KAAKsC,SAASC,IAAI,UAAWI,WAAW,IAC5D+D,SAAW1G,KAAKsC,SAASkB,gBAAgB,qBAAsB,CAC7DjF,KAAM4E,YAAYG,MAClBjE,YAAaW,KAAKX,mBAGpB6D,UAAYlD,KAAKsC,SAASkB,gBACxB,uBACAxD,KAAKV,mBAEPoH,SAAW1G,KAAKsC,SAASkB,gBAAgB,sBAAuB,CAC9DC,MAAOd,WAAWf,OAClBvC,YAAaW,KAAKV,0BAIhBoE,YAAc,CAClBJ,MAAOJ,UACPS,KAAM+C,SACNC,KAAMC,uBAAaC,MAAMC,eAGrBhD,YAAc9D,KAAK+D,0BAA0BL,aAEnDI,MAAMiD,UAAUC,GAAGC,sBAAYC,QAASC,IAEtCA,EAAErG,iBACFgD,MAAMsD,eACD9E,SAASwC,SAAS,gBAAiBnC,+CAUZjC,OAAQD,oDACZT,KAAKsC,SAAU5B,OAAQD,MAAO,2CASrBC,OAAQD,oDACjBT,KAAKsC,SAAU5B,OAAQD,MAAO,wCAU7BC,OAAQD,MAAO4G,cACrC3G,OAAOM,QAAQiB,KAGpBxB,MAAMK,sBACDwB,SAASwC,SAASuC,aAAc,CAAC3G,OAAOM,QAAQiB,gCAS7BvB,OAAQD,uCAC1BwE,MAAQjF,KAAK8B,cAAcpB,WACb,GAAhBuE,MAAMrD,oBAGJuC,wCAAYzD,OAAOM,QAAQoC,iEAAa,KAC9C3C,MAAMK,sBACDwB,SAASwC,SAAS,cAAeG,MAAOd,kCASxBzD,OAAQD,aACvBwE,MAAQjF,KAAK8B,cAAcpB,WACb,GAAhBuE,MAAMrD,cAIVnB,MAAMK,qBAEF4F,SAAW,KACXxD,UAAY,QACI,GAAhB+B,MAAMrD,OAAa,OACfuD,OAASnF,KAAKsC,SAASC,IAAI,KAAM0C,MAAM,IAC7C/B,WAAY,mBAAU,iBAAkB,qBACxCwD,UAAW,mBAAU,gBAAiB,oBAAqB,CACzDC,KAAMxB,OAAOmC,QACb/I,KAAM4G,OAAO5G,YAGf2E,WAAY,mBAAU,kBAAmB,qBACzCwD,UAAW,mBAAU,iBAAkB,oBAAqB,CAC1DjD,MAAOwB,MAAMrD,eAIX8B,YAAc,CAClBJ,MAAOJ,UACPS,KAAM+C,SACNC,KAAMC,uBAAaC,MAAMC,eAGrBhD,YAAc9D,KAAK+D,0BAA0BL,aAEnDI,MAAMiD,UAAUC,GAAGC,sBAAYC,QAASC,IAEtCA,EAAErG,iBACFgD,MAAMsD,eACD9E,SAASwC,SAAS,WAAYG,uCASVvE,cACrBuE,MAAQjF,KAAK8B,cAAcpB,WACb,GAAhBuE,MAAMrD,oBAKJoB,KAAO,CACXuE,aAFevH,KAAKsC,SAASW,cAENuE,cAAcxH,KAAKsC,SAASxC,MAAOmF,QAEtDvB,YAAc,CAClBJ,OAAO,mBAAU,eAAgB,QACjCK,KAAMC,mBAAUC,OACd,uDACAb,MAEFyE,gBAAgB,mBAAU,QAAS,QACnCd,KAAMC,uBAAaC,MAAMa,aAErB5D,YAAc9D,KAAK+D,0BAA0BL,kBAE9CiE,+BAA+B7D,MAAOmB,yCAQXvE,cAC1BiC,WAAa3C,KAAK8B,cAAcpB,WACb,GAArBiC,WAAWf,oBAGT0B,MACiB,GAArBX,WAAWf,OACP,4BACA,6BAEAvC,YACiB,GAArBsD,WAAWf,OAAc5B,KAAKX,YAAcW,KAAKV,kBAG7CoE,YAAc,CAClBJ,MAAOtD,KAAKsC,SAASkB,gBAAgBF,MAAOjE,aAC5CsE,KAAMC,mBAAUC,OACd,4DACA,IAEF4D,gBAAgB,mBAAU,QAAS,QACnCd,KAAMC,uBAAaC,MAAMa,aAErB5D,YAAc9D,KAAK+D,0BAA0BL,kBAE9CiE,+BAA+B7D,MAAOnB,YAQ7CgF,+BAA+B7D,MAAO/B,KAEpC+B,MAAM8D,kBAAkB,QAAQ,SAE1BC,eAAkBC,cAChBC,SAAWD,mBAAAA,MAAOE,cACnBD,gBAGAzF,SAASwC,SAASiD,SAAUhG,MAC1B,EAAP,EAGIiC,WAAY,uBAASF,MAAMG,WACZD,UAAUiE,iBAC7BjI,KAAKxB,UAAUU,cAEJgF,SAAS4D,QACpBA,MAAM/H,iBAAiB,UAAU,KAC/B+D,MAAM8D,kBAAkB,QAAQ,MAElCE,MAAMI,WAAWnI,iBAAiB,SAAS,KACzC+H,MAAMK,SAAU,EAChBrE,MAAM8D,kBAAkB,QAAQ,MAElCE,MAAMI,WAAWnI,iBAAiB,YAAaqI,eACzCP,eAAeC,SACjBM,aAAatH,iBACbgD,MAAMsD,iBAKZtD,MAAMiD,UAAUC,GAAGC,sBAAYoB,MAAM,WAC7BP,MAAQ9D,UAAUK,wBACnBrE,KAAKxB,UAAUU,0BAEpB2I,eAAeC,MAAf,IASJrG,qBAAqB6G,QACHtI,KAAKuI,YAAYvI,KAAKxB,UAAUM,YACxCoF,SAASjE,UACfA,QAAQW,UAAU4H,OAAOxI,KAAKb,QAAQC,SAAUkJ,aAC3CG,iBAAiBxI,QAASqI,WASnChE,aAAarE,SACPA,UACFA,QAAQyI,MAAMC,cAAgB,OAC9B1I,QAAQyI,MAAME,WAAa,OAC3B3I,QAAQW,UAAUiI,IAAI7I,KAAKb,QAAQC,UACnCa,QAAQ6I,aAAa,iBAAiB,GACtC7I,QAAQF,iBAAiB,SAAUU,OAAUA,MAAMK,oBAUvDiD,0BAA0BL,oBACjB,IAAIqF,SAAQ,CAAC/D,QAASgE,iCACd3K,OAAOqF,aACjBuF,MAAMnF,QACLA,MAAMoF,kBAAiB,GAEvBpF,MAAMiD,UAAUC,GAAGC,sBAAYkC,cAAc,KAC3CnE,QAAQlB,MAAR,SAGiC3C,IAA/BuC,YAAY+D,gBACd3D,MAAMsF,kBAAkB1F,YAAY+D,qBAEDtG,IAAjCuC,YAAY2F,kBACdvF,MAAMwF,oBAAoB5F,YAAY+D,gBAExC3D,MAAMyF,UAGPC,OAAM,KACLR,mCAAA,OAaRjE,cAAcjB,MAAO7D,SACnB6D,MAAM2F,aACAC,eAAiB,IAAI7G,sDACvB5C,SACFA,QAAQ0J,QAEVC,YAAW,KACT9F,MAAMsD,UACNsC,eAAe1E,YACd,KASLjC,6BAA6B9C,eACrB4J,WAAa5J,QAAQU,QAAQX,KAAKxB,UAAUQ,eAC7C6K,kBAGEA,WAAWxF,cAAcrE,KAAKxB,UAAUS"}