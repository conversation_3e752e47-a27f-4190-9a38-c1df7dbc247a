<?php namespace tool_migration\models;

use block_externalhistory\models\external_history;
use coding_exception;
use stored_file;
use tool_migration\util\ingestion_helper;
use context_user;

defined('MOODLE_INTERNAL')||die();

class curso_externo extends abstract_migratable_entity{
    const TABLE = 'eadtech_cursos_externos';

    protected int $userid;

    public static function define_identifier(): string {
        return 'source_id';
    }

    protected function get_userid() : ?int {
        if(!isset($this->userid)){
            $this->userid = estudante::get_instanceid_from_identifier($this->get('source_codaluno'));
        }
        return $this->userid;
    }

    protected function get_eadtech_certificate_path() : ?string {
        global $CFG;

        $eadtech_certificates_dir = "$CFG->dirroot/eadtech/certificates";
        $filepath = $eadtech_certificates_dir . '/' . $this->get('source_certificadoguid');

        if(file_exists($filepath)){
            return $filepath;
        }

        return null;
    }
    
    protected static function define_model_properties(): array{
        return [
            'source_id' => [
                'type' => PARAM_INT,
            ],
            'source_codaluno' => [
                'type' => PARAM_INT,
            ],
            'source_nomedaacao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_descricao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_instituicao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_datarealizacao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_cargahoraria' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_certificado' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_criado' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_editado' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_excluido' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_certificadoguid' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_datarealizacaofim' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
        ];
    }

    public function get_idnumber(): string{
        return 'LEGADO-' . $this->get('source_id');
    }

    public function to_external_history(): external_history {
        $history = (object)[
            'userid' => $this->get_userid(),
            'solutionname' => $this->get('source_nomedaacao'),
            'format' => null,
            'description' => $this->get('source_descricao'),
            'hourscompleted' => ingestion_helper::parse_workload($this->get('source_cargahoraria')),
            'startdate' => ingestion_helper::str_to_timestamp($this->get('source_datarealizacao')),
            'enddate' => ingestion_helper::str_to_timestamp($this->get('source_datarealizacaofim')),
            'institution' => $this->get('source_instituicao'),
            'status' => 1,
        ];

        if($id = $this->get('instanceid')){
            $history->id = $id;
        }

        return new external_history(0, $history);
    }

    /**
     * @return stored_file
     */
    public function save_certificate_file(external_history $history) : stored_file {
        $filepath = $this->get_eadtech_certificate_path();
        return $history->save_certificate_file_from_filepath($filepath);
    }
}
