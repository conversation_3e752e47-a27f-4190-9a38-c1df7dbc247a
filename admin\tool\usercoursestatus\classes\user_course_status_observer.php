<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace tool_usercoursestatus;

use tool_usercoursestatus\model\user_course_status;
use tool_usercoursestatus\task\update_course_status;
use tool_usercoursestatus\utils\constants;

/**
 * Class user_course_status_observer
 *
 * @package    tool_usercoursestatus
 * @copyright  2025 2015 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class user_course_status_observer
{
    /**
     * Triggered via user_enrolment_created event.
     *
     * @param \core\event\user_enrolment_created $event
     * @return bool true on success.
     */
    public static function user_enrolment_created(\core\event\user_enrolment_created $event)
    {
        return self::process_status($event->relateduserid, $event->courseid);
    }

    /**
     * Triggered via user_enrolment_deleted event.
     *
     * @param \core\event\user_enrolment_deleted $event
     * @return bool true on success.
     */
    public static function user_enrolment_deleted(\core\event\user_enrolment_deleted $event)
    {
        return self::process_status($event->relateduserid, $event->courseid);
    }

    /**
     * Triggered via user_enrolment_updated event.
     *
     * @param \core\event\user_enrolment_updated $event
     * @return bool true on success
     */
    public static function user_enrolment_updated(\core\event\user_enrolment_updated $event)
    {
        return self::process_status($event->relateduserid, $event->courseid);
    }

    /**
     * Triggered via course_completion_updated event.
     *
     * @param \core\event\course_completion_updated $event
     * @return bool true on success.
     */
    public static function course_completion_updated(\core\event\course_completion_updated $event)
    {
        return self::create_task($event->courseid);
    }

    /**
     * Triggered when 'user_graded' event is triggered.
     *
     * @param \core\event\user_graded $event
     */
    public static function user_graded(\core\event\user_graded $event)
    {
        global $DB;

        if (!$item = $DB->get_record('grade_items', ['id' => $event->other['itemid']])) {
            return;
        }
        if (!$grade = $DB->get_record('grade_grades', ['id' => $event->objectid])) {
            return;
        }

        $course     = $DB->get_record('course', ['id' => $event->courseid]);
        $userid     = ($event->relateduserid) ? $event->relateduserid : $event->userid;
        $user       = $DB->get_record('user', array('id' => $userid));

        if ($course && $user) {
            self::process_status($user->id, $course->id);
        }
    }

    /**
     * Triggered when 'grade_deleted' event is triggered.
     *
     * @param \core\event\grade_deleted $event
     */
    public static function grade_deleted(\core\event\grade_deleted $event)
    {
        global $DB;

        $course = $DB->get_record('course', ['id' => $event->context->instanceid]);

        if ($course) {
            self::create_task($event->courseid);
        }
    }

    /**
     * Triggered when 'grade_item_created' event is triggered.
     *
     * @param \core\event\grade_item_created $event
     */
    public static function grade_item_created(\core\event\grade_item_created $event)
    {
        global $DB;

        $course = $DB->get_record('course', ['id' => $event->courseid]);

        if ($course) {
            self::create_task($event->courseid);
        }
    }

    /**
     * Triggered when 'grade_item_updated' event is triggered.
     *
     * @param \core\event\grade_item_updated $event
     */
    public static function grade_item_updated(\core\event\grade_item_updated $event)
    {
        global $DB;

        $course = $DB->get_record('course', ['id' => $event->courseid]);

        if ($course) {
            self::create_task($event->courseid);
        }
    }

    /**
     * Triggered when 'grade_item_deleted' event is triggered.
     *
     * @param \core\event\grade_item_deleted $event
     */
    public static function grade_item_deleted(\core\event\grade_item_deleted $event)
    {
        global $DB;

        $course = $DB->get_record('course', ['id' => $event->courseid]);

        if ($course) {
            self::create_task($event->courseid);
        }
    }

    /**
     * Triggered when 'course_content_deleted' event is triggered.
     *
     * @param \core\event\course_content_deleted $event
     */
    public static function course_content_deleted(\core\event\course_content_deleted $event)
    {
        global $DB;

        $course = $DB->get_record('course', ['id' => $event->objectid]);

        if ($course) {
            self::create_task($event->courseid);
        }
    }

    /**
     * Triggered when 'course_completed' event is triggered.
     *
     * @param \core\event\course_completed $event
     */
    public static function course_completed(\core\event\course_completed $event)
    {
        return self::process_status($event->relateduserid, $event->courseid);
    }

    /**
     * Triggered when 'course_deleted' event is triggered.
     *
     * @param \core\event\course_deleted $event
     */
    public static function course_deleted(\core\event\course_deleted $event)
    {
        global $DB;

        $course = $DB->get_record('course', ['id' => $event->courseid]);

        if ($course) {
            self::create_task($event->courseid);
        }
    }

    /**
     * Triggered when 'course_updated' event is triggered.
     *
     * @param \core\event\course_updated $event
     */
    public static function course_updated(\core\event\course_updated $event)
    {
        global $DB;

        $course = $DB->get_record('course', ['id' => $event->courseid]);

        if ($course) {
            self::create_task($event->courseid);
        }
    }

    /**
     * Triggered when 'course_module_completion_updated' event is triggered.
     *
     * @param \core\event\course_module_completion_updated $event
     */
    public static function course_module_completion_updated(\core\event\course_module_completion_updated $event)
    {
        global $DB;

        $course = $DB->get_record('course', ['id' => $event->courseid]);

        if ($course) {
            self::create_task($event->courseid);
        }
    }

    protected static function create_task($courseid)
    {
        $adhoc_task = update_course_status::instance($courseid);

        \core\task\manager::queue_adhoc_task($adhoc_task, true);
    }

    protected static function process_status(int $userid, int $courseid)
    {
        $status = user_course_status::get_record(
            [
                'userid' => $userid,
                'courseid' => $courseid
            ]
        );

        if ($status) {
            $status->update_status_if_different();
        } else {
            $status = new user_course_status(
                0,
                (object) [
                    'userid' => $userid,
                    'courseid' => $courseid,
                    'status' => constants::STATUS_ENROLED
                ]
            );

            $status->save();
            $status->update_status_if_different();
        }
        return true;
    }
}
