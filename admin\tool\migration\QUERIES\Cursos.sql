SELECT
    C<PERSON>CODCURSO,
    <PERSON><PERSON>,
    C.<PERSON>,
    <PERSON><PERSON>,
    C.NOMECURSOMENU,
    C.STATUS,
    C.PRAZO,
    C.CRIADO                      AS DATACRIACAO,
    C.<PERSON>ODIFICADO                  AS DATAMOFICACAO,
    C.CURSOPRESENCIAL,
    C.FREQUENCIA,
    C.MEDIA,
    C.REMATRICULA,
    C.Keywords,
    C.CARGAHORARIA,
    C.DESCRICAO,
    (
        SELECT TOP 1 pcpt.NOME
        FROM dbUcSebraeFull.dbo.PDICOMPETENCIA_CURSOS ppc
        INNER JOIN dbUcSebraeFull.dbo.PDICOMPETENCIA   pcpt
            ON pcpt.CODPDICOMPETENCIA = ppc.CO<PERSON>DI<PERSON>MP<PERSON><PERSON>CIA
        WHERE ppc.CODCURSO = C.CODCURSO
    )                             AS TIPO_SOLUCAO,
    (
        SELECT cc.DESCRICAO
        FROM CURSOCARACTERISTICA cc
        WHERE cc.CODCURSO  = C.CODCURSO
          AND cc.CODCARACT = 10132
    )                             AS Apresentacao,
    (
        SELECT cc.DESC<PERSON>CAO
        FROM CURSOCARACTERISTICA cc
        WHERE cc.CODCURSO  = C.CODCURSO
          AND cc.CODCARACT = 10133
    )                             AS Objetivos,
    (
        SELECT cc.DESCRICAO
        FROM CURSOCARACTERISTICA cc
        WHERE cc.CODCURSO  = C.CODCURSO
          AND cc.CODCARACT = 10134
    )                             AS Conteudo_Programatico,
    (
        SELECT cc.DESCRICAO
        FROM CURSOCARACTERISTICA cc
        WHERE cc.CODCURSO  = C.CODCURSO
          AND cc.CODCARACT = 10135
    )                             AS Nivel_de_Complexidade,
    (
        SELECT cc.DESCRICAO
        FROM CURSOCARACTERISTICA cc
        WHERE cc.CODCURSO  = C.CODCURSO
          AND cc.CODCARACT = 10136
    )                             AS Termo_de_Aceite,
    (
        SELECT cc.DESCRICAO
        FROM CURSOCARACTERISTICA cc
        WHERE cc.CODCURSO  = C.CODCURSO
          AND cc.CODCARACT = 10137
    )                             AS Ficha_Tecnica,
    (
        SELECT cc.DESCRICAO
        FROM CURSOCARACTERISTICA cc
        WHERE cc.CODCURSO  = C.CODCURSO
          AND cc.CODCARACT = 10138
    )                             AS Requisitos,
    (
        SELECT cc.DESCRICAO
        FROM CURSOCARACTERISTICA cc
        WHERE cc.CODCURSO  = C.CODCURSO
          AND cc.CODCARACT = 10139
    )                             AS Criterios_de_Avaliacao,
    (
        SELECT cc.DESCRICAO
        FROM CURSOCARACTERISTICA cc
        WHERE cc.CODCURSO  = C.CODCURSO
          AND cc.CODCARACT = 10140
    )                             AS Publico,
    (
        SELECT cc.DESCRICAO
        FROM CURSOCARACTERISTICA cc
        WHERE cc.CODCURSO  = C.CODCURSO
          AND cc.CODCARACT = 10142
    )                             AS Area_Subarea
FROM CURSOS C;


-- Não tem tipo de solução?
-- Não conseguem dizer se um curso está sendo ofertado ou não?
-- O ID da Degreed se referia ao encontrado em [dbo].[DegreedContent]

-- Não tem tipo de solução?
-- Não conseguem dizer se um curso está sendo ofertado ou não?
-- O ID da Degreed se referia ao encontrado em [dbo].[DegreedContent]