<form method="get" action="index.php" class="table-form pb-md-0 pb-5">
    <div class="">
		<p class="text-muted font-weight-bold h5">{{total_categories}} Categorias, {{total_courses}} {{#str}}courses{{/str}}</p>
        <div class="d-lg-flex justify-content-between align-items-center mb-4 mt-5">
            <div class="d-lg-flex align-items-center">
				<div class="d-flex flex-column mb-2 mb-lg-0">
					<label class="mb-1">Pesquisa</label>
					<div class="input-group">
						<input type="text" class="form-control rounded-sm" name="search" placeholder="{{#str}}searchcourses{{/str}}" value="{{search}}" aria-label="{{#str}}searchcourses{{/str}}" aria-describedby="basic-addon2">
						<div class="input-group-append">
							<button type="submit" class="btn btn-primary w-auto"><i class="fa fa-search"></i></button>
						</div>
					</div>
				</div>

				<div class="d-md-flex">
					<div class="d-flex flex-column ml-lg-3 p-0 mb-2 mb-lg-0">
						<label class="mb-1 text-nowrap">Mover categorias selecionadas para</label>
						<div class="input-group movecategoriesto" style="max-width:250px">
							<select class="custom-select rounded-sm" name="movecategoriesto" disabled>
								<option value="">{{#str}}choose{{/str}}</option>
								<option value="0">{{#str}}top{{/str}}</option>
								{{#categorylist}}
									<option value="{{id}}">{{name}}</option>
								{{/categorylist}}
							</select>
							<div class="input-group-append">
								<button type="submit" class="btn btn-primary btn-bulkaction w-auto" name="bulkmovecategories" value="1" disabled><i class="fa fa-refresh"></i></button>
							</div>
						</div>
					</div>

					<div class="d-flex flex-column ml-md-3 p-0 mb-2 mb-lg-0">
						<label class="mb-1">Mover cursos selecionados para</label>
						<div class="input-group movecoursesto" style="max-width:250px">
							<select class="custom-select rounded-sm" name="movecoursesto" disabled>
								<option value="">{{#str}}choose{{/str}}</option>
								{{#categorylist}}
									<option value="{{id}}">{{name}}</option>
								{{/categorylist}}
							</select>
							<div class="input-group-append">
								<button type="submit" class="btn btn-primary btn-bulkaction w-auto" name="bulkmovecourses" value="1" disabled><i class="fa fa-refresh"></i></button>
							</div>
						</div>
					</div>
				</div>

				<input type="hidden" class="input-bulkaction" name="action" value="bulkaction" disabled />
			</div>

			{{#hasinterestareascap}}<a href="{{wwwroot}}/admin/tool/interestareas/index.php" class="btn btn-primary mt-auto">{{#str}}pluginname, tool_interestareas{{/str}}</a>{{/hasinterestareascap}}

        </div>

		<div class="custom-link my-4 text-right">
			<a href="#" class="btn-collapse-toggle expanded" data-toggle="collapse" data-target=".collapse">
				<span class="collapse-text">{{#str}}expandall{{/str}}</span>
				<span class="contract-text">{{#str}}collapseall{{/str}}</span>
			</a>
		</div>
    </div>

	<div class="mb-3">
		<a href="#" class="btn btn-block btn-add btn-edit-category rounded-sm text-left p-3" data-title="{{#str}}addnewcategory{{/str}}"><i class="fa fa-plus-circle pr-2"></i> {{#str}}addnewcategory{{/str}}</a>
	</div>

	{{{categories}}}
</form>

{{>tool_coursemanagement/category_form}}
{{>tool_coursemanagement/course_form}}

{{#js}}
require(['jquery', 'jqueryui', 'core_form/modalform', 'core/notification'], function($, ui, ModalForm, Notification) {
	function resize_course_cards(){
		let card_imgOriginalWidth = 600;
		let card_imgOriginalHeight = 350;

		let card_width = Math.max.apply(null, Array.from($(".courses-list li.course-item")).map(function(a){
				return a.offsetWidth || a.offsetWidth || a.scrollWidth;
			})
		);

		let card_height = (card_width * card_imgOriginalHeight) / card_imgOriginalWidth;

		$(".courses-list.has_courses li").css("height", card_height);
	}

	resize_course_cards();

	$(window).on("resize", resize_course_cards);

	$(".courses-list").sortable({
		connectWith: ".courses-list",
		handle: ".btn-move-course",
		placeholder: "sortable-placeholder col-md-6 col-lg-4 col-xl-3 my-3",
		tolerance: "pointer",
		start: function(e, ui){
			originalCategory = ui.item.closest(".category-item").data("id");
			originalIndex = ui.item.index();

			$(".courses-list").addClass("drop-waiting");
		},
		over: function(e, ui){
			let itemHeight = ui.item.outerHeight();
			$(this).css("min-height", itemHeight);
		},
		out: function(e, ui){
			$(this).css("min-height", "");
		},
		stop: function(e, ui){
			$(".courses-list").removeClass("drop-waiting").css("min-height", "");

			targetCategory = ui.item.closest(".category-item").data("id");
			currentIndex = ui.item.index();
			courseid = ui.item.data("id");

			if(originalCategory == targetCategory){
				if(originalIndex > currentIndex){
					moveCourse("movecourseup", ui.item, courseid, targetCategory);
				}else if(originalIndex < currentIndex){
					moveCourse("movecoursedown", ui.item, courseid, targetCategory);
				}
			}else{
				coursebefore = currentIndex ? ui.item.prev(".course-item").data("id") : 0;
				moveCourse("movecoursesintocategory", ui.item, courseid, targetCategory, coursebefore);
			}
		}
	}).disableSelection();

	$(".category-list").sortable({
		axis: "y",
		handle: ".btn-move-category:not(.disabled)",
		helper: function(e, ui){
			let $helper = ui.clone(true, true);
			$helper.find(".btn-edit-course, .dropdown, > ul").remove();
			$helper.css({
				"right": "0",
				"height": "auto",
			});
			return $helper;
		},
		placeholder: "sortable-placeholder px-3 py-2 rounded-sm",
		tolerance: "pointer",
		start: function(e, ui){
			originalIndex = ui.item.index();
		},
		stop: function(e, ui){
			currentIndex = ui.item.index();
			categoryid = ui.item.data("id");
			let range = 0;

			if(originalIndex > currentIndex){
				range = originalIndex - currentIndex;
				moveCategory("movecategoryup", ui.item, categoryid, range);
			}else if(originalIndex < currentIndex){
				range = currentIndex - originalIndex;
				console.log(range);
				moveCategory("movecategorydown", ui.item, categoryid, range);
			}
		}
	}).disableSelection();

	function moveCategory(action, $item, categoryid, range){
		$.ajax({
			url:"{{wwwroot}}/admin/tool/coursemanagement/index.php",
			method: "POST",
			data: {action: action, categoryid: categoryid, range: range},
			success: function(response){
				$item.find("> .category-title").addClass("changed");

				setTimeout(function(){
					$item.find("> .category-title").removeClass("changed");
				}, 1000);
			},
			error: function(data){
				console.log(data.responseText);
			}
		});
	}

	function moveCourse(action, $item, courseid, categoryid, coursebefore){
		$.ajax({
			url:"{{wwwroot}}/admin/tool/coursemanagement/index.php",
			method: "POST",
			data: {action: action, courseid: courseid, categoryid:categoryid, coursebefore: coursebefore},
			success: function(response){
				$item.addClass("changed");
				count_category_courses();

				setTimeout(function(){
					$item.removeClass("changed");
				}, 1000);
			},
			error: function(data){
				console.log(data.responseText);
			}
		});
	}

	function count_category_courses(){
		$(".courses-list").each(function(){
			let total_courses = $(this).find(".course-item").length;
			let $categoryItem = $(this).closest(".category-item");

			$categoryItem.find(".total_courses").text(total_courses);

			if(total_courses){
				$categoryItem.find("[data-toggle='collapse']").removeClass("d-none");
			}else{
				$categoryItem.find("[data-toggle='collapse']").addClass("d-none");
			}
		});
	}

	count_category_courses();

    $(document).on("click", ".orderby:not(.not_sortable)", function(e){
		e.preventDefault();
		let orderby = $(this).data("order");
		let togglesort = $(this).data("sort");
		$("input[name=sort]").val(orderby);
		$("input[name=dir]").val(togglesort);
		$(this).closest("form").submit();
    })
	.on("click", ".btn-edit-category", function(e){
        e.preventDefault();
        let $button = $(this);
		let parentid = $button.data("parent") ? $button.data("parent") : "0";

        const modalForm = new ModalForm({
            formClass: "tool_coursemanagement\\form\\category_form",
            args: {category: $button.data("id"), parent: parentid},
            modalConfig: {title: $button.data("title")},
            //returnFocus: $button[0],
        });

		let action = $button.data("id") ? "edit_category" : "save_category";
		let redirectTo = "{{wwwroot}}/admin/tool/coursemanagement/redirect.php?action="+action;

        modalForm.addEventListener(modalForm.events.LOADED, (e) => formLoaded(modalForm, "category"));
        modalForm.addEventListener(modalForm.events.SERVER_VALIDATION_ERROR, (e) => formLoaded(modalForm, "category"));
        modalForm.addEventListener(modalForm.events.FORM_SUBMITTED, (e) => window.location.href = redirectTo);
		modalForm.show();
    })
	.on("click", ".btn-edit-course", function(e){
        e.preventDefault();
        let $button = $(this);

        const modalForm = new ModalForm({
            formClass: "tool_coursemanagement\\form\\course_form",
            args: {courseid: $button.data("id"), categoryid: $button.data("categoryid")},
            modalConfig: {title: $button.data("title")},
            //returnFocus: $button[0],
        });

		let action = $button.data("id") ? "edit_course" : "save_course";
		let redirectTo = "{{wwwroot}}/admin/tool/coursemanagement/redirect.php?action="+action;

        modalForm.addEventListener(modalForm.events.LOADED, (e) => formLoaded(modalForm, "course"));
        modalForm.addEventListener(modalForm.events.SERVER_VALIDATION_ERROR, (e) => formLoaded(modalForm, "course"));
        modalForm.addEventListener(modalForm.events.FORM_SUBMITTED, (e) => window.location.href = redirectTo);
		modalForm.show();
	})
	.on("click", ".btn-delete-category", function(e){
        e.preventDefault();
        let $button = $(this);

        const modalForm = new ModalForm({
            formClass: "tool_coursemanagement\\form\\deletecategory_form",
            args: {id: $button.data("id")},
            modalConfig: {title: $button.data("title")},
        });

		let action = "delete_category";
		let redirectTo = "{{wwwroot}}/admin/tool/coursemanagement/redirect.php?action="+action;

        modalForm.addEventListener(modalForm.events.FORM_SUBMITTED, (e) => {
			window.location.href = redirectTo;
		});

		modalForm.show();
	})
	.on("click", ".btn-delete-course", function(e){
		e.preventDefault();

		let fullname = $(this).data("fullname");
		let deleteurl = $(this).attr("href");

		Notification.confirm(
            '{{#str}}deletecourse{{/str}}',
            '{{#str}}deletecoursecheck{{/str}}',
			'{{#str}}delete{{/str}}',
			'',
            function() {
                window.location.href = deleteurl;
            }
        );
    })
	.on("change", ".course-checkbox", function(){
		let disableFields = $(".course-checkbox:checked").length ? false : true;
		$(".movecoursesto").find("select, button").prop("disabled", disableFields);
		$(".input-bulkaction").prop("disabled", true);
	})
	.on("change", ".category-checkbox", function(){
		let disableFields = $(".category-checkbox:checked").length ? false : true;
		$(".movecategoriesto").find("select, button").prop("disabled", disableFields);
		$(".input-bulkaction").prop("disabled", true);
	})
	.on("change", "[name='enddate[enabled]']", function(){
		$(this).closest("fieldset").find(".datepicker").prop("disabled", !$(this).is(":checked"));
	})
	.on("click", ".btn-bulkaction", function(){
		$(".input-bulkaction").prop("disabled", false);
    })
	.on("click", ".btn-collapse-toggle", function(){
		if($(this).is(".expanded")){
			$(this).removeClass("expanded");
		}else{
			$(this).addClass("expanded");
		}
    });

	function format_form(modalForm, formtype){
		let $formattedForm = $(".formatted-"+formtype+"-form").clone();
		let $modalBody = modalForm.modal.body;

		modalForm.modal.modal.addClass("modal-"+formtype+"-form");

		$modalBody.find(".fitem").each(function(){
			let inputName = $(this).find("input, select").attr("name");
			$(this).find(".col-md-3, .col-md-9").removeClass("col-md-3 col-md-9");

			if($(this).is("[data-groupname]")){
				inputName = $(this).data("groupname");
			}

			$datepickerfield = $formattedForm.find(".field-"+inputName+"-container .datepicker");

			if($datepickerfield.length){
				$(this).find("fieldset").html(function (i, html) {
					return html.replace(/&nbsp;/g, '');
				})
				$(this).find("fieldset").addClass("d-flex align-items-center mt-1");
				$(this).find("fieldset .fdate_time_selector").removeClass("flex-wrap");

				$datepickerfield.closest(".datepicker-wrapper").prependTo($(this).find("fieldset"));

				$day = $(this).find("[name='startdate[day]'], [name='enddate[day]']");
				$month = $(this).find("[name='startdate[month]'], [name='enddate[month]']");
				$year = $(this).find("[name='startdate[year]'], [name='enddate[year]']");

				$day.closest(".fitem").addClass("d-none");
				$month.closest(".fitem").addClass("d-none");
				$year.closest(".fitem").addClass("d-none");

				$datepickerfield.val($day.val()+"/"+$month.val()+"/"+$year.val());

				if(inputName == "enddate"){
					if($(this).find("[name='enddate[enabled]']").is(":checked")){
						$datepickerfield.prop("disabled", false);
					}else{
						$datepickerfield.prop("disabled", true);
					}

					$(this).find(".form-check").addClass("ml-3");
				}
			}

			$(this).appendTo($formattedForm.find(".field-"+inputName+"-container"));
		});

		if(!modalForm.modal.footer.find(".fdescription.required").length){
			$modalBody.find(".fdescription.required").addClass("mr-auto").prependTo(modalForm.modal.footer);
		}else{
			$modalBody.find(".fdescription.required").remove();
		}

		$formattedForm.removeClass("d-none");
		$formattedForm.prependTo($modalBody.find("form"));

		if(formtype == "course"){
			modalForm.modal.modal.addClass("modal-xxl");

			$formattedForm.find(".datepicker").datepicker("destroy");

			$formattedForm.find(".datepicker").datepicker({
				closeText: "Fechar",
				prevText: "&#x3C;Anterior",
				nextText: "Próximo&#x3E;",
				currentText: "Hoje",
				monthNames: ["Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
					"Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
				],
				monthNamesShort: ["Jan", "Fev", "Mar", "Abr", "Mai", "Jun",
					"Jul", "Ago", "Set", "Out", "Nov", "Dez"
				],
				dayNames: [
					"Domingo",
					"Segunda-feira",
					"Terça-feira",
					"Quarta-feira",
					"Quinta-feira",
					"Sexta-feira",
					"Sábado"
				],
				dayNamesShort: ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"],
				dayNamesMin: ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"],
				weekHeader: "Sm",
				dateFormat: "dd/mm/yy",
				firstDay: 0,
				isRTL: false,
				showMonthAfterYear: false,
				yearSuffix: "",
				beforeShow: function (input, inst) {
					window.setTimeout(function () {
						let offset = $(input).offset();
						let inputHeight = $(input).outerHeight();
						let top = offset.top + inputHeight - $(document).scrollTop();

						$(inst.dpDiv).css({ top: top + 'px', left:offset.left + 'px' })
					}, 1);
				}
			}).on("change", function(e) {
				date = $(this).val().split("/");

				$(this).closest("fieldset").find("[name='startdate[day]'], [name='enddate[day]']").val(parseInt(date[0]));
				$(this).closest("fieldset").find("[name='startdate[month]'], [name='enddate[month]']").val(parseInt(date[1]));
				$(this).closest("fieldset").find("[name='startdate[year]'], [name='enddate[year]']").val(parseInt(date[2]));
			}).on("keypress", function(e) {
				if (e.keyCode < 47 || e.keyCode > 57)
					e.preventDefault();

				let value = $(this).val();
				let len = value.length;

				if (len !== 1 || len !== 3) {
					if (e.keyCode == 47)
						e.preventDefault();
				}

				if (len === 2) value += '/';
				if (len === 5) value += '/';

				$(this).val(value);
			});
		}

		//$("div[id*='fitem_id_summary_editor']").appendTo($formattedForm.find(".field-summary_editor-container"));

		//setTimeout(function(){
			//$modalBody.find("[id*='fitem_id_summary_editor']").each(function(){
			//	$(this).next('.fitem').addBack().addClass("col-md-6 pl-0").wrapAll('<div class="d-flex align-items-center"/>');
			//});
		//}, 100);
	}

	function formLoaded(modalForm, form){
		check_modal_loaded(modalForm, form);
	}

	function check_modal_loaded(modalForm, form){
		let $modalBody = modalForm.modal.body;

		if(!$modalBody.find("form").length){
			setTimeout(function(){
				check_modal_loaded(modalForm, form);
			},100);
		}else{
			setTimeout(function(){
				format_form(modalForm, form);
			}, 100);
			return;
		}
	}
});
{{/js}}
