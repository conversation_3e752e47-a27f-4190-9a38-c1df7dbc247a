<?php

namespace block_externalhistory\models;

use block_externalhistory\forms\externalhistory_form;
use core\persistent;
use context_user;
use stored_file;
use file_exception;
use coding_exception;
use moodle_exception;
use moodle_url;
use templatable;
use renderer_base;

defined('MOODLE_INTERNAL') || die();

class external_history extends persistent implements templatable {

    public const TABLE = 'block_externalhistory';

    protected static function define_properties(): array {
        return [
            'userid' => [
                'type' => PARAM_INT,
            ],
            'solutionname' => [
                'type'    => PARAM_TEXT,
                'default' => '',
            ],
            'format' => [
                'type'    => PARAM_TEXT,
                'default' => '',
            ],
            'description' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
            ],
            'hourscompleted' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
            ],
            'startdate' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
            ],
            'enddate' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
            ],
            'institution' => [
                'type'    => PARAM_TEXT,
                'default' => '',
            ],
            'status' => [
                'type'    => PARAM_INT,
                'default' => 1,
            ],
        ];
    }

    /**
     * Returns parameters for the certificate file area.
     *
     * @return array
     * @throws coding_exception if record has no ID yet
     */
    public function get_certificate_file_parameters(): array {
        if (!$this->get('id')) {
            throw new coding_exception('get_certificate_file_parameters can only be called on an existing record');
        }
        $context = context_user::instance($this->get('userid'));
        return [
            'contextid' => $context->id,
            'component' => 'block_externalhistory',
            'filearea'  => 'certfile',
            'itemid'    => $this->get('id'),
            'filepath'  => '/',
        ];
    }

    /**
     * Returns file manager options for uploading a certificate.
     *
     * @param context_user $context
     * @return array
     */
    public function get_file_options(context_user $context): array {
        return [
            'subdirs'      => false,
            'maxbytes'     => 0,
            'maxfiles'     => 1,
            'context'      => $context,
        ];
    }

    /**
     * Returns the certificate file, if it exists.
     *
     * @return stored_file|null
     * @throws coding_exception
     */
    public function get_certificate_file(): ?stored_file {
        $fs     = get_file_storage();
        $params = $this->get_certificate_file_parameters();
        $files  = $fs->get_area_files(
            $params['contextid'],
            $params['component'],
            $params['filearea'],
            $params['itemid'],
            'sortorder',
            false
        );
        foreach ($files as $file) {
            if (!$file->is_directory()) {
                return $file;
            }
        }
        return null;
    }

    /**
     * Returns a pluginfile URL for the certificate, or null if none.
     *
     * @return moodle_url|null
     * @throws coding_exception
     */
    public function get_certificate_file_url(): ?moodle_url {
        $file = $this->get_certificate_file();
        if ($file) {
            return moodle_url::make_pluginfile_url(
                $file->get_contextid(),
                $file->get_component(),
                $file->get_filearea(),
                $file->get_itemid(),
                $file->get_filepath(),
                $file->get_filename(),
                false
            );
        }
        return null;
    }

    /**
     * Saves the certificate file from a draft area, merging and removing old files automatically.
     *
     * @param int $draftitemid
     * @throws file_exception
     * @throws coding_exception
     */
    public function save_certificate_file_from_draft(int $draftitemid){
        $params  = $this->get_certificate_file_parameters();
        $context = context_user::instance($this->get('userid'));

        file_save_draft_area_files(
            $draftitemid,
            $params['contextid'],
            $params['component'],
            $params['filearea'],
            $params['itemid'],
            $this->get_file_options($context)
        );
    }

    /**
     * Saves the certificate file from a server filepath, clearing previous files first.
     *
     * @param string $filepath
     * @return stored_file
     * @throws moodle_exception if file does not exist
     * @throws file_exception
     * @throws coding_exception
     */
    public function save_certificate_file_from_filepath(string $filepath): stored_file {
        if (!file_exists($filepath)) {
            throw new moodle_exception('exception:file_not_found', 'block_externalhistory');
        }

        // Remove all existing files in certificate area.
        $fs     = get_file_storage();
        $params = $this->get_certificate_file_parameters();
        $fs->delete_area_files(
            $params['contextid'],
            $params['component'],
            $params['filearea'],
            $params['itemid']
        );

        // Create new file from given path.
        $params['filename'] = basename($filepath);
        return $fs->create_file_from_pathname($params, $filepath);
    }

    /**
     * Returns a list of external_history records for a user.
     *
     * @param int      $userid
     * @param int|null $limit  Maximum number of records, or null for no limit
     * @return static[] Array of external_history instances
     */
    public static function list_user_histories(int $userid, ?int $limit = null): array {
        return static::get_records(
            ['userid' => $userid],
            'id',
            'DESC',
            0,
            $limit
        );
    }

    /**
     * Export data for Mustache template.
     *
     * @param renderer_base $output
     * @return object Template data
     */
    public function export_for_template(renderer_base $output): object {
        $edit_url = new moodle_url(
            '/blocks/externalhistory/edit.php',
            ['id' => $this->get('id')]
        );
        
        $template = [
            'id'   => $this->get('id'),
            'solutionname'   => $this->get('solutionname'),
            'description'    => $this->get('description'),
            'institution'    => $this->get('institution'),
            'startdate'      => userdate($this->get('startdate')),
            'enddate'        => userdate($this->get('enddate')),
            'hourscompleted' => format_time($this->get('hourscompleted')),
            'urlpdf'         => $this->get_certificate_file_url()->out(),
            'bg' => $output->get_generated_image_for_id($this->get('id')),
            'headerlink'     =>$edit_url->out(),
        ];

        $template['json'] = json_encode($template);
        return (object) $template;
    }


    /**
     * Handles creation or update from form data.
     *
     * @param externalhistory_form $form
     * @return static
     * @throws coding_exception
     * @throws file_exception
     */
    public static function from_externalhistory_form(externalhistory_form $form): static {
        $data = $form->get_data();

        if (!empty($data->id)) {
            $record = static::get_record(['id' => $data->id], MUST_EXIST);
        } else {
            $record = new static(0, (object)['userid' => $data->userid]);
        }

        $record->set('solutionname', $data->solutionname);
        $record->set('format', $data->format);
        $record->set('description', $data->description);
        $record->set('hourscompleted', (int)$data->hourscompleted);
        $record->set('startdate', (int)$data->startdate);
        $record->set('enddate', (int)$data->enddate);
        $record->set('institution', $data->institution);
        $record->save();

        if (!empty($data->certfile)) {
            $record->save_certificate_file_from_draft($data->certfile);
        }

        return $record;
    }
}
