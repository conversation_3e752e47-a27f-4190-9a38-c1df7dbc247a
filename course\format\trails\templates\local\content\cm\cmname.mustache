{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/content/cm/cmname

    Convenience mustache to displays a course module inplcae editable from the format renderer.

    Format plugins are free to implement an alternative inplace editable for activities.

    Example context (json):
    {
        "url": "#",
        "icon": "../../../pix/help.svg",
        "iconclass": "",
        "pluginname": "File",
        "textclasses": "",
        "purpose": "content",
        "modname": "resource",
        "activityname": {
            "displayvalue" : "<a href=\"#\"><PERSON><PERSON><PERSON></a>",
            "value" : "Moodle",
            "itemid" : "1",
            "component" : "core_unknown",
            "itemtype" : "unknown",
            "edithint" : "Edit this",
            "editlabel" : "New name for this",
            "type" : "text",
            "options" : "",
            "linkeverything": 0
        }
    }
}}
{{#url}}
    <div class="activity-instance d-flex flex-column">
        <div class="activitytitle media align-items-center {{textclasses}} modtype_{{modname}} position-relative">
            <div class="activityiconcontainer {{purpose}} courseicon mr-3">
                <img src="{{{icon}}}" class="activityicon {{iconclass}}" alt="{{{modname}}} icon">
            </div>
            <div class="media-body align-self-center">
                <div class="activityname font-weight-bold">
                    {{#activityname}}
                        {{$ core/inplace_editable }}
                            {{> core/inplace_editable }}
                        {{/ core/inplace_editable }}
                    {{/activityname}}
                </div>
                <div class="small">
                    {{{pluginname}}} {{#workload_activity}}• {{workload_activity}}{{/workload_activity}}
                </div>
            </div>
        </div>
    </div>
{{/url}}
