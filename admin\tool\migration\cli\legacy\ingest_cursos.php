<?php

use tool_migration\models\curso;

require_once(__DIR__ . '/config.php');

foreach (curso::get_pending_legacy_upserts($limit) as $curso) {
    try {
        $record = $curso->to_record();
        $record->courseid = $record->instanceid;

        $legacyid = \local_legacy\models\curso::upsert_from_migration_table_data($record);

        if($legacyid){
            $curso->set('legacyid', $legacyid);
            $curso->set('needs_update', 0);
            $curso->save();

            $identifier = $curso->get('source_codcurso');
            mtrace("Curso $identifier importado para o local_legacy");
        }
    } catch (\Throwable $th) {
        mtrace($th->getMessage());
    }
}