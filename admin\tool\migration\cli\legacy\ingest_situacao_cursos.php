<?php

use tool_migration\models\progresso_curso;

require_once(__DIR__ . '/config.php');

foreach (progresso_curso::get_pending_legacy_upserts($limit) as $progresso_curso) {
    try {
        $record = $progresso_curso->to_record();

        $legacyid = \local_legacy\models\situacao_curso::upsert_from_migration_table_data($record);

        if($legacyid){
            $progresso_curso->set('instanceid', $legacyid);
            $progresso_curso->set('needs_update', 0);
            $progresso_curso->save();

            $identifier = $progresso_curso->get('source_codsituacaoalunocurso');
            mtrace("Situação de usuário no curso $identifier importado para o local_legacy");
        }
    } catch (\Throwable $th) {
        mtrace($th->getMessage());
    }
}