<?php namespace tool_migration;


defined('MOODLE_INTERNAL') || die();

use tool_migration\models\curso;
use tool_migration\util\ingestion_helper;
use local_degreed_integration\degreed\entities\skill;
use local_ssystem\constants\custom_course_fields;
use text_progress_trace;
use tool_migration\importers\curso_importer;
use tool_migration\importers\trilha_importer;
use tool_migration\models\trilha;

final class trilha_importer_test extends \advanced_testcase {

    protected $data = [];

    protected function setUp(): void {
        parent::setUp();

        $this->resetAfterTest(true);

        $this->data = [
            'source_id'                    => '123',
            'source_disponivel'            => 'S',
            'source_nome'                  => 'Transformação Digital - Introdução a Transformação Digital',
            'source_status'                => '1',
            'source_prazo'                 => null,
            'source_data_de_criacao'       => '2022-07-28 15:30:06.4025959 ',
            'source_data_de_modificacao'   => '2023-04-14 17:11:50.5197574 ',
            'source_frequencia'            => '100',
            'source_media'                 => '70',
            'source_permite_rematricula'   => null,
            'source_keywords'              => 'produtividade,agente local de inovação',
            'source_carga_horaria_minima'  => '20.00',
            'source_descricao'             => '<p>Nesta trilha, os participantes conhecer&atilde;o o novo Sistema de Gest&atilde;o Estrat&eacute;gica: O LEME e os conhecimentos que embasam&nbsp;a elabora&ccedil;&atilde;o do Planejamento Estrat&eacute;gico do Sebrae, bem como, as orienta&ccedil;&otilde;es de todo o processo.&nbsp;</p>
',
            'source_tipo_solucao'          => 'Trilha de aprendizagem',
            'source_apresentacao'          => '<p>O Planejamento Estrat&eacute;gico do Sebrae &eacute; constru&iacute;do com a for&ccedil;a de todos os colaboradores, com objetivo de cumprir com o processo de transforma&ccedil;&atilde;o do Sebra e tamb&eacute;m, transformar os pequenos neg&oacute;cios em protagonistas do desenvolvimento sustent&aacute;vel do Brasil.</p>

<p>Nesta trilha, os participantes conhecer&atilde;o o novo Sistema de Gest&atilde;o Estrat&eacute;gica: O LEME e os conhecimentos que embasam&nbsp;a elabora&ccedil;&atilde;o do Planejamento Estrat&eacute;gico do Sebrae, bem como, as orienta&ccedil;&otilde;es de todo o processo de elabora&ccedil;&atilde;o do Planejamento Estrat&eacute;gico 2023 e consequentemente, a participa&ccedil;&atilde;o no processo de transforma&ccedil;&atilde;o iniciado em agosto/2019.&nbsp;</p>
',
            'source_objetivos'             => '<p>Ao final da capacita&ccedil;&atilde;o, os participantes ser&atilde;o capazes de:<br />
- Conhecer os Programas Nacionais e respectivos impactos<br />
-&nbsp;Comprender sobre planejamento Estrat&eacute;gico, t&aacute;tico e operacional<br />
- Definir indicadores<br />
- Analisar o Mapa Estratpegico 2023<br />
- Operacionalizar o Sistema LEME</p>
',
            'source_conteudo_programatico' => '<p>Carga hor&aacute;ria: 20 horas</p>

<p>Conte&uacute;do program&aacute;tico:</p>

<p>Atua&ccedil;&atilde;o do Sebrae em inova&ccedil;&atilde;o.<br />
Pequenos neg&oacute;cios no Brasil.<br />
Inova&ccedil;&atilde;o e transforma&ccedil;&atilde;o digital.<br />
Sustentabilidade para desenvolvimento dos pequenos neg&oacute;cios.<br />
A jornada ALI para a produtividade.</p>
',
            'source_nivel_de_complexidade' => '<p>Avan&ccedil;ado</p>
',
            'source_termo_de_aceite'       => '<p>Ao me inscrever nesta trilha estou de acordo com as regras de uso da plataforma da UCSebrae, incluso uso de dados e regras de participa&ccedil;&atilde;o em solu&ccedil;&otilde;es educacionais descritas no Termo de aceite da plataforma, dispon&iacute;vel em meu perfil.</p>
',
            'source_ficha_tecnica'         => '<p>Equipe:<br />
<strong>Conteudistas</strong><br />
- Adriane&nbsp;<br />
- Murilo Terra<br />
- Alexandre Ambrosini<br />
- Joana Bona<br />
- Aretha Zarlenga<br />
<strong>Coordena&ccedil;&atilde;o pedag&oacute;gica</strong><br />
- Priscylla Nunes</p>
',
            'source_requisitos'            => '<p><em><strong>Conclus&atilde;o no n&iacute;vel intermedi&aacute;rio da Trilha Jornada Centrado no Cliente</strong></em></p>
',
            'source_criterios_de_avaliacao'=> '<p>teste</p>
',
            'source_publico'               => '<p>Analistas e Assistentes do Sistema Sebrae</p>
',
            'source_area_subarea'          => null,
        ];

    }


    /**
     * @group current
     */
    function test_trail_creation(){
        global $DB;

        $coursecat = $this->getDataGenerator()->create_category();

        $curso = new trilha(0, (object)$this->data);
        $curso->save();

        $importer = new trilha_importer();
        $importer->set_category($coursecat->id);
        $course = $importer->upsert_course($curso);

        $newcourse = get_course($course->id);
        \tool_lfxp\helpers\custom_fields\course\custom_course_field::load_custom_fields($newcourse);

        $this->assertEquals(
            20*HOURSECS,
            $newcourse->customfields->{custom_course_fields::WORKLOAD}->get_value()
        );

        $this->assertEquals(
            'LEGADO-' . $this->data['source_id'], 
            $newcourse->idnumber
        );

        $this->assertStringContainsString(
            $this->data['source_nome'], 
            $newcourse->fullname
        );

        $this->assertTrue((bool)$newcourse->enablecompletion);

        $this->assertEquals(
            'Trilha de aprendizagem',
            $newcourse->customfields->{custom_course_fields::SOLUTION_FORMAT}->export_value()
        );

        $this->assertEquals(
            custom_course_fields::COMPLEXITY_ADVANCED,
            $newcourse->customfields->{custom_course_fields::COMPLEXITY_LEVEL}->export_value()
        );

        $this->assertStringContainsString(
            'conhecimentos que emba', 
            $newcourse->summary
        );

        $this->assertStringContainsString(
            'de cumprir com o processo', 
            $newcourse->customfields->{custom_course_fields::INTRODUCTION}->export_value()
        );

        $this->assertStringContainsString(
            'os participantes se', 
            $newcourse->customfields->{custom_course_fields::OBJECTIVES}->export_value()
        );

        $this->assertStringContainsString(
            'Sebrae em inova', 
            $newcourse->customfields->{custom_course_fields::CURRICULUM}->export_value()
        );

        $this->assertStringContainsString(
            'Ao me inscrever nesta trilha', 
            $newcourse->customfields->{custom_course_fields::CONSENT_TERM}->export_value()
        );

        $this->assertStringContainsString(
            'Equipe:',
            $newcourse->customfields->{custom_course_fields::TECHNICAL_SHEET}->export_value()
        );

        $this->assertStringContainsString(
            'Trilha Jornada Centrado',
            $newcourse->customfields->{custom_course_fields::REQUIREMENTS}->export_value()
        );

        $this->assertStringContainsString(
            'teste',
            $newcourse->customfields->{custom_course_fields::EVALUATION_CRITERIA}->export_value()
        );

        $this->assertStringContainsString(
            'Assistentes do Sistema',
            $newcourse->customfields->{custom_course_fields::TARGET_AUDIENCE}->export_value()
        );

        $this->assertEmpty(
            $newcourse->customfields->{custom_course_fields::AREA}->export_value()
        );
    }


    /**
     * @group !current
     */
    function test_trail_update(){
        global $DB;

        $coursecat = $this->getDataGenerator()->create_category();

        $original_course = $this->getDataGenerator()->create_course([
            'category' => $coursecat->id,
        ]);
        
        $data = (object)$this->data;
        $data->instanceid = $original_course->id;

        $curso = new trilha(0, $data);
        $curso->save();

        $importer = new trilha_importer();
        $importer->set_category($coursecat->id);
        $course = $importer->upsert_course($curso);

        $newcourse = get_course($course->id);
        \tool_lfxp\helpers\custom_fields\course\custom_course_field::load_custom_fields($newcourse);

        $this->assertEquals(
            20*HOURSECS,
            $newcourse->customfields->{custom_course_fields::WORKLOAD}->get_value()
        );

        $this->assertEquals(
            'LEGADO-' . $this->data['source_id'], 
            $newcourse->idnumber
        );

        $this->assertStringContainsString(
            $this->data['source_nome'], 
            $newcourse->fullname
        );

        $this->assertTrue((bool)$newcourse->enablecompletion);

        $this->assertEquals(
            'Trilha de aprendizagem',
            $newcourse->customfields->{custom_course_fields::SOLUTION_FORMAT}->export_value()
        );

        $this->assertEquals(
            custom_course_fields::COMPLEXITY_ADVANCED,
            $newcourse->customfields->{custom_course_fields::COMPLEXITY_LEVEL}->export_value()
        );

        $this->assertStringContainsString(
            'conhecimentos que emba', 
            $newcourse->summary
        );

        $this->assertStringContainsString(
            'de cumprir com o processo', 
            $newcourse->customfields->{custom_course_fields::INTRODUCTION}->export_value()
        );

        $this->assertStringContainsString(
            'os participantes se', 
            $newcourse->customfields->{custom_course_fields::OBJECTIVES}->export_value()
        );

        $this->assertStringContainsString(
            'Sebrae em inova', 
            $newcourse->customfields->{custom_course_fields::CURRICULUM}->export_value()
        );

        $this->assertStringContainsString(
            'Ao me inscrever nesta trilha', 
            $newcourse->customfields->{custom_course_fields::CONSENT_TERM}->export_value()
        );

        $this->assertStringContainsString(
            'Equipe:',
            $newcourse->customfields->{custom_course_fields::TECHNICAL_SHEET}->export_value()
        );

        $this->assertStringContainsString(
            'Trilha Jornada Centrado',
            $newcourse->customfields->{custom_course_fields::REQUIREMENTS}->export_value()
        );

        $this->assertStringContainsString(
            'teste',
            $newcourse->customfields->{custom_course_fields::EVALUATION_CRITERIA}->export_value()
        );

        $this->assertStringContainsString(
            'Assistentes do Sistema',
            $newcourse->customfields->{custom_course_fields::TARGET_AUDIENCE}->export_value()
        );

        $this->assertEmpty(
            $newcourse->customfields->{custom_course_fields::AREA}->export_value()
        );
    }
}
